using Raiconet.Cotizador.Api.Configuration;
using Serilog;
using ILogger = Microsoft.Extensions.Logging.ILogger;

var builder = WebApplication.CreateBuilder(args);
builder.AddInfisicalConfigurationProvider();

builder.Services
    .AddRaiconetDatabase(builder.Configuration)
    .AddResend(builder.Configuration)
    .AddRaiconetEndpoints(builder.Configuration, builder.Environment)
    .AddRaiconetServices(builder.Configuration);

builder.Services.AddTransient<ILogger>(p =>
{
    var loggerFactory = p.GetRequiredService<ILoggerFactory>();
    return loggerFactory.CreateLogger("Raiconet");
});

builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

var app = builder.Build();

app.UseHttpsRedirection();
app.UseSerilogRequestLogging();
app.UseRaiconetEndpoints();
await app.ApplyMigrations();
app.Lifetime.ApplicationStopped.Register(Log.CloseAndFlush);

app.Run();
