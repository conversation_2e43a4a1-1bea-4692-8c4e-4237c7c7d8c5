using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using Raiconet.Cotizador.Api.Services.Quotations;

namespace Raiconet.Cotizador.Api.Services.Quotation;

public interface IQuotationCalculator
{
    Task<QuotationCalculationResult> CalculateQuotation(
        int customerId,
        int serviceTypeId,
        int packageType,
        decimal totalPackageValue,
        List<GetImportQuotation.PackageRequest> packages,
        SetupZones setupZone,
        List<AdditionalService>? additionalServices,
        decimal dutyPercentage,
        decimal? extraCharge,
        int? tvhType = null,
        int? pickupZoneId = null,  // New parameter for warehouse pickup zone
        string? warehouseCountry = null,
        bool? includePickup = null
        );
}