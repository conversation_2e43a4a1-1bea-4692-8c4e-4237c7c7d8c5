using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using Raiconet.Cotizador.Api.Services.Quotations;

namespace Raiconet.Cotizador.Api.Services.Quotation;

public interface IRicQuotationCalculator
{
    Task<QuotationCalculationResult> CalculateRicQuotation(
        int customerId,
        int serviceTypeId,
        int packageType,
        decimal totalPackageValue,
        List<GetRicImportQuotation.PackageRequest> packages,
        SetupZones setupZone,
        List<AdditionalService>? additionalServices,
        decimal dutyPercentage,
        decimal? extraCharge,
        bool isPrepaid,
        bool includeTaxes,
        int? tvhType = null,
        int? pickupZoneId = null,
        string? warehouseCountry = null,
        bool? includePickup = null
    );
}
