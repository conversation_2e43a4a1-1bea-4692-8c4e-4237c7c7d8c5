using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using System.Reflection;
using System.Text.Json.Serialization;
using Raiconet.Cotizador.Api.Services.Quotation;

namespace Raiconet.Cotizador.Api.Services.Quotations;

public class QuotationCalculatorExport : IQuotationCalculatorExport
{
  private readonly ILogger<QuotationCalculatorExport> _logger;
  private readonly AppDbContext _dbContext;

  public QuotationCalculatorExport(
      ILogger<QuotationCalculatorExport> logger,
      AppDbContext dbContext
  )
  {
    _logger = logger;
    _dbContext = dbContext;
  }

  // Función para redondear el peso al 0.5 más cercano hacia arriba
  private decimal RoundWeightToNearestHalf(decimal weight)
  {
    // Obtener la parte decimal
    decimal decimalPart = weight - Math.Floor(weight);
    
    // Aplicar la regla de redondeo
    if (decimalPart <= 0.0m)
        return Math.Floor(weight);
    else if (decimalPart <= 0.5m)
        return Math.Floor(weight) + 0.5m;
    else
        return Math.Floor(weight) + 1.0m;
  }

  public async Task<QuotationCalculationResult> CalculateQuotationExport(
      int customerId,
      int serviceTypeId,
      int packageType,
      decimal totalPackageValue,
      bool includeInsurance ,
      List<GetImportQuotation.PackageRequest> packages,
      SetupZones setupZone,
      List<AdditionalService>? additionalServices,
      decimal dutyPercentage
  )
  {
    try
    {
      var country = setupZone.Country?.Trim().ToUpper();
      var iataCode = setupZone.IataCode?.Trim().ToUpper();
      _logger.LogInformation($"País destino buscado (normalizado): {country}");

      if (string.IsNullOrEmpty(country))
      {
        _logger.LogError("El país es nulo o vacío.");
        return new QuotationCalculationResult
        {
          Success = false,
          Error = "El país es nulo o vacío"
        };
      }

      var upsZones = await _dbContext.UpsZonesExports.FirstOrDefaultAsync(
          c => c.IataCode != null && c.IataCode.Trim().ToUpper() == iataCode
      );

      var fedexZones = await _dbContext.FedexZonesExports.FirstOrDefaultAsync(
          c => c.IataCode != null && c.IataCode.Trim().ToUpper() == iataCode
      );

      // Get total weight for all packages
      decimal totalWeight = packages.Sum(p => Math.Max((decimal)p.VolumetricWeight, (decimal)p.NetWeight));

      decimal additionalServicesCost = 0;
      decimal storage = 0;
      decimal terminalCharge = 0;

      // Variables específicas para exportación
      decimal exportDocumentFee = 0;
      decimal exportCustomsDeclaration = 0;
      decimal exportHandlingFee = 0;
      decimal collectFeeMin = 0;
      decimal collectFeePercentage = 0;
      
      // Configurar variables según el tipo de servicio
      if (serviceTypeId == 2) // Courier Carga (anteriormente identificado como serviceTypeId = 4)
      {
          var exportDoc = await _dbContext.AdditionalServices.Where(c => c.Code == "EXPORT_D").FirstOrDefaultAsync();
          var TasaS = await _dbContext.AdditionalServices.Where(c => c.Code == "TASA_S").FirstOrDefaultAsync();
          var CollectF = await _dbContext.AdditionalServices.Where(c => c.Code == "COLLECT_F").FirstOrDefaultAsync();

          exportDocumentFee = exportDoc?.UpsValue ?? 0;
          exportCustomsDeclaration = TasaS?.UpsValue ?? 0;
          collectFeeMin = CollectF?.MinimumValue ?? 0;
          collectFeePercentage = CollectF?.UpsValue ?? 0;
          
          // Para Courier Carga, no se calcula terminal charge
          terminalCharge = 0;
      }
      else // serviceTypeId == 1 - Courier Directo
      {
          // Calculate terminal charge based on package values para Courier Directo
          // terminalCharge = Math.Round(
          //     Math.Max(Math.Min(totalPackageValue * 0.10M, 65.00M), 20),
          //     2
          // );
          terminalCharge = 0;
      }

      // Fuel
      var fuel = await _dbContext.AdditionalServices.Where(c => c.Code == "FUEL").FirstOrDefaultAsync();
      var upsFuel = fuel?.UpsValue ?? 0;
      var fedexFuel = fuel?.FedexValue ?? 0;

      // Fetch customer discounts
      var customerDiscount = await _dbContext.CustomerBillingReason
          .Where(c => c.CustomerId == customerId)
          .Include(b => b.BillingReason)
          .ToListAsync();

      // Get billing reason zone based on country
      var billingReasonZone = await _dbContext.SetupExportZones
          .Where(c => c.Name.ToUpper() == country)
          .FirstOrDefaultAsync();

      decimal clientDiscountCourierDirecto = 0;
      decimal clientDiscountCourierCarga = 0;
      

      var cifRate = customerDiscount.Count > 0 ? customerDiscount.FirstOrDefault().BillingReason.CifFreightRate:0;
      
      // Calcular CifFreight - peso total del envío multiplicado por cifRate
      decimal cifFreight = cifRate is null? 0: Math.Round(totalWeight * cifRate.Value, 2);
      
      // Insurance para subtotal - específico para cada tipo de servicio
      decimal insurance;
      
      if (serviceTypeId == 1) // Courier Directo
      {
          // Mínimo 10 USD o 1% del valor del paquete
          decimal calculatedInsurance = Math.Round(totalPackageValue * 0.01M, 2);
          insurance = includeInsurance == true ? Math.Max(10M, calculatedInsurance):0;
      }
      else // Courier Carga
      {
          insurance = includeInsurance == true ? Math.Round(totalPackageValue * 0.01M, 2):0;
          
      }
      
      // Insurance para CIF - 1% del (valor del paquete + cifFreight)
      decimal cifInsurance = Math.Round((totalPackageValue + cifFreight) * 0.01M, 2);
      
      if (billingReasonZone != null)
      {
        // Customer discounts
        clientDiscountCourierDirecto = customerDiscount
            .FirstOrDefault(c => c.BillingReason.Code == billingReasonZone.QuotationsDocEconomy)
            ?.Percentage ?? 0;
        
        clientDiscountCourierCarga = customerDiscount
          .FirstOrDefault(c => c.BillingReason.Code == billingReasonZone.QuotationsDocPriority)
          ?.Percentage ?? 0;
      }
      else
      {
        _logger.LogWarning($"No se encontró SetupExportZones para el país: {country}");
      }

      // Calculate rates for each package
      var packageRates = new List<PackageRate>();
      
      foreach (var package in packages)
      {
        // Use the maximum of NetWeight and VolumetricWeight
        decimal chargeableWeight = Math.Max(package.NetWeight, package.VolumetricWeight);

        int upsPackageType = chargeableWeight > (decimal)31.5 ? 3 : packageType;
        int fedexPackageType = chargeableWeight > (decimal)20.5 ? 4 : packageType;

        List<UpsServices> upsRates = await GetUpsRates(chargeableWeight, upsPackageType);
        List<UpsServices> upsMinimumRates = await GetUpsMinimumRates(chargeableWeight, 4);
        List<FedexServices> fedexRates = await GetFedexRates(chargeableWeight, fedexPackageType);

        var upsServiceRates = new Dictionary<string, decimal>();
        for (int i = 0; i < upsRates.Count && i < upsMinimumRates.Count; i++)
        {
            var upsRate = upsRates[i];
            var upsMinimumRate = upsMinimumRates[i];

            decimal upsZoneRate = GetUpsZoneRate(upsRate, upsZones?.ExpressSaver ?? 0);
            decimal upsZoneRateMinimum = GetUpsZoneRate(upsMinimumRate, upsZones?.ExpressSaver ?? 0);
            decimal finalRate = 0;

            if (upsPackageType == 3)
            {
                finalRate = upsZoneRateMinimum + upsZoneRate * (chargeableWeight - 31.5m);
            }
            else
            {
                finalRate = upsZoneRate;
            }

            upsServiceRates[GetUpsServiceName(upsRate)] = finalRate * 3;
        }

        var fedexServiceRates = new Dictionary<string, decimal>();
        var processedServices = new Dictionary<string, bool>();

        for (int i = 0; i < fedexRates.Count; i++)
        {
          var fedexRate = fedexRates[i];
          string serviceName = GetFedexServiceName(fedexRate);
          
          if (processedServices.ContainsKey(serviceName) && processedServices[serviceName])
            continue;
          
          decimal fedexZoneRate = GetFedexZoneRate(fedexRate, fedexZones?.Ip ?? "", chargeableWeight);
          
          if (fedexZoneRate > 0)
          {
            decimal finalRate = 0;
            
            if (upsPackageType == 3)
            {
              finalRate = fedexZoneRate * chargeableWeight;
            }
            else
            {
              finalRate = fedexZoneRate;
            }
            
            fedexServiceRates[serviceName] = finalRate * 3;
            processedServices[serviceName] = true;
          }
          else
          {
            if (!processedServices.ContainsKey(serviceName))
              processedServices[serviceName] = false;
          }
        }

        packageRates.Add(
            new PackageRate
            {
              UpsServiceRates = upsServiceRates,
              FedexServiceRates = fedexServiceRates
            }
        );
      }

      // Calculate totals by service
      var totalsByService = new Dictionary<string, TotalsByService>();

      // Check if PackageTypeId = 1 (Document/Envelope)
      bool hasDocumentPackage = packageType == 1;
      _logger.LogInformation($"Document package detected in export calculation: {hasDocumentPackage} (PackageTypeId: {packageType})");
      
      foreach (var packageRate in packageRates)
      {
        foreach (var upsServiceRate in packageRate.UpsServiceRates)
        {
          if (!totalsByService.ContainsKey(upsServiceRate.Key))
          {
            totalsByService[upsServiceRate.Key] = new TotalsByService();
            totalsByService[upsServiceRate.Key].Label = upsServiceRate.Key switch
            {
              "UpsSaver" => "Raico Priority ",
              "UpsExpedited" => "Raico Economy",
              //"UpsExpress" => "Raico Priority (Directo)",
              _ => ""
            };
            totalsByService[upsServiceRate.Key].CourierLabel = upsServiceRate.Key switch
            {
              "UpsSaver" => "UPS Saver",
              "UpsExpedited" => "UPS Expedited",
              //"UpsExpress" => "UPS Express",
              _ => ""
            };
          }

          // Apply discount to the rate *BEFORE* calculating CIF
          decimal discountPercentage = serviceTypeId == 1 
              ? clientDiscountCourierDirecto 
              : clientDiscountCourierCarga;
              
          decimal discountedRate = Math.Round(upsServiceRate.Value * (1 - (discountPercentage / 100)), 2);
          decimal fuelCharge = Math.Round(discountedRate * (upsFuel / 100), 2);

          // Calculate CIF
          decimal cif = totalPackageValue + cifInsurance + cifFreight;

          // Calculate additional costs
          decimal estadistica = 0;
          decimal derecho = 0;
          decimal iva = 0;
          decimal collectFee = 0;

          // Apply document package logic if PackageTypeId = 1
          if (hasDocumentPackage)
          {
              _logger.LogInformation("Applying document package logic for UPS export service - excluding all charges except freight and fuel");
              estadistica = 0;
              derecho = 0;
              iva = 0;
              collectFee = 0;
              exportDocumentFee = 0;
              exportCustomsDeclaration = 0;
          }
          else
          {
              // Cálculos específicos para el tipo de servicio
              if (serviceTypeId == 2) // Courier Carga
              {
                  // Calcular collectFee según la regla: si el porcentaje aplicado al rate es mayor al mínimo, usar ese valor
                  decimal calculatedCollectFee = Math.Round(discountedRate * (collectFeePercentage / 100), 2);
                  collectFee = Math.Max(collectFeeMin, calculatedCollectFee);

                  // Para Courier Carga, estadistica, derecho e iva se dejan en 0
                  estadistica = 0;
                  derecho = 0;
                  iva = 0;

                  // Agregar los campos de exportación al objeto TotalsByService
                  totalsByService[upsServiceRate.Key].ExportDocument = exportDocumentFee;
                  totalsByService[upsServiceRate.Key].TasaSumaria = exportCustomsDeclaration;
                  totalsByService[upsServiceRate.Key].CollectFee = collectFee;
              }
              else // Courier Directo
              {
                  bool applyAdditionalCharges = true;

                  if (applyAdditionalCharges)
                  {
                    estadistica = 0;
                    //estadistica = Math.Round(cif * 0.03M, 2);
                    derecho = Math.Round(cif * (dutyPercentage / 100), 2);
                   // iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);
                    iva = 0;
                  }
              }
          }

          // Accumulate values in TotalsByService
          totalsByService[upsServiceRate.Key].Cif += cif;
          totalsByService[upsServiceRate.Key].Estadistica = estadistica;
          totalsByService[upsServiceRate.Key].Derecho = derecho;
          totalsByService[upsServiceRate.Key].Iva = iva;
          totalsByService[upsServiceRate.Key].SubTotal += discountedRate;
          totalsByService[upsServiceRate.Key].UpsFuel += fuelCharge;
          totalsByService[upsServiceRate.Key].Flete += discountedRate;
        }

        foreach (var fedexServiceRate in packageRate.FedexServiceRates)
        {
          if (!totalsByService.ContainsKey(fedexServiceRate.Key))
          {
            totalsByService[fedexServiceRate.Key] = new TotalsByService();
            totalsByService[fedexServiceRate.Key].Label = fedexServiceRate.Key switch
            {
              "FedexIe" => "Raico Economy",
              "FedexIp" => "Raico Priority",
              _ => ""
            };
            totalsByService[fedexServiceRate.Key].CourierLabel = fedexServiceRate.Key switch
            {
              "FedexIe" => "Fedex International Economy",
              "FedexIp" => "Fedex International Priority",
              _ => ""
            };
          }

          // Apply discount to the rate *BEFORE* calculating CIF
          decimal discountPercentage = serviceTypeId == 1 
              ? clientDiscountCourierDirecto 
              : clientDiscountCourierCarga;
              
          decimal discountedRate = Math.Round(fedexServiceRate.Value * (1 - (discountPercentage / 100)), 2);
          decimal fuelCharge = Math.Round(discountedRate * (fedexFuel / 100), 2);

          // Calculate CIF
          decimal cif = totalPackageValue + cifInsurance + cifFreight;

          // Calculate additional costs
          decimal estadistica = 0;
          decimal derecho = 0;
          decimal iva = 0;
          decimal collectFee = 0;

          // Apply document package logic if PackageTypeId = 1
          if (hasDocumentPackage)
          {
              _logger.LogInformation("Applying document package logic for FedEx export service - excluding all charges except freight and fuel");
              estadistica = 0;
              derecho = 0;
              iva = 0;
              collectFee = 0;
              exportDocumentFee = 0;
              exportCustomsDeclaration = 0;
          }
          else
          {
              // Cálculos específicos para el tipo de servicio
              if (serviceTypeId == 2) // Courier Carga
              {
                  // Calcular collectFee según la regla: si el porcentaje aplicado al rate es mayor al mínimo, usar ese valor
                  decimal calculatedCollectFee = Math.Round(discountedRate * (collectFeePercentage / 100), 2);
                  collectFee = Math.Max(collectFeeMin, calculatedCollectFee);

                  // Para Courier Carga, estadistica, derecho e iva se dejan en 0
                  estadistica = 0;
                  derecho = 0;
                  iva = 0;

                  // Agregar los campos de exportación al objeto TotalsByService
                  totalsByService[fedexServiceRate.Key].ExportDocument = exportDocumentFee;
                  totalsByService[fedexServiceRate.Key].TasaSumaria = exportCustomsDeclaration;
                  totalsByService[fedexServiceRate.Key].CollectFee = collectFee;
              }
              else // Courier Directo
              {
                  bool applyAdditionalCharges = true;

                  if (applyAdditionalCharges)
                  {
                    estadistica = 0;
                    //estadistica = Math.Round(cif * 0.03M, 2);
                    //derecho = Math.Round(cif * (dutyPercentage / 100), 2);
                    derecho = Math.Round(cif * (dutyPercentage / 100), 2);
                    //iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);
                    iva = 0;
                  }
              }
          }

          // Accumulate values in TotalsByService
          totalsByService[fedexServiceRate.Key].Cif += cif;
          totalsByService[fedexServiceRate.Key].Estadistica = estadistica;
          totalsByService[fedexServiceRate.Key].Derecho = derecho;
          totalsByService[fedexServiceRate.Key].Iva = iva;
          totalsByService[fedexServiceRate.Key].SubTotal += discountedRate;
          totalsByService[fedexServiceRate.Key].FedexFuel = fuelCharge;
          totalsByService[fedexServiceRate.Key].Flete += discountedRate;
        }
      }

      // Add insurance, storage, terminalCharge, and fuelCharge *ONCE* per service
      foreach (var service in totalsByService.Values)
      {
        // Apply document package logic if PackageTypeId = 1
        if (hasDocumentPackage)
        {
            _logger.LogInformation("Applying document package logic for export final service calculation - excluding all charges except freight and fuel");
            service.TerminalCharge = 0;
            service.Insurance = 0;
            service.Storage = 0;
            service.ExportDocument = 0;
            service.TasaSumaria = 0;
            service.CollectFee = 0;

            // For document packages, SubTotal should only include freight (Flete) and fuel (FedexFuel or UpsFuel)
            service.SubTotal = Math.Round(service.Flete + (service.FedexFuel > 0 ? service.FedexFuel : service.UpsFuel), 2);
        }
        else
        {
            service.TerminalCharge = terminalCharge;
            service.Insurance = insurance;
            service.Storage = storage;

            // Cálculo del SubTotal diferente para cada tipo de servicio
            if (serviceTypeId == 2) // Courier Carga
            {
                service.SubTotal = Math.Round(service.SubTotal + insurance + storage + service.FedexFuel +
                    service.ExportDocument + service.TasaSumaria  + service.CollectFee, 2);
            }
            else // Courier Directo
            {
                service.SubTotal = Math.Round(service.SubTotal + insurance  + service.FedexFuel, 2);
            }
        }

        service.Total = Math.Round(service.SubTotal + service.Derecho , 2);
      }

      decimal subTotalAllServices = totalsByService.Values.Sum(t => t.SubTotal);
      if (additionalServices != null && additionalServices.Any())
      {
        foreach (var additionalService in additionalServices)
        {
          additionalServicesCost += CalculateAdditionalServiceCost(
              additionalService,
              packages,
              subTotalAllServices
          );
        }
      }

      // Add additional services cost to the total
      foreach (var service in totalsByService.Values)
      {
        service.Total = Math.Round(service.Total + additionalServicesCost, 2);
      }

      return new QuotationCalculationResult
      {
        Success = true,
        PackageRates = packageRates,
        TotalsByService = totalsByService,
        AdditionalServicesCost = additionalServicesCost
      };
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error calculating export quotation");
      return new QuotationCalculationResult
      {
        Success = false,
        Error = "Error calculating export quotation"
      };
    }
  }
  
  private async Task<List<UpsServices>> GetUpsRates(decimal weight, int packageTypeId)
  {
    // Redondear el peso para buscar en las tablas
    decimal roundedWeight = RoundWeightToNearestHalf(weight);
    _logger.LogInformation($"Buscando tarifa UPS para peso: {weight}kg (redondeado a {roundedWeight}kg)");
    
    var rates = new List<UpsServices>();

    // Get rates from all three tables
    IQueryable<UpsServices> querySavers = _dbContext.UpsExportServicesSavers;
    if (packageTypeId == 3)
    {
      querySavers = querySavers.Where(r => r.UpsPackageType.Id == packageTypeId);
    }
    else
    {
      querySavers = querySavers.Include(r => r.UpsPackageType).Where(
        r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
      ).Distinct();
    }
    var savers = await querySavers.FirstOrDefaultAsync();
    rates.Add(savers);
    

    IQueryable<UpsServices> queryExpediteds = _dbContext.UpsExportServicesExpediteds;
    if (packageTypeId == 3)
    {
      queryExpediteds = queryExpediteds.Where(
        r => r.UpsPackageType.Id == packageTypeId
      );
    }
    else
    {
      queryExpediteds = queryExpediteds.Where(
        r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
      );
    }
    var expediteds = await queryExpediteds.ToListAsync();
    rates.AddRange(expediteds);
    
    // IQueryable<UpsServices> queryExpress = _dbContext.UpsExportServicesExpresses;
    // if (packageTypeId == 3)
    // {
    //   queryExpress = queryExpress.Where(
    //     r => r.UpsPackageType.Id == packageTypeId
    //   );
    // }
    // else
    // {
    //   queryExpress = queryExpress.Where(
    //     r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
    //   );
    // }
    // var express = await queryExpress.ToListAsync();
    // rates.AddRange(express);

    return rates;
  }
  
  private async Task<List<UpsServices>> GetUpsMinimumRates(decimal weight, int packageTypeId)
  {
      // Redondear el peso para buscar en las tablas
      decimal roundedWeight = RoundWeightToNearestHalf(weight);
      _logger.LogInformation($"Buscando tarifa UPS para peso: {weight}kg (redondeado a {roundedWeight}kg)");
    
      var rates = new List<UpsServices>();

      // Get rates from all three tables
      IQueryable<UpsServices> querySavers = _dbContext.UpsExportServicesSavers;
      if (packageTypeId == 4)
      {
          querySavers = querySavers.Where(r => r.UpsPackageType.Id == packageTypeId);
      }
      else
      {
          querySavers = querySavers.Where(
              r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
          );
      }
      var savers = await querySavers.ToListAsync();
      rates.AddRange(savers);

      IQueryable<UpsServices> queryExpressFreights =
          _dbContext.UpsExportServicesExpresses;
      if (packageTypeId == 4)
      {
          queryExpressFreights = queryExpressFreights.Where(
              r => r.UpsPackageType.Id == packageTypeId
          );
      }
      else
      {
          queryExpressFreights = queryExpressFreights.Where(
              r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
          );
      }
      var expressFreights = await queryExpressFreights.ToListAsync();
      rates.AddRange(expressFreights);

      IQueryable<UpsServices> queryExpediteds = _dbContext.UpsExportServicesExpediteds;
      if (packageTypeId == 4)
      {
          queryExpediteds = queryExpediteds.Where(
              r => r.UpsPackageType.Id == packageTypeId
          );
      }
      else
      {
          queryExpediteds = queryExpediteds.Where(
              r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
          );
      }
      var expediteds = await queryExpediteds.ToListAsync();
      rates.AddRange(expediteds);

      return rates;
  }
  
  private async Task<List<FedexServices>> GetFedexRates(decimal weight, int packageType)
  {
    // Redondear el peso para buscar en las tablas
    decimal roundedWeight = RoundWeightToNearestHalf(weight);
    _logger.LogInformation($"Buscando tarifa Fedex para peso: {weight}kg (redondeado a {roundedWeight}kg)");
    
    var rates = new List<FedexServices>();

    // Get rates from all four tables
    IQueryable<FedexServices> queryIps = _dbContext.FedexExportServiceIps;
    if (packageType == 4)
    {
      queryIps = queryIps.Where(r => r.FedexPackageType.Id == packageType);
    }
    else
    {
      queryIps = queryIps.Where(
          r => r.FedexPackageType.Id == packageType && r.Weight == roundedWeight
      );
    }
    var ips = await queryIps.ToListAsync();
    rates.AddRange(ips);

    IQueryable<FedexServices> queryIes = _dbContext.FedexExportServicesIes;
    if (packageType == 4)
    {
      queryIes = queryIes.Where(r => r.FedexPackageType.Id == packageType);
    }
    else
    {
      queryIes = queryIes.Where(
          r => r.FedexPackageType.Id == packageType && r.Weight == roundedWeight
      );
    }
    var ies = await queryIes.ToListAsync();
    rates.AddRange(ies);

    return rates;
  }

  private decimal GetUpsZoneRate(UpsServices upsService, int zone)
  {
    if (upsService == null)
    {
      return 0;
    }

    string zonePropertyName = $"Zone{zone}";
    PropertyInfo propertyInfo = typeof(UpsServices).GetProperty(zonePropertyName);

    if (propertyInfo == null)
    {
      _logger.LogError($"Zone property '{zonePropertyName}' not found on UpsServices.");
      return 0;
    }

    object value = propertyInfo.GetValue(upsService);

    if (value == null)
    {
      _logger.LogWarning($"Zone property '{zonePropertyName}' is null.");
      return 0;
    }

    return Convert.ToDecimal(value);
  }

  private decimal GetFedexZoneRate(FedexServices fedexService, string zone, decimal weight)
  {
    if (fedexService == null)
    {
      return 0;
    }

    decimal rate = 0;

    // Determine the correct zone property based on the zone string
    string zonePropertyName = $"Zone{zone.ToUpper()}";
    // Get the property info for the zone
    PropertyInfo zoneProperty = typeof(FedexServices).GetProperty(zonePropertyName);

    if (zoneProperty == null)
    {
      _logger.LogError($"Zone property '{zonePropertyName}' not found on FedexServices.");
      return 0;
    }

    // Get the value of the zone property
    object zoneValue = zoneProperty.GetValue(fedexService);

    if (zoneValue == null)
    {
      _logger.LogWarning($"Zone property '{zonePropertyName}' is null.");
      return 0;
    }

    // Convert the zone value to decimal
    decimal zoneRate = Convert.ToDecimal(zoneValue);

    // Check if weight_range is not null and calculate rate based on weight
    if (!string.IsNullOrEmpty(fedexService.WeightRange))
    {
      // Extract the lower and upper bounds from the weight_range string
      string weightRange = fedexService.WeightRange;
      string[] ranges = weightRange.Split(" - ");

      if (
          ranges.Length == 2 &&
          decimal.TryParse(ranges[0], out decimal lowerBound) &&
          decimal.TryParse(ranges[1], out decimal upperBound)
      )
      {
        // Check if the weight falls within the range
        if (weight >= lowerBound && weight <= upperBound)
        {
          rate = zoneRate * weight; // Calculate the rate based on weight
        }
      }
      else
      {
        _logger.LogError($"Invalid weight_range format: {fedexService.WeightRange}");
      }
    }
    else
    {
      rate = zoneRate; // If no weight range, use the zone rate directly
    }

    return rate;
  }

  private string GetUpsServiceName(UpsServices upsService)
  {
    if (upsService is UpsExportServicesSaver)
      return "UpsSaver";
    if (upsService is UpsExportServicesExpedited)
      return "UpsExpedited";
    // if (upsService is UpsExportServicesExpress)
    //   return "UpsExpress";

    return "UnknownUpsService";
  }

  private string GetFedexServiceName(FedexServices fedexService)
  {
    if (fedexService is FedexExportServiceIp)
      return "FedexIp";
    if (fedexService is FedexExportServiceIef)
      return "FedexIef";
    if (fedexService is FedexExportServicesIe)
      return "FedexIe";
    if (fedexService is FedexExportServicesIpe)
      return "FedexIpe";
    if (fedexService is FedexExportServiceIpf)
      return "FedexIp";
    return "UnknownFedexService";
  }

  private static decimal CalculateAdditionalServiceCost(
      AdditionalService service,
      List<GetImportQuotation.PackageRequest> packages,
      decimal subTotal
  )
  {
    var totalValue = packages.Sum(p => p.PackageValue);

    if (service.MinimumValue.HasValue && totalValue < service.MinimumValue.Value)
      return service.UpsValue;

    return subTotal * (service.UpsValue / 100);
  }
}