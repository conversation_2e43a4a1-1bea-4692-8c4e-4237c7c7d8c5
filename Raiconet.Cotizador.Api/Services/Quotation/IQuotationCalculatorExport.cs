using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using Raiconet.Cotizador.Api.Services.Quotations;

namespace Raiconet.Cotizador.Api.Services.Quotation;

public interface IQuotationCalculatorExport
{
    Task<QuotationCalculationResult> CalculateQuotationExport(
        int customerId,
        int serviceTypeId,
        int packageType,
        decimal totalPackageValue,
        bool includeInsurance,
        List<GetImportQuotation.PackageRequest> packages,
        SetupZones setupZone,
        List<AdditionalService>? additionalServices,
        decimal dutyPercentage
    );
}

