using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using Raiconet.Cotizador.Api.Services.Quotations;

namespace Raiconet.Cotizador.Api.Services.Quotation;

public class RicQuotationCalculator : IRicQuotationCalculator
{
    private readonly IQuotationCalculator _quotationCalculator;
    private readonly ILogger _logger;

    public RicQuotationCalculator(IQuotationCalculator quotationCalculator, ILogger logger)
    {
        _quotationCalculator = quotationCalculator;
        _logger = logger;
    }

    public async Task<QuotationCalculationResult> CalculateRicQuotation(
        int customerId,
        int serviceTypeId,
        int packageType,
        decimal totalPackageValue,
        List<GetRicImportQuotation.PackageRequest> ricPackages,
        SetupZones setupZone,
        List<AdditionalService>? additionalServices,
        decimal dutyPercentage,
        decimal? extraCharge,
        bool isPrepaid,
        bool includeTaxes,
        int? tvhType = null,
        int? pickupZoneId = null,
        string? warehouseCountry = null,
        bool? includePickup = null
    )
    {
        try
        {
            _logger.LogInformation("Starting RIC quotation calculation with IsPrepaid={IsPrepaid}, IncludeTaxes={IncludeTaxes}",
                isPrepaid, includeTaxes);

            // Convert RIC packages to standard packages for the existing calculator
            var standardPackages = ricPackages.Select(p => new GetImportQuotation.PackageRequest
            {
                PackageValue = p.PackageValue,
                PackageTypeId = p.PackageTypeId,
                VolumetricWeight = p.VolumetricWeight,
                NetWeight = p.NetWeight
            }).ToList();
            // Use the existing quotation calculator to get the base calculation
            var baseResult = await _quotationCalculator.CalculateQuotation(
                customerId,
                serviceTypeId,
                packageType,
                totalPackageValue,
                standardPackages,
                setupZone,
                additionalServices,
                dutyPercentage,
                extraCharge,
                tvhType,
                pickupZoneId,
                warehouseCountry,
                includePickup
            );

            if (!baseResult.Success)
            {
                return baseResult;
            }

            // Apply RIC-specific conditional logic to the results
            var ricTotalsByService = ApplyRicConditionalLogic(baseResult.TotalsByService, isPrepaid, includeTaxes);

            _logger.LogInformation("RIC quotation calculation completed successfully");

            return new QuotationCalculationResult
            {
                Success = true,
                PackageRates = baseResult.PackageRates,
                TotalsByService = ricTotalsByService,
                AdditionalServicesCost = baseResult.AdditionalServicesCost
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RIC quotation calculation");
            return new QuotationCalculationResult
            {
                Success = false,
                Error = $"Error en el cálculo: {ex.Message}"
            };
        }
    }

    private Dictionary<string, TotalsByService> ApplyRicConditionalLogic(
        Dictionary<string, TotalsByService> originalTotals,
        bool isPrepaid,
        bool includeTaxes)
    {
        var ricTotals = new Dictionary<string, TotalsByService>();

        foreach (var kvp in originalTotals)
        {
            var serviceName = kvp.Key;
            var originalService = kvp.Value;

            // Create a copy of the original service
            var ricService = new TotalsByService
            {
                Label = originalService.Label,
                CourierLabel = originalService.CourierLabel,
                Flete = originalService.Flete,
                Insurance = originalService.Insurance,
                ExtraCharge = originalService.ExtraCharge
            };

            // Apply RIC conditional logic

            // 1. Fuel charge - only include if prepaid
            if (isPrepaid)
            {
                ricService.UpsFuel = originalService.UpsFuel;
            }
            else
            {
                ricService.UpsFuel = 0;
            }

            // 2. Storage - only include if NOT prepaid
            if (!isPrepaid)
            {
                ricService.Storage = originalService.Storage;
            }
            else
            {
                ricService.Storage = 0;
            }

            // 3. Terminal charges and taxes - only include if includeTaxes is true
            if (includeTaxes)
            {
                ricService.TerminalCharge = originalService.TerminalCharge;
                ricService.Estadistica = originalService.Estadistica;
                ricService.Derecho = originalService.Derecho;
                ricService.Iva = originalService.Iva;
            }
            else
            {
                ricService.TerminalCharge = 0;
                ricService.Estadistica = 0;
                ricService.Derecho = 0;
                ricService.Iva = 0;
            }

            // Recalculate subtotal and total based on RIC logic
            CalculateRicTotals(ricService, isPrepaid, includeTaxes);

            ricTotals[serviceName] = ricService;
        }

        return ricTotals;
    }

    private void CalculateRicTotals(TotalsByService service, bool isPrepaid, bool includeTaxes)
    {
        // Base costs that are always included: Freight + Insurance
        decimal baseSubTotal = service.Flete + service.Insurance;

        // Add fuel charge only if prepaid
        if (isPrepaid)
        {
            baseSubTotal += service.UpsFuel;
        }

        // Add storage only if NOT prepaid
        if (!isPrepaid)
        {
            baseSubTotal += service.Storage;
        }

        // Add terminal charge only if includeTaxes is true
        if (includeTaxes)
        {
            baseSubTotal += service.TerminalCharge;
        }

        // Always add extra charge
        baseSubTotal += service.ExtraCharge;

        service.SubTotal = Math.Round(baseSubTotal, 2);

        // Add taxes only if includeTaxes is true
        decimal totalTaxes = 0;
        if (includeTaxes)
        {
            totalTaxes = service.Estadistica + service.Derecho + service.Iva;
        }

        service.Total = Math.Round(service.SubTotal + totalTaxes, 2);
    }
}
