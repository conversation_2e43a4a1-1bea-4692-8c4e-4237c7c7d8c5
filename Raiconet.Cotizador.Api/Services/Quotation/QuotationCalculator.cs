using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using System.Reflection;
using System.Text.Json.Serialization;
using Raiconet.Cotizador.Api.Services.Quotation;

namespace Raiconet.Cotizador.Api.Services.Quotations;

public class QuotationCalculator : IQuotationCalculator
{
  private readonly ILogger<QuotationCalculator> _logger;
  private readonly AppDbContext _dbContext;

  public QuotationCalculator(
      ILogger<QuotationCalculator> logger,
      AppDbContext dbContext
  )
  {
    _logger = logger;
    _dbContext = dbContext;
  }

  // Función para redondear el peso al 0.5 más cercano hacia arriba
  private decimal RoundWeightToNearestHalf(decimal weight)
  {
    // Obtener la parte decimal
    decimal decimalPart = weight - Math.Floor(weight);
    
    // Aplicar la regla de redondeo
    if (decimalPart <= 0.0m)
        return Math.Floor(weight);
    else if (decimalPart <= 0.5m)
        return Math.Floor(weight) + 0.5m;
    else
        return Math.Floor(weight) + 1.0m;
  }

  public async Task<QuotationCalculationResult> CalculateQuotation(
      int customerId,
      int serviceTypeId,
      int packageType,
      decimal totalPackageValue,
      List<GetImportQuotation.PackageRequest> pack,
      SetupZones setupZone,
      List<AdditionalService>? additionalServices,
      decimal dutyPercentage,
      decimal? extraCharge,
      int? tvhType = null,  // 1 = prepaid, 2 = collect
      int? pickupZoneId = null,  // New parameter for warehouse pickup zone
      string? warehouseCountry = null,  // New parameter for warehouse country (China, Madrid, Miami)
      bool? includePickup = null  // New parameter to decide if pickup should be included
  )
  {
    try
    { 
        var packages = SumAllPackages(pack);
        
        decimal customerExtraCharge = extraCharge??0;
        var customer = await _dbContext.Customers.FirstOrDefaultAsync(c => c.Id == customerId);
        var customerComission = customer.ComissionFee;
      var country = setupZone.Country?.Trim().ToUpper();
      var iataCode = setupZone.IataCode?.Trim().ToUpper();
      _logger.LogInformation($"País buscado (normalizado): {country}");

      if (string.IsNullOrEmpty(country))
      {
        _logger.LogError("El país es nulo o vacío.");
        return new QuotationCalculationResult
        {
          Success = false,
          Error = "El país es nulo o vacío"
        };
      }

      var upsZones = await _dbContext.UpsZonesImports.FirstOrDefaultAsync(
          c => c.IataCode != null && c.IataCode.Trim().ToUpper() == iataCode
      );

      var fedexZones = await _dbContext.FedexZonesImports.FirstOrDefaultAsync(
          c => c.IataCode != null && c.IataCode.Trim().ToUpper() == iataCode
      );

      // Get total weight for all packages
      //decimal totalWeight = packages.Sum(p => (decimal)p.NetWeight);

      // Calculate total volumetric and net weights for conditional logic
      decimal totalVolumetricWeight = packages.Sum(p => (decimal)p.VolumetricWeight);
      decimal totalNetWeight = packages.Sum(p => (decimal)p.NetWeight);

      // Apply new conditional logic for specific warehouse scenario
      decimal totalWeight;
      if (serviceTypeId == 3 &&
          includePickup == true &&
          warehouseCountry == "Madrid" &&
          totalVolumetricWeight > totalNetWeight)
      {
          // Apply 1.25 multiplier to VolumetricWeight and use in calculation
          decimal adjustedVolumetricWeight = totalVolumetricWeight * 1.25m;
          decimal roundedadjustedVolumetricWeight = RoundWeightToNearestHalf(adjustedVolumetricWeight);
          totalWeight = Math.Max(roundedadjustedVolumetricWeight, totalNetWeight);
          _logger.LogInformation($"Applied Madrid warehouse special logic: VolumetricWeight {totalVolumetricWeight}kg * 1.25 = {adjustedVolumetricWeight}kg, NetWeight {totalNetWeight}kg, Final totalWeight: {totalWeight}kg");
      }
      else
      {
          // Use existing logic - maximum between VolumetricWeight and NetWeight for each package
          totalWeight = packages.Sum(p => Math.Max((decimal)p.VolumetricWeight, (decimal)p.NetWeight));
          _logger.LogInformation($"Using standard weight calculation logic: totalWeight = {totalWeight}kg");
      }

      decimal totaNetlWeight = packages.Sum(p => Math.Max(0, (decimal)p.NetWeight));

      // Verificar parámetros para Warehouse (serviceTypeId = 3)
      decimal warehouseRate = 0;
      decimal pickupRate = 0;
      string warehouseLabel = "";
      
      if (serviceTypeId == 3)
      {
        // Validar parámetros requeridos para Warehouse
        if (!pickupZoneId.HasValue)
        {
          _logger.LogError("Warehouse service selected but pickupZoneId not provided");
          return new QuotationCalculationResult
          {
            Success = false,
            Error = "Warehouse service selected but pickupZoneId not provided"
          };
        }

        if (string.IsNullOrEmpty(warehouseCountry))
        {
          _logger.LogError("Warehouse service selected but warehouseCountry not provided");
          return new QuotationCalculationResult
          {
            Success = false,
            Error = "Warehouse service selected but warehouseCountry not provided (China, Madrid, Miami)"
          };
        }
        
        // Obtener zona de pickup
        var pickupZone = await _dbContext.ImportZonesPickUps.FirstOrDefaultAsync(c => c.Id == pickupZoneId);
        if (pickupZone == null)
        {
          _logger.LogError($"Pickup zone not found for ID: {pickupZoneId}");
          return new QuotationCalculationResult
          {
            Success = false,
            Error = $"Pickup zone not found for ID: {pickupZoneId}"
          };
        }
        
        // Obtener tarifa de pickup basada en la zona
        var pickupRateInfo = await _dbContext.ImportPickupRates.FirstOrDefaultAsync(c => c.Zone == pickupZone.Zone && c.Weight == totalWeight);
        if (pickupRateInfo == null)
        {
          _logger.LogWarning($"No pickup rate found for zone: {pickupZone.Zone}");
        }
        else
        {
          // Solo asignar pickupRate si includePickup es true o no se especificó
          if (includePickup == true || includePickup == null)
          {
            pickupRate = pickupRateInfo.Price;
            _logger.LogInformation($"Pickup rate found: ${pickupRate} for zone {pickupZone.Zone}");
          }
          else if (includePickup == false || includePickup == null)
          {
            pickupRate = 0;
            _logger.LogInformation($"Pickup rate set to 0 because includePickup is false");
          }
        }

        // Normalizar nombre del país de warehouse
        string normalizedWarehouseCountry = warehouseCountry.Trim().ToUpper();
        
        // Redondear el peso total para buscar en las tablas
        decimal roundedTotalWeight = RoundWeightToNearestHalf(totalWeight);
        _logger.LogInformation($"Peso total para Warehouse: {totalWeight}kg (redondeado a {roundedTotalWeight}kg)");
        
        // Obtener tarifa de warehouse según el país y peso
        switch (normalizedWarehouseCountry)
        {
          case "CHINA":
            var warehouseZoneChina = await _dbContext.ImportWarehouseChinas
              .Where(w => w.Weight <= roundedTotalWeight)
              .OrderByDescending(w => w.Weight)
              .FirstOrDefaultAsync();
              
            if (warehouseZoneChina != null)
            {
              warehouseRate = warehouseZoneChina.Price;
              warehouseLabel = "Warehouse China";
              _logger.LogInformation($"Warehouse China rate found: ${warehouseRate} for weight {warehouseZoneChina.Weight}kg");
            }
            else
            {
              _logger.LogWarning($"No Warehouse China rate found for weight: {roundedTotalWeight}kg");
            }
            break;
            
          case "MADRID":
            var warehouseZoneMadrid = await _dbContext.ImportWarehouseMadrids
              .Where(w => w.Weight <= roundedTotalWeight)
              .OrderByDescending(w => w.Weight)
              .FirstOrDefaultAsync();
              
            if (warehouseZoneMadrid != null)
            {
              warehouseRate = warehouseZoneMadrid.Price;
              warehouseLabel = "Warehouse Madrid";
              _logger.LogInformation($"Warehouse Madrid rate found: ${warehouseRate} for weight {warehouseZoneMadrid.Weight}kg");
            }
            else
            {
              _logger.LogWarning($"No Warehouse Madrid rate found for weight: {roundedTotalWeight}kg");
            }
            break;
            
          case "MIAMI":
            var warehouseZoneMiami = await _dbContext.ImportWarehouseMiamis
              .Where(w => w.Weight <= roundedTotalWeight)
              .OrderByDescending(w => w.Weight)
              .FirstOrDefaultAsync();
              
            if (warehouseZoneMiami != null)
            {
              warehouseRate = warehouseZoneMiami.Price;
              warehouseLabel = "Warehouse Miami";
              _logger.LogInformation($"Warehouse Miami rate found: ${warehouseRate} for weight {warehouseZoneMiami.Weight}kg");
            }
            else
            {
              _logger.LogWarning($"No Warehouse Miami rate found for weight: {roundedTotalWeight}kg");
            }
            break;
            
          default:
            _logger.LogError($"Invalid warehouse country: {warehouseCountry}. Valid options are: China, Madrid, Miami");
            return new QuotationCalculationResult
            {
              Success = false,
              Error = $"Invalid warehouse country: {warehouseCountry}. Valid options are: China, Madrid, Miami"
            };
        }
      }

      decimal additionalServicesCost = 0;
      decimal storage = Math.Round(totaNetlWeight * 1.0M, 2);

      decimal exportDocument = 0;
      decimal collectFeeMin = 0;
      decimal collectFeePercentage = 0;
      decimal TasaSumaria = 0;
      decimal terminalCharge = customerComission ?? 0;
      decimal tvhRate = 0;
      bool isTvhPrepaid = false;
      
      // TVH Rates - Usar tvhType para determinar si es prepaid o collect
      if (serviceTypeId == 5)
      {
          // Verificar si se proporcionó tvhType
          if (!tvhType.HasValue)
          {
              _logger.LogError("TVH service selected but tvhType not provided");
              return new QuotationCalculationResult
              {
                  Success = false,
                  Error = "TVH service selected but tvhType not provided (1 = prepaid, 2 = collect)"
              };
          }
          
          // Determinar si es prepaid o collect basado en tvhType
          isTvhPrepaid = tvhType.Value == 1;
          
          // Validar que el valor del paquete sea coherente con el tipo de TVH seleccionado
          if (isTvhPrepaid && totalPackageValue < 2500)
          {
              _logger.LogError($"TVH Prepaid selected but package value (${totalPackageValue}) is less than $2500");
              return new QuotationCalculationResult
              {
                  Success = false,
                  Error = "TVH Prepaid (type 1) requiere un valor de paquete mínimo de $2500"
              };
          }
          else if (!isTvhPrepaid && totalPackageValue > 2500)
          {
              _logger.LogError($"TVH Collect selected but package value (${totalPackageValue}) is greater than $2500");
              return new QuotationCalculationResult
              {
                  Success = false,
                  Error = "TVH Collect (type 2) requiere un valor de paquete máximo de $2500"
              };
          }
          
          _logger.LogInformation($"TVH service type: {(isTvhPrepaid ? "Prepaid" : "Collect")}");
          
          // Calcular los kilos a cotizar según el tipo
          decimal kgToCost;
          if (isTvhPrepaid)
          {
              // Para prepaid, solo cotizar kilos adicionales a partir de 50kg
              kgToCost = Math.Max(0, totalWeight - 50);
              _logger.LogInformation($"TVH Prepaid: Total weight={totalWeight}kg, Charging for={kgToCost}kg (over 50kg)");
          }
          else
          {
              // Para collect, cotizar todos los kilos
              kgToCost = totalWeight;
              _logger.LogInformation($"TVH Collect: Total weight={totalWeight}kg, Charging for all={kgToCost}kg");
          }
          
          // Redondear los kilos a cotizar antes de buscar en las tablas
          decimal roundedKgToCost = RoundWeightToNearestHalf(kgToCost);
          _logger.LogInformation($"Peso para TVH redondeado: {kgToCost}kg -> {roundedKgToCost}kg");
          
          // Buscar el precio según el peso a cotizar y tipo de TVH
          if (roundedKgToCost > 0)
          {
              if (isTvhPrepaid)
              {
                  // Usar TvhImportPrepaids para tipo prepaid
                  var tvhPricing = await _dbContext.TvhImportPrepaids
                      .Where(c => c.Weight <= roundedKgToCost)
                      .OrderByDescending(c => c.Weight)
                      .FirstOrDefaultAsync();
                  
                  if (tvhPricing != null)
                  {
                      tvhRate = tvhPricing.Price;
                      _logger.LogInformation($"TVH Prepaid Rate found: ${tvhRate} for {tvhPricing.Weight}kg");
                  }
                  else
                  {
                      _logger.LogWarning($"No TVH Prepaid rate found for weight: {roundedKgToCost}kg");
                      tvhRate = 0;
                  }
              }
              else
              {
                  // Usar TvhImportCollects para tipo collect
                  var tvhPricing = await _dbContext.TvhImportCollects
                      .Where(c => c.Weight <= roundedKgToCost)
                      .OrderByDescending(c => c.Weight)
                      .FirstOrDefaultAsync();
                  
                  if (tvhPricing != null)
                  {
                      tvhRate = tvhPricing.Price;
                      _logger.LogInformation($"TVH Collect Rate found: ${tvhRate} for {tvhPricing.Weight}kg");
                  }
                  else
                  {
                      _logger.LogWarning($"No TVH Collect rate found for weight: {roundedKgToCost}kg");
                      tvhRate = 0;
                  }
              }
          }
          else
          {
              _logger.LogInformation("TVH Prepaid: No additional kilos to charge (below 50kg threshold)");
              tvhRate = 0;
          }
      }

      // Cargar servicios adicionales para exportación si serviceTypeId == 4
      if (serviceTypeId == 4 || serviceTypeId == 3)
      {
          var exportD = await _dbContext.AdditionalServices.Where(c => c.Code == "EXPORT_D").FirstOrDefaultAsync();
          var TasaS = await _dbContext.AdditionalServices.Where(c => c.Code == "TASA_S").FirstOrDefaultAsync();
          var CargoT = await _dbContext.AdditionalServices.Where(c => c.Code == "CARGO_T").FirstOrDefaultAsync();
          var CollectF = await _dbContext.AdditionalServices.Where(c => c.Code == "COLLECT_F").FirstOrDefaultAsync();


          if (serviceTypeId== 4)
          {
              exportDocument = exportD?.UpsValue ?? 0;
          }
          else
          {
              exportDocument = warehouseCountry == "Madrid"? exportD?.UpsValue ?? 0 : 0;  
          }
          
          TasaSumaria = TasaS?.UpsValue ?? 0;
          terminalCharge = serviceTypeId == 4? CargoT?.UpsValue + terminalCharge?? 0:terminalCharge + Math.Round(
              Math.Max(Math.Min(totalPackageValue * 0.10M, 65.00M), 20),
              2
          ); // Para serviceTypeId=4, terminalCharge viene de la base de datos
          collectFeeMin = CollectF?.MinimumValue ?? 0;
          collectFeePercentage = CollectF?.UpsValue ?? 0;
      }
      else 
      {
          // Calculate terminal charge based on package values (para servicios que no son exportación ni TVH ni Warehouse)
          terminalCharge =  terminalCharge + Math.Round(
              Math.Max(Math.Min(totalPackageValue * 0.10M, 65.00M), 20),
              2
          );
      }

      //Fuel
      var fuel = await _dbContext.AdditionalServices.Where(c => c.Code == "FUEL").FirstOrDefaultAsync();
      var upsFuel = fuel?.UpsValue ?? 0;
      var fedexFuel = fuel?.FedexValue ?? 0;

      // Fetch customer discounts
      var customerDiscount = await _dbContext.CustomerBillingReason
          .Where(c => c.CustomerId == customerId)
          .Include(b => b.BillingReason)
          .ToListAsync();

      // Get billing reason zone based on country
      // var billingReasonZone = await _dbContext.SetupImportZones
      //     .Where(c => c.Name.ToUpper() == country)
      //     .FirstOrDefaultAsync();
      
      var billingReasonZone = await _dbContext.SetupImportZones
          .Where(c => c.Code.ToUpper() == iataCode.ToUpper())
          .FirstOrDefaultAsync();

      decimal clientDiscountCourierDirecto = 0;
      decimal clientDiscountEcommerce = 0;
      decimal clientDiscountWarehouse = 0;
      decimal clientDiscountTvh = 0;
      decimal clientDiscountCourierCarga = 0;

      // TODO: Agregar en billinReasonZone Todos los paises con su motivo de facturacion country_master_impo, obtenerlos de la tabla legacy llamada
      // var billingReason =await  _dbContext.BillingReasons.Where(c => c.Code == billingReasonZone.QuotationsCd).FirstOrDefaultAsync();
      // var cifRate = billingReason.CifFreightRate is null? 0:billingReason.CifFreightRate;
      // var zoneCode = setupZone.IataCode;
      // var mercosur = new List<string> {"VE","BR","UY" };
      // if (zoneCode == "CH")
      // {
      //     cifRate = 5.50;
      // }else if (mercosur.Contains(zoneCode))
      // {
      //     cifrate = 2.50
      // }
      // else
      // {
      //     cifRate = 2.50;
      // }
      //
      
      var mercosurPartes = new List<string> { "AR", "BO", "BR", "PY", "UY", "VE" };

// Países del Mercosur - Estados Asociados
      var mercosurAsociados = new List<string> { "CL", "CO", "EC", "GY", "PE", "SR" };

// Todos los países del Mercosur combinados
      var mercosur = new List<string>(mercosurPartes);
      mercosur.AddRange(mercosurAsociados);

// Países de Europa
      var europa = new List<string> {
          "AL", "AT", "BE", "BA", "BG", "CY", "HR", "DK", "SK", "SI", "ES", "EE",
          "FI", "FR", "GR", "HU", "IE", "IS", "IT", "LV", "LI", "LT", "LU", "MK",
          "MT", "MC", "ME", "NO", "NL", "PL", "PT", "GB", "CZ", "RO", "RU", "SM",
          "RS", "SE", "CH", "UA", "DE"
      };

// Obtener el código de zona
      var zoneCode = setupZone.IataCode.Substring(0,2);

// Validación para asegurar que el código de zona sea válido
      if (string.IsNullOrEmpty(zoneCode))
      {
          throw new ArgumentException("El código IATA no puede estar vacío");
      }

// Lógica para determinar la tasa CIF según la zona usando switch
      decimal cifRate;

      switch (zoneCode)
      {
          case "CN":
              // Suiza tiene una tasa específica
              cifRate = 5.50M;
              break;
    
          case "US":
              // Estados Unidos
              cifRate = 3.75M;
              break;
    
          default:
              // Usar un enfoque diferente para las listas
              if (mercosurPartes.Contains(zoneCode))
              {
                  // Estados Partes del Mercosur
                  cifRate = 2.50M;
              }
              else if (mercosurAsociados.Contains(zoneCode))
              {
                  // Estados Asociados del Mercosur
                  cifRate = 2.25M;
              }
              else if (europa.Contains(zoneCode))
              {
                  // Países de Europa
                  cifRate = 3.50M;
              }
              else
              {
                  // Resto del mundo
                  cifRate = 2.50M;
              }
              break;
      }

      //return cifRate;
      // Calcular CifFreight - peso total del envío multiplicado por cifRate
      decimal cifFreight = Math.Round(totalWeight * cifRate, 2);
      
      decimal insurance;
      // if (serviceTypeId == 1 || serviceTypeId == 2 || serviceTypeId == 4)
      // {
      //     // Mínimo 10 USD o 1% del valor del paquete
      //     decimal calculatedInsurance = Math.Round(totalPackageValue * 0.01M, 2);
      //     insurance = Math.Max(10M, calculatedInsurance);
      // }
      // else
      // {
      //     insurance = Math.Round(totalPackageValue * 0.01M, 2);
      // }
      
      decimal calculatedInsurance = serviceTypeId == 4? Math.Round(totalPackageValue * 0.007M, 2):Math.Round(totalPackageValue * 0.01M, 2);
      insurance = Math.Max(10M, calculatedInsurance);
      
      // Insurance para CIF - 1% del (valor del paquete + cifFreight)
      decimal cifInsurance = Math.Round((totalPackageValue + cifFreight) * 0.01M, 2);

      if (billingReasonZone != null)
      {
        // Customer discounts
        clientDiscountCourierDirecto = customerDiscount
            .FirstOrDefault(c => c.BillingReason.Code == billingReasonZone.QuotationsCd)
            ?.Percentage ?? 0;
        
        clientDiscountEcommerce = customerDiscount
            .FirstOrDefault(c => c.BillingReason.Code == billingReasonZone.QuotationsEcommerce)
            ?.Percentage ?? 0;
        clientDiscountWarehouse = customerDiscount
            .FirstOrDefault(c => c.BillingReason.Code == billingReasonZone.QuotationsWarehouse)
            ?.Percentage ?? 0;
        clientDiscountCourierCarga = customerDiscount
            .FirstOrDefault(c => c.BillingReason.Code == billingReasonZone.QuotationsCc)
            ?.Percentage ?? 0;
            
        clientDiscountTvh = customerDiscount
            .FirstOrDefault(c => c.BillingReason.Code == billingReasonZone.QuotationsTvh)
            ?.Percentage ?? 0;
      }
      else
      {
        _logger.LogWarning($"No se encontró SetupImportZones para el país: {country}");
      }

      // Calculate rates for each package
      var packageRates = new List<PackageRate>();
      
      // Special handling for warehouse (serviceTypeId = 3)
      if (serviceTypeId == 3)
      {
          // Crear una entrada única para el servicio de Warehouse
          var warehouseServiceKey = "Warehouse";
          var warehouseServiceRates = new Dictionary<string, decimal>();
          warehouseServiceRates[warehouseServiceKey] = warehouseRate;
          
          packageRates.Add(
              new PackageRate
              {
                  UpsServiceRates = warehouseServiceRates,
                  FedexServiceRates = new Dictionary<string, decimal>()
              }
          );
      }
      // Para serviceTypeId = 5 (TVH), no necesitamos calcular rates por paquete
      else if (serviceTypeId == 5)
      {
          // Crear una entrada única para el servicio TVH
          var tvhServiceKey = "TVH";
          var tvhServiceRates = new Dictionary<string, decimal>();
          tvhServiceRates[tvhServiceKey] = tvhRate;
          
          packageRates.Add(
              new PackageRate
              {
                  UpsServiceRates = tvhServiceRates,
                  FedexServiceRates = new Dictionary<string, decimal>()
              }
          );
      }
      else
      {
          foreach (var package in packages)
          {
            // Use the maximum of NetWeight and VolumetricWeight
            decimal chargeableWeight = Math.Max(package.NetWeight, package.VolumetricWeight);
    
            int upsPackageType = chargeableWeight > (decimal)31.5 ? 3 : packageType;
            int fedexPackageType = chargeableWeight > (decimal)20.5 ? 4 : packageType;
    
            List<UpsServices> upsRates = await GetUpsRates(chargeableWeight, upsPackageType);
            List<UpsServices> upsMinimumRates = await GetUpsMinimumRates(chargeableWeight, 4);
            List<FedexServices> fedexRates = await GetFedexRates(chargeableWeight, fedexPackageType);
            //List<FedexServices> fedexMinimumRates = await GetFedexMinimumRates(chargeableWeight, 4);
    
            var upsServiceRates = new Dictionary<string, decimal>();
            
            for (int i = 0; i < upsRates.Count && i < upsMinimumRates.Count; i++)
            {
                var upsRate = upsRates[i];
                var upsMinimumRate = upsMinimumRates[i];

                decimal upsZoneRate = GetUpsZoneRate(upsRate, upsZones?.ExpressSaver ?? 0);
                decimal upsZoneRateMinimum = GetUpsZoneRate(upsMinimumRate, upsZones?.ExpressSaver ?? 0);
                decimal finalRate = 0;

                // Si es ExpressFreight, aplicar lógica especial según el peso
                if (upsRate is UpsImportServicesExpressFreight)
                {
                    if (chargeableWeight < 68m)
                    {
                        // Si pesa menos de 68kg, usar el valor mínimo
                        finalRate = upsZoneRateMinimum;
                    }
                    else
                    {
                        // Si pesa 68kg o más, usar upsZoneRate * chargeableWeight
                        finalRate = upsZoneRate * chargeableWeight;
                    }
                }
                else if (upsPackageType == 3)
                {
                    // Para otros servicios con paquete tipo 3, aplicar la fórmula original
                    finalRate = upsZoneRateMinimum + upsZoneRate * (chargeableWeight - 32m);
                }
                else
                {
                    // Para el resto de casos, usar el valor mayor entre ambas tarifas
                    //finalRate = upsZoneRate > upsZoneRateMinimum ? upsZoneRate : upsZoneRateMinimum;
                    finalRate = upsZoneRate;
                }
                
                if (upsZones?.ExpressSaver == 3)
                {
                    if (serviceTypeId == 1 || serviceTypeId == 2 || serviceTypeId == 4)
                    {
                        //directo warehouse e ecommerce

                        if (totalWeight <= 10 || totalWeight > 40)
                        {
                            upsServiceRates[GetUpsServiceName(upsRate)] = finalRate * 3M;

                        }
                        else if (totalWeight >= 10 && totalWeight <= 40)
                        {
                            upsServiceRates[GetUpsServiceName(upsRate)] = finalRate * 2.8M;
                        }
                    }

                }
                else
                {
                    if (serviceTypeId == 1 || serviceTypeId == 2 || serviceTypeId == 4 )
                    {
                        //directo warehouse e ecommerce
                    
                        if (totalWeight <= 10 || totalWeight > 40)
                        {
                            upsServiceRates[GetUpsServiceName(upsRate)] = finalRate * 4.0M;
                        
                        }else if (totalWeight >= 10 && totalWeight <= 40)
                        {
                            upsServiceRates[GetUpsServiceName(upsRate)] = finalRate * 3.5M;
                        }
                    }
                    else
                    {
                        upsServiceRates[GetUpsServiceName(upsRate)] = finalRate * 3;
                    }
                        
                }

            }
    
            var fedexServiceRates = new Dictionary<string, decimal>();
            var processedServices = new Dictionary<string, bool>();

            for (int i = 0; i < fedexRates.Count; i++)
            {
                var fedexRate = fedexRates[i];
                string serviceName = GetFedexServiceName(fedexRate);
          
                if (processedServices.ContainsKey(serviceName) && processedServices[serviceName])
                    continue;
          
                decimal fedexZoneRate = GetFedexZoneRate(fedexRate, fedexZones?.Ip ?? "", chargeableWeight);
          
                if (fedexZoneRate > 0)
                {
                    decimal finalRate = 0;
            
                    if (upsPackageType == 3)
                    {
                        finalRate = fedexZoneRate * chargeableWeight;
                    }
                    else
                    {
                        finalRate = fedexZoneRate;
                    }
            
                   // fedexServiceRates[serviceName] = finalRate * 3;
                    if (serviceTypeId == 1 || serviceTypeId == 2 || serviceTypeId == 4)
                    {
                        fedexServiceRates[serviceName] = finalRate * 3.5M;
                    }
                    else
                    {
                        fedexServiceRates[serviceName]  = finalRate * 3;
                    }
                    processedServices[serviceName] = true;
                }
                else
                {
                    if (!processedServices.ContainsKey(serviceName))
                        processedServices[serviceName] = false;
                }
            }

            packageRates.Add(
                new PackageRate
                {
                    UpsServiceRates = upsServiceRates,
                    FedexServiceRates = fedexServiceRates
                }
            );
          }
      }
      
      
      

      // Calculate totals by service
      var totalsByService = new Dictionary<string, TotalsByService>();

      // Check if PackageTypeId = 1 (Document/Envelope) - applies to all service types
      bool hasDocumentPackage = packageType == 1;
      _logger.LogInformation($"Document package detected: {hasDocumentPackage} (PackageTypeId: {packageType}, ServiceTypeId: {serviceTypeId})");

      // Special handling for Warehouse (serviceTypeId = 3)
      if (serviceTypeId == 3)
      {
          // Crear una entrada única para el servicio de Warehouse
          var warehouseServiceKey = "Warehouse";
          totalsByService[warehouseServiceKey] = new TotalsByService
          {
              Label = warehouseLabel,
              CourierLabel = warehouseLabel
          };
          
          // Aplicar descuento al rate de warehouse
          decimal discountPercentage = clientDiscountWarehouse;
          decimal discountedWarehouseRate = Math.Round(warehouseRate * (1 - (discountPercentage / 100)), 2);
          
          // Calcular fuel charge - Para ecommerce (serviceTypeId = 2), no aplicar fuel charge
          decimal fuelCharge =  Math.Round(discountedWarehouseRate * (upsFuel / 100), 2);
          
          // Establecer valores en TotalsByService
          totalsByService[warehouseServiceKey].Flete = discountedWarehouseRate;
          totalsByService[warehouseServiceKey].UpsFuel = fuelCharge;
          
          // Agregar pickup rate
          totalsByService[warehouseServiceKey].PickupRate = pickupRate;
          
          // Calculate CIF
          decimal cif = totalPackageValue + cifInsurance + cifFreight;
          
          // Si totalPackageValue <= 400, no aplicar cargos adicionales para warehouse
          // bool applyAdditionalCharges = !(totalPackageValue <= 400);
          //
          // Calcular estadistica, derecho e IVA si aplica
          decimal estadistica = 0;
          decimal derecho = 0;
          decimal iva = 0;
          
          // if (applyAdditionalCharges)
          // {
          //   estadistica = Math.Round(cif * 0.03M, 2);
          //   derecho = Math.Round(cif * (dutyPercentage / 100), 2);
          //   iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);
          // }
          estadistica = Math.Round(cif * 0.03M, 2);
          derecho = Math.Round(cif * (dutyPercentage / 100), 2);
          iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);
          
          // Apply document package logic if PackageTypeId = 1
          if (hasDocumentPackage)
          {
              _logger.LogInformation("Applying document package logic for Warehouse service - excluding all charges except freight and fuel");
              estadistica = 0;
              derecho = 0;
              iva = 0;
              insurance = 0;
              storage = 0;
              terminalCharge = 0;
              customerExtraCharge = 0;
              exportDocument = 0;
          }

          // Actualizar valores en TotalsByService
          totalsByService[warehouseServiceKey].Cif = cif;
          totalsByService[warehouseServiceKey].Estadistica = estadistica;
          totalsByService[warehouseServiceKey].Derecho = derecho;
          totalsByService[warehouseServiceKey].Iva = iva;

          // Actualizar valores finales
          totalsByService[warehouseServiceKey].Insurance = insurance;
          totalsByService[warehouseServiceKey].Storage = storage;
          totalsByService[warehouseServiceKey].TerminalCharge = terminalCharge;
          totalsByService[warehouseServiceKey].ExtraCharge = customerExtraCharge;
          totalsByService[warehouseServiceKey].ExportDocument = exportDocument;

          // Calcular subTotal y total
          // Calcular subTotal y total
          if (hasDocumentPackage)
          {
              // For document packages, only include freight and fuel charges
              totalsByService[warehouseServiceKey].SubTotal = Math.Round(discountedWarehouseRate + pickupRate + fuelCharge, 2);
          }
          else
          {
              totalsByService[warehouseServiceKey].SubTotal = Math.Round(customerExtraCharge + exportDocument +
                  discountedWarehouseRate + pickupRate + insurance + storage + terminalCharge + fuelCharge, 2);
          }

          totalsByService[warehouseServiceKey].Total = Math.Round(
              totalsByService[warehouseServiceKey].SubTotal + estadistica + derecho + iva, 2);
      }
      // Special handling for TVH (serviceTypeId = 5)
      else if (serviceTypeId == 5)
      {
          // Crear una entrada única para el servicio TVH
          var tvhServiceKey = "TVH";
          totalsByService[tvhServiceKey] = new TotalsByService
          {
              Label = isTvhPrepaid ? "TVH Prepaid" : "TVH Collect",
              CourierLabel = "TVH"
          };
          
          // Si es prepaid y peso <= 50kg, el flete y el fuel son 0
          decimal fuelCharge = 0;
          if (isTvhPrepaid && totalWeight <= 50)
          {
              totalsByService[tvhServiceKey].Flete = 0;
              totalsByService[tvhServiceKey].UpsFuel = 0;
              totalsByService[tvhServiceKey].SubTotal = 0;
          }
          else
          {
              // Aplicar descuento al rate
              decimal discountPercentage = clientDiscountTvh;
              decimal discountedRate = Math.Round(tvhRate * (1 - (discountPercentage / 100)), 2);
              fuelCharge = Math.Round(discountedRate * (upsFuel / 100), 2);
              
              totalsByService[tvhServiceKey].Flete = discountedRate;
              totalsByService[tvhServiceKey].UpsFuel = fuelCharge;
              totalsByService[tvhServiceKey].SubTotal = discountedRate;
          }
          
          // Calculate CIF
          decimal cif = totalPackageValue + cifInsurance + cifFreight;
          
          // Calcular estadistica, derecho e IVA normal
          decimal estadistica = Math.Round(cif * 0.03M, 2);
          decimal derecho = Math.Round(cif * (dutyPercentage / 100), 2);
          decimal iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);

          // Apply document package logic if PackageTypeId = 1
          if (hasDocumentPackage)
          {
              _logger.LogInformation("Applying document package logic for TVH service - excluding all charges except freight and fuel");
              estadistica = 0;
              derecho = 0;
              iva = 0;
              insurance = 0;
              storage = 0;
              terminalCharge = 0;
              customerExtraCharge = 0;
          }

          // Actualizar valores en TotalsByService
          totalsByService[tvhServiceKey].Cif = cif;
          totalsByService[tvhServiceKey].Estadistica = estadistica;
          totalsByService[tvhServiceKey].Derecho = derecho;
          totalsByService[tvhServiceKey].Iva = iva;
          totalsByService[tvhServiceKey].TerminalCharge = terminalCharge;
          totalsByService[tvhServiceKey].ExtraCharge = customerExtraCharge;

          // Actualizar valores finales
          totalsByService[tvhServiceKey].Insurance = insurance;
          totalsByService[tvhServiceKey].Storage = storage;
          
          // Calcular subTotal y total
          if (hasDocumentPackage)
          {
              // For document packages, only include freight and fuel charges
              if (isTvhPrepaid && totalWeight <= 50)
              {
                  // Para prepaid <= 50kg con documento, solo flete y fuel (que ya son 0)
                  totalsByService[tvhServiceKey].SubTotal = 0;
              }
              else
              {
                  // Para documento con peso > 50kg o collect, incluir flete y fuel
                  totalsByService[tvhServiceKey].SubTotal = Math.Round(totalsByService[tvhServiceKey].SubTotal + fuelCharge, 2);
              }
          }
          else if (isTvhPrepaid && totalWeight <= 50)
          {
              // Para prepaid <= 50kg, solo incluir insurance y storage
              totalsByService[tvhServiceKey].SubTotal = Math.Round(customerExtraCharge + insurance + storage + terminalCharge, 2);
          }
          else
          {
              // Para los demás casos, incluir todo
              totalsByService[tvhServiceKey].SubTotal = Math.Round(totalsByService[tvhServiceKey].SubTotal + customerExtraCharge  + terminalCharge + insurance + storage + fuelCharge, 2);
          }

          totalsByService[tvhServiceKey].Total = Math.Round(
              totalsByService[tvhServiceKey].SubTotal + estadistica + derecho + iva, 2);
      }
      else
      {
          // Proceso normal para otros tipos de servicio
          foreach (var packageRate in packageRates)
          {
            foreach (var upsServiceRate in packageRate.UpsServiceRates)
            {
              if (!totalsByService.ContainsKey(upsServiceRate.Key))
              {
                totalsByService[upsServiceRate.Key] = new TotalsByService();
                totalsByService[upsServiceRate.Key].Label = upsServiceRate.Key switch
                {
                  "UpsSaver" => "Raico Priority",
                  //"UpsExpedited" => "Raico Economy",
                  //"UpsExpressFreight" => "Raico Priority (Directo)",
                  _ => ""
                };
                totalsByService[upsServiceRate.Key].CourierLabel = upsServiceRate.Key switch
                {
                  "UpsSaver" => "UPS Saver",
                  //"UpsExpedited" => "UPS Expedited",
                 // "UpsExpressFreight" => "UPS Express Freight",
                  _ => ""
                };
              }
    
              // Apply discount to the rate *BEFORE* calculating CIF
              decimal discountPercentage = serviceTypeId switch
              {
                1 => clientDiscountCourierDirecto,
                2 => clientDiscountEcommerce,
                3 => clientDiscountWarehouse,
                4 => clientDiscountCourierCarga,
                5 => clientDiscountTvh,
                _ => 0 // Or throw an exception for invalid service type
              };
              decimal discountedRate = Math.Round(upsServiceRate.Value * (1 - (discountPercentage / 100)), 2);
              // Para ecommerce (serviceTypeId = 2), no aplicar fuel charge
              decimal fuelCharge = serviceTypeId == 2 ? 0 : Math.Round(discountedRate * (upsFuel / 100), 2);
    
              // Calculate CIF - MODIFICADO
              // Ahora usamos cifFreight y cifInsurance para el cálculo de CIF
              decimal cif = totalPackageValue + cifInsurance + cifFreight;
    
              // Calculate additional costs
              decimal estadistica = 0;
              decimal derecho = 0;
              decimal iva = 0;
    
              // Calcular collectFee para serviceTypeId = 4
              decimal collectFee = 0;
              if (serviceTypeId == 4)
              {
                  // Calcular collectFee según la regla: si el porcentaje aplicado al rate es mayor al mínimo, usar ese valor
                  decimal calculatedCollectFee = Math.Round(discountedRate * (collectFeePercentage / 100), 2);
                  collectFee = Math.Max(collectFeeMin, calculatedCollectFee);
                  
                  // Para serviceTypeId = 4, estadistica, derecho e iva se dejan en 0
                  estadistica = 0;
                  derecho = 0;
                  iva = 0;
                  
                  // Agregar los campos de exportación al objeto TotalsByService
                  totalsByService[upsServiceRate.Key].ExportDocument = exportDocument;
                  totalsByService[upsServiceRate.Key].TasaSumaria = TasaSumaria;
                  totalsByService[upsServiceRate.Key].CollectFee = collectFee;
              }
              else
              {
                  // Apply document package logic if PackageTypeId = 1
                  if (hasDocumentPackage)
                  {
                      _logger.LogInformation("Applying document package logic for UPS service - excluding all charges except freight and fuel");
                      estadistica = 0;
                      derecho = 0;
                      iva = 0;
                  }
                  else
                  {
                      // Si es serviceTypeId=2 (Warehouse) y totalPackageValue <= 400, no aplicar cargos adicionales
                      bool applyAdditionalCharges = !(serviceTypeId == 2 && totalPackageValue <= 400);
                      if (applyAdditionalCharges)
                      {
                          estadistica = serviceTypeId == 2
                              ? Math.Round((cif - 400) * 0.03M, 2)
                              : Math.Round((cif) * 0.03M, 2);
                          // Usar dutyPercentage en lugar del 18% fijo
                          derecho = serviceTypeId == 2
                              ? Math.Round((cif - 400) * (dutyPercentage / 100), 2)
                              : Math.Round((cif) * (dutyPercentage / 100), 2);
                          // iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);
                      }
                      iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);
                  }
              }
    
              // Accumulate values in TotalsByService
              totalsByService[upsServiceRate.Key].Cif += cif;
              totalsByService[upsServiceRate.Key].Estadistica = estadistica;
              totalsByService[upsServiceRate.Key].Derecho = derecho;
              totalsByService[upsServiceRate.Key].Iva = iva;
              totalsByService[upsServiceRate.Key].SubTotal += discountedRate;
              totalsByService[upsServiceRate.Key].UpsFuel += fuelCharge;
              totalsByService[upsServiceRate.Key].Flete += discountedRate;
              totalsByService[upsServiceRate.Key].ExtraCharge =   +customerExtraCharge;
            }
    
            foreach (var fedexServiceRate in packageRate.FedexServiceRates)
            {
              if (!totalsByService.ContainsKey(fedexServiceRate.Key))
              {
                totalsByService[fedexServiceRate.Key] = new TotalsByService();
                totalsByService[fedexServiceRate.Key].Label = fedexServiceRate.Key switch
                {
                    "FedexIe" => "Raico Economy",
                    "FedexIp" => "Raico Priority",
                    "FedexIpf" => "Raico Priority Carga",
                    "FedexIef" => "Raico Priority Carga",
                    _ => ""
                };
                totalsByService[fedexServiceRate.Key].CourierLabel = fedexServiceRate.Key switch
                {
                    "FedexIe" => "Fedex International Economy",
                    "FedexIef" => "Fedex International Economy",
                    "FedexIp" => "Fedex International Priority",
                    "FedexIpf" => "Fedex International Priority",
                    _ => ""
                };
              }
    
              // Apply discount to the rate *BEFORE* calculating CIF
              decimal discountPercentage = serviceTypeId switch
              {
                1 => clientDiscountCourierDirecto,
                2 => clientDiscountWarehouse,
                3 => clientDiscountCourierCarga,
                _ => 0 // Or throw an exception for invalid service type
              };
              decimal discountedRate = Math.Round(fedexServiceRate.Value * (1 - (discountPercentage / 100)), 2);
              // Para ecommerce (serviceTypeId = 2), no aplicar fuel charge
              decimal fuelCharge = serviceTypeId == 2 ? 0 : Math.Round(discountedRate * (fedexFuel / 100), 2);
    
              // Calculate CIF - MODIFICADO
              // Ahora usamos cifFreight y cifInsurance para el cálculo de CIF
              decimal cif = totalPackageValue + cifInsurance + cifFreight;
    
              // Calculate additional costs
              decimal estadistica = 0;
              decimal derecho = 0;
              decimal iva = 0;
    
              // Calcular collectFee para serviceTypeId = 4
              decimal collectFee = 0;
              if (serviceTypeId == 4)
              {
                  // Calcular collectFee según la regla: si el porcentaje aplicado al rate es mayor al mínimo, usar ese valor
                  // decimal calculatedCollectFee = Math.Round(discountedRate * (collectFeePercentage / 100), 2);
                  // collectFee = Math.Max(collectFeeMin, calculatedCollectFee);
                  
                  // Para serviceTypeId = 4, estadistica, derecho e iva se dejan en 0
                  estadistica = 0;
                  derecho = 0;
                  iva = 0;
                  
                  // Agregar los campos de exportación al objeto TotalsByService
                  totalsByService[fedexServiceRate.Key].ExportDocument = exportDocument;
                  totalsByService[fedexServiceRate.Key].TasaSumaria = TasaSumaria;
                  totalsByService[fedexServiceRate.Key].CollectFee = collectFee;
              }
              else
              {
                  // Apply document package logic if PackageTypeId = 1
                  if (hasDocumentPackage)
                  {
                      _logger.LogInformation("Applying document package logic for FedEx service - excluding all charges except freight and fuel");
                      estadistica = 0;
                      derecho = 0;
                      iva = 0;
                  }
                  else
                  {
                      // Si es serviceTypeId=2 (Warehouse) y totalPackageValue <= 400, no aplicar cargos adicionales
                      bool applyAdditionalCharges = !(serviceTypeId == 2 && totalPackageValue <= 400);

                      if (applyAdditionalCharges)
                      {
                          estadistica = serviceTypeId == 2
                              ? Math.Round((cif - 400) * 0.03M, 2)
                              : Math.Round((cif) * 0.03M, 2);
                          // Usar dutyPercentage en lugar del 18% fijo
                          derecho = serviceTypeId == 2
                              ? Math.Round((cif - 400) * (dutyPercentage / 100), 2)
                              : Math.Round((cif) * (dutyPercentage / 100), 2);
                          // iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);
                      }
                      iva = Math.Round((cif + derecho + estadistica) * 0.21M, 2);
                  }
              }
    
              // Accumulate values in TotalsByService
              totalsByService[fedexServiceRate.Key].Cif += cif;
              totalsByService[fedexServiceRate.Key].Estadistica = estadistica;
              totalsByService[fedexServiceRate.Key].Derecho = derecho;
              totalsByService[fedexServiceRate.Key].Iva = iva;
              totalsByService[fedexServiceRate.Key].SubTotal += discountedRate;
              totalsByService[fedexServiceRate.Key].FedexFuel += fuelCharge;
              totalsByService[fedexServiceRate.Key].Flete += discountedRate;
            }
          }
    
          // Add insurance, storage, terminalCharge, and fuelCharge *ONCE* per service
          foreach (var service in totalsByService.Values)
          {
            // Apply document package logic if PackageTypeId = 1
            if (hasDocumentPackage)
            {
                _logger.LogInformation("Applying document package logic for final service calculation - excluding all charges except freight and fuel");
                service.TerminalCharge = 0;
                service.Insurance = 0;
                service.Storage = 0;
                service.ExtraCharge = 0;
                service.ExportDocument = 0;
                service.TasaSumaria = 0;
                service.CollectFee = 0;

                // For document packages, SubTotal should only include freight (Flete) and fuel (UpsFuel)
                service.SubTotal = Math.Round(service.Flete + service.UpsFuel, 2);
            }
            else
            {
                // Para ecommerce (serviceTypeId = 2), no agregar terminalCharge ni fuel
                if (serviceTypeId == 2) // ecommerce
                {
                    service.TerminalCharge = 0;
                    service.Flete = service.Flete + upsFuel;
                }
                else
                {
                    service.TerminalCharge = terminalCharge;
                }

                service.Insurance = insurance;
                service.Storage = storage;

                // Cálculo del SubTotal diferente para serviceTypeId=4
                if (serviceTypeId == 4)
                {
                    service.Storage = 0;
                    //service.CollectFee = 0;
                    service.SubTotal = Math.Round(service.SubTotal + insurance  + service.UpsFuel + service.ExtraCharge + service.CollectFee +
                        service.ExportDocument + service.TasaSumaria + service.TerminalCharge , 2);
                }
                else if (serviceTypeId == 2) // ecommerce - no sumar terminalCharge
                {
                    service.SubTotal = Math.Round(service.SubTotal + customerExtraCharge + insurance + storage + upsFuel, 2);
                }
                else
                {
                    service.SubTotal = Math.Round(service.SubTotal + customerExtraCharge + insurance + storage + service.TerminalCharge + service.UpsFuel, 2);
                }
            }

            service.Total = Math.Round(service.SubTotal + service.Estadistica + service.Derecho + service.Iva, 2);
          }
      }

      decimal subTotalAllServices = totalsByService.Values.Sum(t => t.SubTotal);
      if (additionalServices != null && additionalServices.Any())
      {
        foreach (var additionalService in additionalServices)
        {
          additionalServicesCost += CalculateAdditionalServiceCost(
              additionalService,
              packages,
              subTotalAllServices
          );
        }
      }

      // Add additional services cost to the total
      foreach (var service in totalsByService.Values)
      {
        service.Total = Math.Round(service.Total + additionalServicesCost, 2);
      }

      return new QuotationCalculationResult
      {
        Success = true,
        PackageRates = packageRates,
        TotalsByService = totalsByService,
        AdditionalServicesCost = additionalServicesCost
      };
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error calculating quotation");
      return new QuotationCalculationResult
      {
        Success = false,
        Error = "Error calculating quotation"
      };
    }
  }

  private async Task<List<UpsServices>> GetUpsRates(decimal weight, int packageTypeId)
  {
    // Redondear el peso para buscar en las tablas
    decimal roundedWeight = RoundWeightToNearestHalf(weight);
    _logger.LogInformation($"Buscando tarifa UPS para peso: {weight}kg (redondeado a {roundedWeight}kg)");
    
    var rates = new List<UpsServices>();

    // Get rates from all three tables
    IQueryable<UpsServices> querySavers = _dbContext.UpsImportServicesSavers;
    if (packageTypeId == 3)
    {
      querySavers = querySavers.Where(r => r.UpsPackageType.Id == packageTypeId);
    }
    else
    {
      querySavers = querySavers.Include(r => r.UpsPackageType).Where(
          r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
      ).Distinct();
    }
    var savers = await querySavers.FirstOrDefaultAsync();
    rates.Add(savers);

    // IQueryable<UpsServices> queryExpressFreights =
    //     _dbContext.UpsImportServicesExpressFreights;
    //
    //   queryExpressFreights = queryExpressFreights.Where(
    //       r => r.UpsPackageType.Id == 3
    //   );
    //
    // var expressFreights = await queryExpressFreights.ToListAsync();
    // rates.AddRange(expressFreights);
    //
    // IQueryable<UpsServices> queryExpediteds = _dbContext.UpsImportServicesExpediteds;
    // if (packageTypeId == 3)
    // {
    //   queryExpediteds = queryExpediteds.Where(
    //       r => r.UpsPackageType.Id == packageTypeId
    //   );
    // }
    // else
    // {
    //   queryExpediteds = queryExpediteds.Where(
    //       r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
    //   );
    // }
    // var expediteds = await queryExpediteds.ToListAsync();
    // rates.AddRange(expediteds);

    return rates;
  }
  
  private async Task<List<UpsServices>> GetUpsMinimumRates(decimal weight, int packageTypeId)
  {
      // Redondear el peso para buscar en las tablas
      decimal roundedWeight = RoundWeightToNearestHalf(weight);
      _logger.LogInformation($"Buscando tarifa UPS para peso: {weight}kg (redondeado a {roundedWeight}kg)");
    
      var rates = new List<UpsServices>();

      // Get rates from all three tables
      IQueryable<UpsServices> querySavers = _dbContext.UpsImportServicesSavers;
      if (packageTypeId == 4)
      {
          querySavers = querySavers.Where(r => r.UpsPackageType.Id == packageTypeId);
      }
      else
      {
          querySavers = querySavers.Where(
              r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
          );
      }
      var savers = await querySavers.ToListAsync();
      rates.AddRange(savers);

      // IQueryable<UpsServices> queryExpressFreights =
      //     _dbContext.UpsImportServicesExpressFreights;
      // if (packageTypeId == 4)
      // {
      //     queryExpressFreights = queryExpressFreights.Where(
      //         r => r.UpsPackageType.Id == packageTypeId
      //     );
      // }
      // else
      // {
      //     queryExpressFreights = queryExpressFreights.Where(
      //         r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
      //     );
      // }
      // var expressFreights = await queryExpressFreights.ToListAsync();
      // rates.AddRange(expressFreights);

      // IQueryable<UpsServices> queryExpediteds = _dbContext.UpsImportServicesExpediteds;
      // if (packageTypeId == 4)
      // {
      //     queryExpediteds = queryExpediteds.Where(
      //         r => r.UpsPackageType.Id == packageTypeId
      //     );
      // }
      // else
      // {
      //     queryExpediteds = queryExpediteds.Where(
      //         r => r.UpsPackageType.Id == packageTypeId && r.Weight == roundedWeight
      //     );
      // }
      // var expediteds = await queryExpediteds.ToListAsync();
      // rates.AddRange(expediteds);

      return rates;
  }

  private async Task<List<FedexServices>> GetFedexRates(decimal weight, int packageType)
  {
    // Redondear el peso para buscar en las tablas
    decimal roundedWeight = RoundWeightToNearestHalf(weight);
    _logger.LogInformation($"Buscando tarifa Fedex para peso: {weight}kg (redondeado a {roundedWeight}kg)");
    
    var rates = new List<FedexServices>();

    // // Get rates from all four tables
    // IQueryable<FedexServices> queryIps = _dbContext.FedexImportServiceIps;
    // if (packageType == 4)
    // {
    //   queryIps = queryIps.Where(r => r.FedexPackageType.Id == packageType);
    // }
    // else
    // {
    //   queryIps = queryIps.Where(
    //       r => r.FedexPackageType.Id == packageType && r.Weight == roundedWeight
    //   );
    // }
    // var ips = await queryIps.ToListAsync();
    // rates.AddRange(ips);
    //
    // IQueryable<FedexServices> queryIefs = _dbContext.FedexImportServiceIefs;
    // if (packageType == 4)
    // {
    //   queryIefs = queryIefs.Where(r => r.FedexPackageType.Id == packageType);
    // }
    // else
    // {
    //   queryIefs = queryIefs.Where(
    //       r => r.FedexPackageType.Id == packageType && r.Weight == roundedWeight
    //   );
    // }
    // var iefs = await queryIefs.ToListAsync();
    // rates.AddRange(iefs);
    //
    // IQueryable<FedexServices> queryIes = _dbContext.FedexImportServiceIes;
    // if (packageType == 4)
    // {
    //   queryIes = queryIes.Where(r => r.FedexPackageType.Id == packageType);
    // }
    // else
    // {
    //   queryIes = queryIes.Where(
    //       r => r.FedexPackageType.Id == packageType && r.Weight == roundedWeight
    //   );
    // }
    // var ies = await queryIes.ToListAsync();
    // rates.AddRange(ies);
    //
    // IQueryable<FedexServices> queryIpfs = _dbContext.FedexImportServiceIpfs;
    // if (packageType == 4)
    // {
    //   queryIpfs = queryIpfs.Where(r => r.FedexPackageType.Id == packageType);
    // }
    // else
    // {
    //   queryIpfs = queryIpfs.Where(
    //       r => r.FedexPackageType.Id == packageType && r.Weight == roundedWeight
    //   );
    // }
    // var ipfs = await queryIpfs.ToListAsync();
    // rates.AddRange(ipfs);

    return rates;
  }

  private decimal GetUpsZoneRate(UpsServices upsService, int zone)
  {
    if (upsService == null)
    {
      return 0;
    }

    string zonePropertyName = $"Zone{zone}";
    PropertyInfo propertyInfo = typeof(UpsServices).GetProperty(zonePropertyName);

    if (propertyInfo == null)
    {
      _logger.LogError($"Zone property '{zonePropertyName}' not found on UpsServices.");
      return 0;
    }

    object value = propertyInfo.GetValue(upsService);

    if (value == null)
    {
      _logger.LogWarning($"Zone property '{zonePropertyName}' is null.");
      return 0;
    }

    return Convert.ToDecimal(value);
  }

  private decimal GetFedexZoneRate(FedexServices fedexService, string zone, decimal weight)
  {
      if (fedexService == null)
      {
          return 0;
      }

      decimal rate = 0;

      // Determine the correct zone property based on the zone string
      string zonePropertyName = $"Zone{zone.ToUpper()}";
      // Get the property info for the zone
      PropertyInfo zoneProperty = typeof(FedexServices).GetProperty(zonePropertyName);

      if (zoneProperty == null)
      {
          _logger.LogError($"Zone property '{zonePropertyName}' not found on FedexServices.");
          return 0;
      }

      // Get the value of the zone property
      object zoneValue = zoneProperty.GetValue(fedexService);

      if (zoneValue == null)
      {
          _logger.LogWarning($"Zone property '{zonePropertyName}' is null.");
          return 0;
      }

      // Convert the zone value to decimal
      decimal zoneRate = Convert.ToDecimal(zoneValue);

      // Check if weight_range is not null and calculate rate based on weight
      if (!string.IsNullOrEmpty(fedexService.WeightRange))
      {
          // Extract the lower and upper bounds from the weight_range string
          string weightRange = fedexService.WeightRange;
          string[] ranges = weightRange.Split(" - ");

          if (
              ranges.Length == 2 &&
              decimal.TryParse(ranges[0], out decimal lowerBound) &&
              decimal.TryParse(ranges[1], out decimal upperBound)
          )
          {
              // Check if the weight falls within the range
              if (weight >= lowerBound && weight <= upperBound)
              {
                  rate = zoneRate * weight; // Calculate the rate based on weight
              }
          }
          else
          {
              _logger.LogError($"Invalid weight_range format: {fedexService.WeightRange}");
          }
      }
      else
      {
          rate = zoneRate; // If no weight range, use the zone rate directly
      }

      return rate;
  }

  private string GetUpsServiceName(UpsServices upsService)
  {
    if (upsService is UpsImportServicesSaver)
      return "UpsSaver";
    if (upsService is UpsImportServicesExpedited)
      return "UpsExpedited";
    if (upsService is UpsImportServicesExpressFreight)
      return "UpsExpressFreight";

    return "UnknownUpsService";
  }

  private string GetFedexServiceName(FedexServices fedexService)
  {
    if (fedexService is FedexImportServiceIp)
      return "FedexIp";
    if (fedexService is FedexImportServiceIef)
      return "FedexIef";
    if (fedexService is FedexImportServiceIe)
      return "FedexIe";
    if (fedexService is FedexImportServiceIpf)
      return "FedexIp";
      return "UnknownFedexService";
  }
  
  public List<GetImportQuotation.PackageRequest> SumAllPackages(List<GetImportQuotation.PackageRequest> packages)
  {
      return new List<GetImportQuotation.PackageRequest>
      {
          new GetImportQuotation.PackageRequest
          {
              PackageValue = packages?.Sum(p => p.PackageValue) ?? 0,
              PackageTypeId = packages?.Sum(p => p.PackageTypeId) ?? 0,
              VolumetricWeight = packages?.Sum(p => p.VolumetricWeight) ?? 0,
              NetWeight = packages?.Sum(p => p.NetWeight) ?? 0
          }
      };
  }

  private static decimal CalculateAdditionalServiceCost(
      AdditionalService service,
      List<GetImportQuotation.PackageRequest> packages,
      decimal subTotal
  )
  {
    var totalValue = packages.Sum(p => p.PackageValue);

    if (service.MinimumValue.HasValue && totalValue < service.MinimumValue.Value)
      return service.UpsValue;

    return subTotal * (service.UpsValue / 100);
  }
}

public class QuotationCalculationResult
{
  public bool Success { get; set; }
  public string Error { get; set; }
  public List<PackageRate> PackageRates { get; set; }
  public Dictionary<string, TotalsByService> TotalsByService { get; set; }
  public decimal AdditionalServicesCost { get; set; }
}

public class PackageRate
{
  public Dictionary<string, decimal> UpsServiceRates { get; set; } =
      new Dictionary<string, decimal>();
  public Dictionary<string, decimal> FedexServiceRates { get; set; } =
      new Dictionary<string, decimal>();
}

public class TotalsByService
{
  public decimal Flete { get; set; }
  public decimal TerminalCharge { get; set; }
  public decimal ExtraCharge { get; set; } = 0;
  public decimal Insurance { get; set; }
  public decimal Storage { get; set; }
  public decimal UpsFuel { get; set; }
  public decimal SubTotal { get; set; }
  [JsonIgnore]
  public decimal Cif { get; set; }
  public decimal Estadistica { get; set; }
  public decimal Derecho { get; set; }
  public decimal Iva { get; set; }
  public decimal Total { get; set; }
  public string Label { get; set; }
  public string CourierLabel { get; set; }
  
  // Nuevos campos para serviceTypeId = 4 (exportación)
  public decimal ExportDocument { get; set; }
  public decimal TasaSumaria { get; set; }
  public decimal CollectFee { get; set; }
  
  // Nuevo campo para serviceTypeId = 3 (warehouse)
  public decimal PickupRate { get; set; }

  //[JsonIgnore]
  public decimal FedexFuel { get; set; }
}