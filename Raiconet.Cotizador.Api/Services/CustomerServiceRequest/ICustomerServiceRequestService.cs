using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Services.CustomerService;

public interface ICustomerServiceRequestService
{
    Task<CustomerServiceRequest?> GetByIdAsync(int id);
    Task<List<CustomerServiceRequest>> GetAllAsync(int? customerId = null, CustomerServiceRequestStatus? status = null);
    Task<CustomerServiceRequest> CreateAsync(CustomerServiceRequest request, User creator);
    Task<CustomerServiceRequest?> UpdateAsync(int id, CustomerServiceRequest request, string updatedBy);
    Task<bool> DeleteAsync(int id);
    Task<bool> UpdateStatusAsync(int id, CustomerServiceRequestStatus status, string updatedBy);
}
