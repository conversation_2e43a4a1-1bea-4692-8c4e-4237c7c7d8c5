using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Services.CustomerService;

public class CustomerServiceRequestService : ICustomerServiceRequestService
{
    private readonly AppDbContext _dbContext;
    private readonly ISessionManager _sessionManager;

    public CustomerServiceRequestService(
        AppDbContext dbContext,
        ISessionManager sessionManager)
    {
        _dbContext = dbContext;
        _sessionManager = sessionManager;
    }

    public async Task<CustomerServiceRequest?> GetByIdAsync(int id)
    {
        return await _dbContext.CustomerServiceRequests
            .Include(c => c.Customer)
            .Include(c => c.SetupRaicoService)
            .Include(c => c.Creator)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    // public async Task<List<CustomerServiceRequest>> GetAllAsync(int? customerId = null, CustomerServiceRequestStatus? status = null)
    // {
    //     var query = _dbContext.CustomerServiceRequests
    //         .Include(c => c.Customer)
    //         .Include(c => c.SetupRaicoService)
    //         .Include(c => c.Creator)
    //         .AsQueryable();
    //
    //     if (customerId.HasValue)
    //     {
    //         query = query.Where(c => c.CustomerId == customerId.Value);
    //     }
    //
    //     if (status.HasValue)
    //     {
    //         query = query.Where(c => c.Status == status.Value);
    //     }
    //
    //     return await query.ToListAsync();
    // }
    
    public async Task<List<CustomerServiceRequest>> GetAllAsync(int? customerId = null, CustomerServiceRequestStatus? status = null)
    {
        // Obtener el usuario actual y verificar si es administrador
        var currentUser = await _sessionManager.GetCurrentUserAsync();

        var query = _dbContext.CustomerServiceRequests
            .Include(c => c.Customer)
            .Include(c => c.SetupRaicoService)
            .Include(c => c.Creator)
            .AsQueryable();

        // Solo filtrar por customerId si NO es administrador o si explícitamente se proporciona un customerId
        if (customerId.HasValue)
        {
            query = query.Where(c => c.CustomerId == customerId.Value);
        }

        // El filtro por status siempre se aplica si se proporciona
        if (status.HasValue)
        {
            query = query.Where(c => c.Status == status.Value);
        }

        return await query.ToListAsync();
    }

    public async Task<CustomerServiceRequest> CreateAsync(CustomerServiceRequest request, User creator)
    {
        request.CreatedAt = DateTime.UtcNow;
        request.UpdatedAt = DateTime.UtcNow;
        request.CreatedById = creator.Id;
        request.UpdatedBy = creator.Id;
        request.Status = CustomerServiceRequestStatus.Pending;

        await _dbContext.CustomerServiceRequests.AddAsync(request);
        await _dbContext.SaveChangesAsync();

        return request;
    }

    public async Task<CustomerServiceRequest?> UpdateAsync(int id, CustomerServiceRequest request, string updatedBy)
    {
        var existingRequest = await _dbContext.CustomerServiceRequests.FindAsync(id);
        if (existingRequest == null)
            return null;

        existingRequest.CustomerId = request.CustomerId;
        existingRequest.SetupRaicoServiceId = request.SetupRaicoServiceId;
        existingRequest.UpdatedAt = DateTime.UtcNow;
        existingRequest.UpdatedBy = updatedBy;
        
        await _dbContext.SaveChangesAsync();
        return existingRequest;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var existingRequest = await _dbContext.CustomerServiceRequests.FindAsync(id);
        if (existingRequest == null)
            return false;

        _dbContext.CustomerServiceRequests.Remove(existingRequest);
        await _dbContext.SaveChangesAsync();
        return true;
    }

    public async Task<bool> UpdateStatusAsync(int id, CustomerServiceRequestStatus status, string updatedBy)
    {
        var existingRequest = await _dbContext.CustomerServiceRequests.FindAsync(id);
        if (existingRequest == null)
            return false;

        existingRequest.Status = status;
        existingRequest.UpdatedAt = DateTime.UtcNow;
        existingRequest.UpdatedBy = updatedBy;
        
        await _dbContext.SaveChangesAsync();
        return true;
    }
}