using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Resources;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Raiconet.Cotizador.Api.Services.TemplateRender;
using Raiconet.Cotizador.Api.Services.TemplateRender.Models;
using Resend;
using Serilog;

namespace Raiconet.Cotizador.Api.Services.MailSender;

public class ResendMailSender(
    IResend resend,
    ITemplateRender templateRender,
    AppSettings settings,
    ICultureProvider cultureProvider
) : IEmailSender
{
   

    private async Task SendEmailAsync<TModel>(
        string to,
        string subjectResourceKey,
        TemplateMetadata<TModel> template,
        TModel model
    ) where TModel : class
    {
        var culture = cultureProvider.GetCurrentCulture();
        var subject = EmailResources.ResourceManager.GetString(
            subjectResourceKey,
            culture
        );

        var htmlContent = templateRender.Render(template, model);

        var message = new EmailMessage();
        message.From = settings.FromEmail;
        message.To.Add(to);
        if (subject != null) message.Subject = subject;
        message.HtmlBody = htmlContent;

        await resend.EmailSendAsync(message);
    }

    public async Task SendEmailVerificationCodeAsync(string email, string code, string expirationTime)
    {
        try
        {
            var model = new ConfirmEmailModel(email, code, expirationTime);
            await SendEmailAsync(
                email,
                "ConfirmEmail_Subject",
                EmailTemplates.ConfirmEmail,
                model
            );
        }
        catch (Exception e)
        {
            Log.Error(e, "Error sending confirmation link");
        }

    }

    public async Task SendPasswordResetCodeAsync(string email, string code, string expirationTime)
    {
        try
        {
            var model = new ResetPasswordModel(email, code, expirationTime);
            await SendEmailAsync(
                email,
                "ResetPassword_Subject",
                EmailTemplates.ResetPassword,
                model
            );
        }
        catch (Exception e)
        {
            Log.Error(e, "Error sending password reset link");
        }
    }

    public async Task SendWelcomeEmailAsync(string email)
    {
        try
        {
            var model = new WelcomeModel(email, $"https://raiconetcotizadoronline.com/complete-profile" );
            await SendEmailAsync(
                email,
                "Welcome_Subject",
                EmailTemplates.Welcome,
                model
            );
        }
        catch (Exception e)
        {
            Log.Error(e, "Error sending password reset link");
        }
    }
}