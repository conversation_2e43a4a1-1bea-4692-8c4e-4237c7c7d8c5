using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Services.RejectionReasonService;

public interface IRejectionReasonService
{
    Task<RejectionReason?> GetByIdAsync(int id);
    Task<List<RejectionReason>> GetAllAsync(string? searchTerm = null);
    Task<RejectionReason> CreateAsync(RejectionReason rejectionReason);
    Task<RejectionReason?> UpdateAsync(int id, RejectionReason rejectionReason);
    Task<bool> DeleteAsync(int id);
    Task<bool> IsInUseAsync(int id);
}

public class RejectionReasonService : IRejectionReasonService
{
    private readonly AppDbContext _dbContext;

    public RejectionReasonService(AppDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<RejectionReason?> GetByIdAsync(int id)
    {
        return await _dbContext.RejectionReasons
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    public async Task<List<RejectionReason>> GetAllAsync(string? searchTerm = null)
    {
        var query = _dbContext.RejectionReasons.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            searchTerm = searchTerm.ToLower();
            query = query.Where(r => 
                r.Name.ToLower().Contains(searchTerm) || 
                (r.Description != null && r.Description.ToLower().Contains(searchTerm)));
        }

        return await query.OrderBy(r => r.Name).ToListAsync();
    }

    public async Task<RejectionReason> CreateAsync(RejectionReason rejectionReason)
    {
        await _dbContext.RejectionReasons.AddAsync(rejectionReason);
        await _dbContext.SaveChangesAsync();
        
        return rejectionReason;
    }

    public async Task<RejectionReason?> UpdateAsync(int id, RejectionReason rejectionReason)
    {
        var existingReason = await _dbContext.RejectionReasons.FindAsync(id);
        if (existingReason == null)
            return null;

        existingReason.Name = rejectionReason.Name;
        existingReason.Description = rejectionReason.Description;
        
        await _dbContext.SaveChangesAsync();
        return existingReason;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var existingReason = await _dbContext.RejectionReasons.FindAsync(id);
        if (existingReason == null)
            return false;

        // Verificar que no está en uso
        if (await IsInUseAsync(id))
            return false;

        _dbContext.RejectionReasons.Remove(existingReason);
        await _dbContext.SaveChangesAsync();
        return true;
    }

    public async Task<bool> IsInUseAsync(int id)
    {
        return await _dbContext.QuotationResults
            .AnyAsync(q => EF.Property<int?>(q, "RejectionReasonId") == id);
    }
}