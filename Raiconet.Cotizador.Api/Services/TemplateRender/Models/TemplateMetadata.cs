using System.Globalization;

namespace Raiconet.Cotizador.Api.Services.TemplateRender.Models;

public class TemplateMetadata<TModel> where TModel : class
{
    private readonly string _baseFileName;
    public EmailTemplate Template { get; }
    public Type ModelType { get; }

    private TemplateMetadata(EmailTemplate template, string baseFileName)
    {
        Template = template;
        _baseFileName = baseFileName;
        ModelType = typeof(TModel);
    }
    
    public string GetFileName(CultureInfo culture)
    {
        return $"{_baseFileName}.{culture.Name}.hbs";
    }

    public static TemplateMetadata<TModel> Create(EmailTemplate template)
    {
        var baseFileName = template.ToString();
        return new TemplateMetadata<TModel>(template, baseFileName);
    }
}