namespace Raiconet.Cotizador.Api.Services.TemplateRender.Models;

public enum EmailTemplate
{
    ConfirmEmail,
    ResetPassword,
    Welcome
}

public record ConfirmEmailModel(string UserName, string Code, string ExpirationTime);
public record ResetPasswordModel(string UserName, string Code, string ExpirationTime);
public record WelcomeModel(string UserName, string ProfileUrl);

public static class EmailTemplates
{
    public static readonly TemplateMetadata<ConfirmEmailModel> ConfirmEmail = 
        TemplateMetadata<ConfirmEmailModel>.Create(EmailTemplate.ConfirmEmail);

    public static readonly TemplateMetadata<ResetPasswordModel> ResetPassword = 
        TemplateMetadata<ResetPasswordModel>.Create(EmailTemplate.ResetPassword);
    
    public static readonly TemplateMetadata<WelcomeModel> Welcome = 
        TemplateMetadata<WelcomeModel>.Create(EmailTemplate.Welcome); 
}