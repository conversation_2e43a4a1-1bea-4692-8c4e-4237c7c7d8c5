using System.Globalization;
using System.Reflection;
using HandlebarsDotNet;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Raiconet.Cotizador.Api.Services.TemplateRender.Models;
using Serilog;

namespace Raiconet.Cotizador.Api.Services.TemplateRender;

public class HandlebarsTemplateRender(
    IHandlebars handlebars,
    ICultureProvider cultureProvider,
    ITemplateCache templateCache
) : ITemplateRender
{

    public string Render<TModel>(
        TemplateMetadata<TModel> template,
        TModel model) where TModel : class
    {
        var culture = cultureProvider.GetCurrentCulture();
        var templateContent = GetTemplate(template, culture);
        var compiledTemplate = handlebars.Compile(templateContent);
        return compiledTemplate(model);
    }

    private string GetTemplate<TModel>(
        TemplateMetadata<TModel> template,
        CultureInfo culture
    ) where TModel : class
    {
        var cacheKey = $"{template.Template}_{culture.Name}";

        if (templateCache.TryGetTemplate(cacheKey, out var templateContent))
        {
            return templateContent;
        }

        var fileName = template.GetFileName(culture);
        var templatePath = Path.Combine(AppContext.BaseDirectory, "Templates", fileName);

        if (!File.Exists(templatePath) && culture.Name != "es")
        {
            // Fallback to default culture (Spanish)
            return GetTemplate(template, new CultureInfo("es"));
        }

        if (!File.Exists(templatePath))
        {
            throw new FileNotFoundException($"Template file not found: {templatePath}");
        }

        templateContent = File.ReadAllText(templatePath);
        templateCache.SetTemplate(cacheKey, templateContent);

        return templateContent;
    }
    private void RegisterHelpers()
    {
        handlebars.RegisterHelper("formatDate", (context, args) =>
        {
            if (args[0] is DateTime date)
            {
                return date.ToString("d", cultureProvider.GetCurrentCulture());
            }

            return string.Empty;
        });

        handlebars.RegisterHelper("currentYear", (context, args) => DateTime.Now.Year.ToString());
    }
}