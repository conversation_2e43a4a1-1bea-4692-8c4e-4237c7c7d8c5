namespace Raiconet.Cotizador.Api.Services.TemplateRender;

public interface ITemplateCache
{
    bool TryGetTemplate(string key, out string template);
    void SetTemplate(string key, string template);
}

public class TemplateCache : ITemplateCache
{
    private readonly Dictionary<string, string?> _cache = new();
    private readonly object _lock = new();

    public bool TryGetTemplate(string key, out string template)
    {
        lock (_lock)
        {
            return _cache.TryGetValue(key, out template!);
        }
    }

    public void SetTemplate(string key, string template)
    {
        lock (_lock)
        {
            _cache[key] = template;
        }
    }
}
