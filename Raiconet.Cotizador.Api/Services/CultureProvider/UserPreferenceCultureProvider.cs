using System.Globalization;

namespace Raiconet.Cotizador.Api.Services.CultureProvider;

public class UserPreferenceCultureProvider(IHttpContextAccessor httpContextAccessor) 
    : ICultureProvider
{
    public CultureInfo GetCurrentCulture()
    {
        var acceptLanguage = httpContextAccessor.HttpContext?.Request.Headers["Accept-Language"]
            .FirstOrDefault();

        var culture = acceptLanguage?.Split(',', ';')
            .FirstOrDefault()
            ?.Trim()
            .Split('-')
            .FirstOrDefault() ?? "es";

        try
        {
            return new CultureInfo(culture);
        }
        catch (CultureNotFoundException)
        {
            return new CultureInfo("es");
        }
    }
}