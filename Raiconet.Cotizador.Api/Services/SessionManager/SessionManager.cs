using Microsoft.AspNetCore.Identity;
using Raiconet.Cotizador.Api.Domain;
using Serilog;

namespace Raiconet.Cotizador.Api.Services.SessionManager;

public class SessionManager(IHttpContextAccessor httpContextAccessor, UserManager<User> userManager)
    : ISessionManager
{
    public async Task<User?> GetCurrentUserAsync()
    {
        try
        {
            var userClaim = httpContextAccessor.HttpContext?.User;
            if (userClaim == null) return null;
            var user = await userManager.GetUserAsync(userClaim);
            return user;
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message); 
            throw;
        }
    }
}