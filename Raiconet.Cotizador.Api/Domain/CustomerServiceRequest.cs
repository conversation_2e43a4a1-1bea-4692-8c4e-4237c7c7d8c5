namespace Raiconet.Cotizador.Api.Domain;

public class CustomerServiceRequest
{
    public int Id { get; set; }
    public int CustomerId { get; set; }
    public Customer Customer { get; set; }
    public int SetupRaicoServiceId { get; set; }
    public SetupRaicoService  SetupRaicoService { get; set; }
    public CustomerServiceRequestStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public User Creator { get; set; }
    public string CreatedById { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string UpdatedBy { get; set; }
}

public enum CustomerServiceRequestStatus: byte
{
    Pending = 0,
    Accepted = 1,
    Rejected = 2,
    Created = 3
}