using System.Security.Cryptography;

namespace Raiconet.Cotizador.Api.Domain;

public class Otp
{
    public const int DefaultCodeLength = 6;
    public const int DefaultExpirationMinutes = 15;
    public const int MaxVerificationAttempts = 3;

    public int Id { get; private set; }
    public string Code { get; init; } = null!;
    public DateTime ExpiresAt { get; init; }
    public DateTime CreatedAt { get; private set; }
    public int VerificationAttempts { get; private set; }
    public bool IsVerified { get; private set; }
    public string Email { get; private set; } = null!;


    private Otp()
    {
        // For EF Core
    }

    public static Otp Create(string email)
    {
        return new Otp
        {
            Code = GenerateCode(),
            CreatedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddMinutes(DefaultExpirationMinutes),
            VerificationAttempts = 0,
            IsVerified = false,
            Email = email
        };
    }

    private static string GenerateCode()
    {
        using var rng = RandomNumberGenerator.Create();
        byte[] randomBytes = new byte[4];

        rng.GetBytes(randomBytes);
        var result = BitConverter.ToInt32(randomBytes, 0);

        result = Math.Abs(result) % (int)Math.Pow(10, DefaultCodeLength);

        return result.ToString().PadLeft(DefaultCodeLength, '0');
    }
    
    public bool IsExpired()
    {
        return DateTime.Compare(DateTime.UtcNow, ExpiresAt) > 0;
    }

    public OtpVerificationResult Verify(string code)
    {
        if (IsVerified)
        {
            return OtpVerificationResult.AlreadyVerified;
        }

        if (IsExpired())
        {
            return OtpVerificationResult.Expired;
        }

        if (VerificationAttempts >= MaxVerificationAttempts)
        {
            return OtpVerificationResult.MaxAttemptsReached;
        }

        VerificationAttempts++;

        if (Code != code)
        {
            return OtpVerificationResult.Invalid;
        }

        IsVerified = true;
        return OtpVerificationResult.Success;
    }

    public enum OtpVerificationResult
    {
        Success,
        Invalid,
        Expired,
        MaxAttemptsReached,
        AlreadyVerified
    }
}