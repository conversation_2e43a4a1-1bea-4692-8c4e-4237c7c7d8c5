namespace Raiconet.Cotizador.Api.Domain;

public class Address
{
    public int Id { get; set; }
    public int CustomerId { get; set; }
    public Customer Customer { get; set; }
    public string? CountryCode { get; set; }
    public string? Province { get; set; }
    public string? City { get; set; }
    public string? StreetAddress { get; set; }
    public string? AddressComments { get; set; }
    public string? PostalCode { get; set; }
    public string? Phone { get; set; }
    public string? Mobile { get; set; }
    
    public string? Contact { get; set; }
    public string? LegalAddress { get; set; }
    public string? CommercialAddress { get; set; }
    public string? CommercialContact1 { get; set; }
    public string? BillingAddress { get; set; }
    public string? RedispatchAddress { get; set; }
    public string? DeliveryAddress { get; set; }
    public string? DeliveryContact1 { get; set; }
}