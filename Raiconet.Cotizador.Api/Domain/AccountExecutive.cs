namespace Raiconet.Cotizador.Api.Domain;

public class AccountExecutive
{
    public int Id { get; set; }
    public User? User { get; set; }
    
    public string? UserId { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public int AccountExecutiveTypeId { get; set; }
    public AccountExecutiveType Type { get; set; }
    public ICollection<Customer> Customers { get; set; }
}