namespace Raiconet.Cotizador.Api.Domain;

public class AdditionalService
{
    public int Id { get; set; }
    public string Code { get; set; }
    public string Description { get; set; }
    public string Type { get; set; }
    public decimal? MinimumValue { get; set; }
    public decimal UpsValue { get; set; }
    public decimal DhlValue { get; set; }
    public decimal FedexValue { get; set; }
    public bool IsActive { get; set; }
}