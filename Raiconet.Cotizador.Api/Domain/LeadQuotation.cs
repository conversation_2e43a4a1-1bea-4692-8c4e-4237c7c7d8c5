namespace Raiconet.Cotizador.Api.Domain;

public class LeadQuotation
{
    public int Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string QuotationRequestData { get; set; } = string.Empty; // JSON field
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
