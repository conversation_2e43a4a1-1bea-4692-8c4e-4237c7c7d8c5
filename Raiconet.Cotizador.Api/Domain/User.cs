using Microsoft.AspNetCore.Identity;

namespace Raiconet.Cotizador.Api.Domain;

public class User: IdentityUser
{
    public bool IsEnabled { get; private set; } = false;
    public int UserProfileId { get; set; }
    public UserProfile? UserProfile { get; set; }
    public ICollection<QuotationResult> CreatedQuotations { get; set; }

    public int? TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    public void UpdateStatus(bool status)
    {
        IsEnabled = status;
    }
}