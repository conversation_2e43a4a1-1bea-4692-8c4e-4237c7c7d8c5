using Ardalis.GuardClauses;

namespace Raiconet.Cotizador.Api.Domain;

public class UserProfile
{

    private UserProfile()
    {
        //EF Core constructor
    }
    
    public int Id { get; private set; }
    public string FirstName { get; private set; }
    public string LastName { get; private set; }
    public UserAddress Address { get; private set; }
    public string UserId { get; private set; }
    public User User { get; private set; }

    public static UserProfile Create(
        string firstName, string lastName, UserAddress address, User user)
    {
        Guard.Against.NullOrWhiteSpace(firstName, nameof(firstName));
        Guard.Against.NullOrWhiteSpace(lastName, nameof(lastName));
        Guard.Against.Null(address, nameof(address));
        Guard.Against.Null(user, nameof(user));

        return new UserProfile()
        {
            FirstName = firstName,
            LastName = lastName,
            Address = address,
            User = user,
        };
    }
}

public class UserAddress
{
    private UserAddress()
    {
        // EF Core constructor
    }
    
    public string Country { get; private set; }
    public string AddressLine1 { get; private set; }
    public string? AddressLine2 { get; private set; }
    public string? PostalCode { get; private set; }
    public string City { get; private set; }

    public static UserAddress Create(
        string country, string addressLine1, string? addressLine2, string? postalCode, string city)
    {
        Guard.Against.NullOrWhiteSpace(country, nameof(country));
        Guard.Against.NullOrWhiteSpace(addressLine1, nameof(addressLine1));
        Guard.Against.NullOrWhiteSpace(city, nameof(city));

        return new UserAddress()
        {
            Country = country,
            AddressLine1 = addressLine1,
            AddressLine2 = addressLine2,
            PostalCode = postalCode,
            City = city
        };
    }
}