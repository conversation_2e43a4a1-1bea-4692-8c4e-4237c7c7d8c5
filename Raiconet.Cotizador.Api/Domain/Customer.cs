namespace Raiconet.Cotizador.Api.Domain;

public class Customer
{
    public int Id { get; set; }
    public string? UserId { get; set; }
    public User? User{ get; set; }
    public int? TenantId { get; set; }
    public Tenant? Tenant{ get; set; }
    public string TaxId { get; set; }
    public string BusinessName { get; set; }
    
    public int? CustomerCode { get; set; }
    public string Email { get; set; }
    public bool? TwoFactorEnabled { get; set; }
    
    public string? AccountExecutiveUserId { get; set; }
    public User? AccountExecutiveUser { get; set; }
    public int? AccountExternalExecutiveId { get; set; }
    public AccountExecutive? AccountExternalExecutive { get; set; }
    public int CustomerTypeId { get; set; }
    public CustomerType Type { get; set; }
    public int PaymentTermId { get; set; }
    public int PaymentTerm { get; set; }
    public decimal? ComissionFee { get; set; }
    public ICollection<CustomerBillingReason> CustomerBillingReason { get; set; }
    public ICollection<Address> Addresses { get; set; }
    public ICollection<CustomerFile> Files { get; set; }
}