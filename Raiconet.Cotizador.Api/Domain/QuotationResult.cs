using System.Text.Json.Serialization;
using Raiconet.Cotizador.Api.Services.Quotations;

namespace Raiconet.Cotizador.Api.Domain;

public class QuotationResult
{
    public int Id { get; set; }
    public int QuotationNumber { get; set; }
    // Propiedad de navegación
    [JsonIgnore]
    public User Creator { get; set; }
    
    public string CreatedById { get; set; }
    public DateTime CreatedAt { get; set; }
    
    public string? UpdatedBy { get; set; } 
    public User? UpdatedByUser { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public decimal TotalPackageValue { get; set; }
    public string CountryOfOrigin { get; set; }
    public string? Type { get; set; }
    public int ServiceTypeId { get; set; }
    public string CustomerName { get; set; }
    public TotalsByService? QuotationPayload {get; set; }
    public Dictionary<string,string>? CustomQuotationPayload {get; set; }
    public QuotationStatus Status { get; set; }
    public List<Package> Packages { get; set; }

    public RejectionReason? RejectionReason { get; set; }
    public int? RejectionReasonId { get; set; }
    public string? Notes { get; set; }
    public string? Description { get; set; }
    public string Code { get; set; }
    public decimal? DutyPercentage { get; set; }
    public decimal? DutyPercentageSuggested { get; set; }
    public decimal? FobPercentage { get; set; }
    public bool ApprovedByClient { get; set; } = false;
}

public enum QuotationStatus: byte
{
    Pending = 0,
    Accepted = 1,
    Rejected = 2,
    Created = 3
}