namespace Raiconet.Cotizador.Api.Domain;
public class BillingReasons
{
    public int Id { get; set; }
    public int Code { get; set; }
    public string Description { get; set; }
    public BillingType Type { get; set; }
    public decimal? CifFreightRate { get; set; }
    public ICollection<BillingConcepts> BillingConcepts { get; set; }
    public ICollection<CustomerBillingReason> CustomerBillingReason { get; set; }
    public SetupRaicoService? SetupRaicoService { get; set; }
    public int? SetupRaicoServiceId { get; set; }
}

public enum BillingType
{
    Import = 1,
    Export = 2
}