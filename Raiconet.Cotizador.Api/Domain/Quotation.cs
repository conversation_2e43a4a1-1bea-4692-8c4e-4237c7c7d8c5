namespace Raiconet.Cotizador.Api.Domain;

public class Quotation
{
    public int Id { get; set; }
    public int ServiceTypeId { get; set; }
    public ServiceType ServiceType { get; set; }
    public int CustomerId { get; set; }
    public Customer Customer { get; set; }
    public int SetupZonesId { get; set; }
    public SetupZones SetupZones { get; set; }
    public ICollection<Package> Packages { get; set; }
    public int AditionalServiceId { get; set; }
    public AdditionalService AditionalService { get; set; }
}