using Raiconet.Cotizador.Api.Features.Account.Handlers;
using SharpGrip.FluentValidation.AutoValidation.Endpoints.Extensions;

namespace Raiconet.Cotizador.Api.Features.Account;

public static class AccountEndpoints
{
    public static void UseAccountEndpoints(this WebApplication app)
    {
        var auth = app.MapGroup("auth").AddFluentValidationAutoValidation().WithTags("Auth");
        auth.MapPost("/email-verification-code", RequestEmailVerificationCodeHandler.Handler);
        auth.MapPost("/password-reset-code", RequestPasswordResetCodeHandler.Handler);
        auth.MapPost("/reset-password", ResetPasswordHandler.Handler);
        auth.MapPost("/register", RegisterHandler.Handler);
        auth.MapPost("/login", LoginHandler.Handler);
        auth.MapPost("/refresh", RefreshHandler.Handler);

        var profile = app.MapGroup("profile")
            .RequireAuthorization()
            .AddFluentValidationAutoValidation()
            .WithTags("Profile");
        profile.MapPost("", CreateUserProfile.Handler);
        profile.MapGet("", GetUserProfile.Handler);
        profile.MapGet("all", GetAllUsers.Handler)
            .WithName("GetUsers")
            .WithDescription("Get all users with pagination, filtering and roles");
        profile.MapGet("by-role", GetUsersByRole.Handler)
            .WithName("GetUsersByRole")
            .WithDescription("Get all users with a specific role");
        profile.MapPatch("{userId}/role", UpdateUserStatus.Handler)
            .WithName("UpdateUserRole")
            .WithDescription("Update user role and enabled status");
        profile.MapGet("roles", GetRoles.Handler)
            .WithName("GetAllRoles")
            .WithDescription("Get all available roles");
    }
}