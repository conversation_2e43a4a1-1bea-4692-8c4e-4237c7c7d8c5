using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Raiconet.Cotizador.Api.Services.SessionManager;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;
using Serilog;


namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class GetUserProfile
{
    public static async Task<Results<Ok<UserProfileResponse>, ProblemHttpResult, ValidationProblem>> Handler(
        [FromServices] AppDbContext dbContext,
        [FromServices] ICultureProvider cultureProvider,
        [FromServices] ISessionManager sessionManager,
        [FromServices] UserManager<User> userManager,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            var user = await sessionManager.GetCurrentUserAsync();
            if (user == null)
            {
                throw new InvalidOperationException("user should be logged in");
            }

            var roles = await userManager.GetRolesAsync(user);
            
            var userProfile = await dbContext.UserProfiles
                .AsNoTracking().FirstOrDefaultAsync(up => up.UserId == user.Id, cancellationToken: cancellationToken);

            var tenantName = await dbContext.Tenants.FirstOrDefaultAsync(x => x.Id == user.TenantId);

            if (userProfile == null)
            {
                return TypedResults.Ok(new UserProfileResponse(
                    UserId: user.Id,
                    FirstName: null,
                    LastName: null,
                    Email: user.Email!,
                    PhoneNumber: null,
                    IsProfileCompleted: false,
                    Role: roles.FirstOrDefault(),
                    IsEnabled:user.IsEnabled,
                    Tenant:user.TenantId,
                    TenantName:  tenantName?.Name
                ));
            }

            return TypedResults.Ok(new UserProfileResponse(
                UserId: user.Id,
                FirstName: userProfile.FirstName,
                LastName: userProfile.LastName,
                Email: user.Email!,
                PhoneNumber: user.PhoneNumber,
                IsProfileCompleted: true,
                Role: roles.FirstOrDefault(),
                IsEnabled:user.IsEnabled,
                Tenant:user.TenantId,
                TenantName:tenantName?.Name
            ));
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            return TypedResults.Problem(
                title: ResourceManager.GetString("ServerError", culture),
                detail: ResourceManager.GetString("ServerErrorDesc", culture),
                statusCode: StatusCodes.Status500InternalServerError
            );
        }
    }
}

public record UserProfileResponse(
    string UserId,
    string? FirstName,
    string? LastName,
    string Email,
    string? PhoneNumber,
    bool IsProfileCompleted,
    string Role,
    bool IsEnabled,
    int? Tenant,
    string? TenantName
);