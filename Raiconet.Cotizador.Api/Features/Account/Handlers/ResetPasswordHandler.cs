using System.Diagnostics;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class ResetPasswordHandler
{
    public static async Task<Results<Ok, ValidationProblem, ProblemHttpResult>> Handler(
        [FromBody] CustomResetPasswordRequest req,
        [FromServices] UserManager<User> userManager,
        [FromServices] AppDbContext dbContext,
        [FromServices] ICultureProvider cultureProvider,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        var user = await userManager.FindByEmailAsync(req.Email);

        if (user is null || !(await userManager.IsEmailConfirmedAsync(user)))
        {
            // Don't reveal that the user does not exist or is not confirmed, so don't return a 200 if we would have
            // returned a 400 for an invalid code given a valid user email.
            return CreateValidationProblem(IdentityResult.Failed(userManager.ErrorDescriber.InvalidToken()));
        }

        IdentityResult result;
        try
        {
            var otp = await dbContext.Otps.FirstOrDefaultAsync(
                t => t.Email == req.Email && t.IsVerified == false, cancellationToken: cancellationToken);

            if (otp == null)
            {
                return TypedResults.Problem(
                    statusCode: StatusCodes.Status404NotFound,
                    title: ResourceManager.GetString("InvalidOtp", culture),
                    detail: ResourceManager.GetString("InvalidOtpDesc", culture)
                );
            }

            var verificationResult = otp.Verify(req.Code);
            if (verificationResult != Otp.OtpVerificationResult.Success)
            {
                return TypedResults.Problem(
                    statusCode: StatusCodes.Status400BadRequest,
                    title: ResourceManager.GetString("InvalidOtp", culture),
                    detail: ResourceManager.GetString("InvalidOtpDesc", culture)
                );
            }

            await userManager.RemovePasswordAsync(user);
            result = await userManager.AddPasswordAsync(user, req.Password);
        }
        catch (FormatException)
        {
            result = IdentityResult.Failed(userManager.ErrorDescriber.InvalidToken());
        }

        if (!result.Succeeded)
        {
            var errors = result.Errors
                .GroupBy(e => e.Code)
                .ToDictionary(g => g.Key, g => g.Select(e => e.Description).ToArray());
            return TypedResults.ValidationProblem(errors);
        }

        await dbContext.SaveChangesAsync(cancellationToken);
        return TypedResults.Ok();
    }

    private static ValidationProblem CreateValidationProblem(IdentityResult result)
    {
        // We expect a single error code and description in the normal case.
        // This could be golfed with GroupBy and ToDictionary, but perf! :P
        Debug.Assert(!result.Succeeded);
        var errorDictionary = new Dictionary<string, string[]>(1);

        foreach (var error in result.Errors)
        {
            string[] newDescriptions;

            if (errorDictionary.TryGetValue(error.Code, out var descriptions))
            {
                newDescriptions = new string[descriptions.Length + 1];
                Array.Copy(descriptions, newDescriptions, descriptions.Length);
                newDescriptions[descriptions.Length] = error.Description;
            }
            else
            {
                newDescriptions = [error.Description];
            }

            errorDictionary[error.Code] = newDescriptions;
        }

        return TypedResults.ValidationProblem(errorDictionary);
    }
}

public record CustomResetPasswordRequest(string Code, string Email, string Password);