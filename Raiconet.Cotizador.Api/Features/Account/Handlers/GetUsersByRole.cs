using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Account.Handlers;
using Raiconet.Cotizador.Api.Services.CultureProvider;

public static class GetUsersByRole
{
    public static async Task<Results<Ok<List<UserResponse>>, BadRequest<string>, ProblemHttpResult>> Handler(
        [FromQuery] string role,
        [FromQuery] string? q,
        [FromServices] AppDbContext dbContext,
        [FromServices] ICultureProvider cultureProvider,
        [FromServices] UserManager<User> userManager,
        [FromServices] RoleManager<IdentityRole> roleManager,
        [FromServices] ILogger logger,
        CancellationToken cancellationToken
    )
    {
        try
        {
            if (string.IsNullOrWhiteSpace(role))
            {
                return TypedResults.BadRequest("Role is required");
            }

            // Buscar el rol exacto en la base de datos
            var existingRole = await roleManager.Roles
                .FirstOrDefaultAsync(r => r.NormalizedName.ToUpper() == role.ToUpper(), cancellationToken);

            if (existingRole == null)
            {
                return TypedResults.BadRequest("Role does not exist");
            }

            var usersInRole = await userManager.GetUsersInRoleAsync(existingRole.Name);

            if (!usersInRole.Any())
            {
                return TypedResults.Ok(new List<UserResponse>());
            }

            // Obtener los IDs de los usuarios
            var userIds = usersInRole.Select(u => u.Id).ToList();

            // Obtener los perfiles de los usuarios con sus tenants
            var userProfiles = await dbContext.UserProfiles
                .AsNoTracking()
                .Include(up => up.User)
                .Where(up =>
                    userIds.Contains(up.UserId) && (q == null || up.FirstName.Contains(q) || up.LastName.Contains(q) ||
                                                    up.User.Email.Contains(q)))
                .OrderBy(up => up.FirstName)
                .ToListAsync(cancellationToken);

            // Obtener los IDs de tenants para hacer una sola consulta
            var tenantIds = userProfiles.Where(up => up.User.TenantId != null)
                                        .Select(up => up.User.TenantId)
                                        .Distinct()
                                        .ToList();

            // Cargar los tenants en una sola consulta para optimizar
            var tenants = await dbContext.Tenants
                .Where(t => tenantIds.Contains(t.Id))
                .ToDictionaryAsync(t => t.Id, t => t.Name, cancellationToken);

            var userResponses = userProfiles.Select(up => new UserResponse(
                UserId: up.UserId,
                FirstName: up.FirstName,
                LastName: up.LastName,
                Email: up.User.Email!,
                PhoneNumber: up.User.PhoneNumber,
                IsProfileCompleted: true,
                Role: existingRole.Name,
                IsEnabled: up.User.IsEnabled,
                Tenant: up.User.TenantId,
                TenantName: up.User.TenantId.HasValue && tenants.ContainsKey(up.User.TenantId.Value) 
                    ? tenants[up.User.TenantId.Value] 
                    : null
            )).ToList();

            return TypedResults.Ok(userResponses);
        }
        catch (Exception e)
        {
            logger.LogError(
                LoggerEvents.UnhandledException,
                e,
                "Users retrieval failed");
            throw;
        }
    }
}