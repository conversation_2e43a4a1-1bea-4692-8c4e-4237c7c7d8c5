using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Raiconet.Cotizador.Api.Services.MailSender;
using Serilog;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class RegisterHandler
{
    public static async Task<Results<Created, ValidationProblem, ProblemHttpResult>> Handler(
        RegisterRequest req,
        [FromServices] AppDbContext dbContext,
        [FromServices] UserManager<User> userManager,
        [FromServices] IUserStore<User> userStore,
        [FromServices] ICultureProvider cultureProvider,
        [FromServices] IEmailSender emailSender,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            var emailStore = (IUserEmailStore<User>)userStore;
            var otp = await dbContext.Otps
                .OrderByDescending(o => o.CreatedAt)
                .FirstOrDefaultAsync(t => t.Email == req.Email && t.IsVerified == false,
                    cancellationToken: cancellationToken);

            if (otp == null)
            {
                return TypedResults.Problem(
                    statusCode: StatusCodes.Status404NotFound,
                    title: ResourceManager.GetString("InvalidOtp", culture),
                    detail: ResourceManager.GetString("InvalidOtpDesc", culture)
                );
            }

            var verificationResult = otp.Verify(req.Code);
            if (verificationResult != Otp.OtpVerificationResult.Success)
            {
                return TypedResults.Problem(
                    statusCode: StatusCodes.Status400BadRequest,
                    title: ResourceManager.GetString("InvalidOtp", culture),
                    detail: ResourceManager.GetString("InvalidOtpDesc", culture)
                );
            }

            var user = new User { EmailConfirmed = true };
            await userStore.SetUserNameAsync(user, req.Email, CancellationToken.None);
            await emailStore.SetEmailAsync(user, req.Email, CancellationToken.None);
            var result = await userManager.CreateAsync(user, req.Password);

            if (!result.Succeeded)
            {
                var errors = result.Errors
                    .GroupBy(e => e.Code)
                    .ToDictionary(g => g.Key, g => g.Select(e => e.Description).ToArray());
                return TypedResults.ValidationProblem(errors);
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            await emailSender.SendWelcomeEmailAsync(req.Email);
            return TypedResults.Created();
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            return TypedResults.Problem(
                statusCode: StatusCodes.Status500InternalServerError,
                title: ResourceManager.GetString("ServerError", culture),
                detail: ResourceManager.GetString("ServerErrorDesc", culture)
            );
        }
    }
}

public record RegisterRequest(string Email, string Code, string Password);

public class RegisterRequestValidator : AbstractValidator<RegisterRequest>
{
    public RegisterRequestValidator(ICultureProvider cultureProvider)
    {
        var culture = cultureProvider.GetCurrentCulture();
        RuleFor(x => x.Email).EmailAddress()
            .WithMessage(ResourceManager.GetString("EmailInvalid", culture));
        RuleFor(x => x.Code).NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture));
        RuleFor(x => x.Password).NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture));
    }
}