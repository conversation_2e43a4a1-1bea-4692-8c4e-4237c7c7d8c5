using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Serilog;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class LoginHandler
{
    public static async Task<Results<EmptyHttpResult, ValidationProblem, ProblemHttpResult>> Handler(
        [FromBody] LoginRequest req,
        [FromServices] SignInManager<User> signInManager,
        [FromServices] ICultureProvider cultureProvider,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            signInManager.AuthenticationScheme = IdentityConstants.BearerScheme;
            var result = await signInManager.PasswordSignInAsync(
                req.Email,
                req.Password,
                isPersistent: false,
                lockoutOnFailure: false);

            if (result.RequiresTwoFactor)
            {
                if (!string.IsNullOrEmpty(req.TwoFactorCode))
                {
                    result = await signInManager.TwoFactorAuthenticatorSignInAsync(req.TwoFactorCode,
                        isPersistent: false, rememberClient: false);
                }
                else if (!string.IsNullOrEmpty(req.TwoFactorRecoveryCode))
                {
                    result = await signInManager.TwoFactorRecoveryCodeSignInAsync(req.TwoFactorRecoveryCode);
                }
            }

            if (!result.Succeeded)
            {
                return TypedResults.Problem(
                    title: ResourceManager.GetString("InvalidCredentials", culture),
                    detail: ResourceManager.GetString("InvalidCredentialsDesc", culture),
                    statusCode: StatusCodes.Status400BadRequest
                );
            }

            // The signInManager already produced the needed response in the form of a cookie or bearer token.
            return TypedResults.Empty;
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            return TypedResults.Problem(
                title: ResourceManager.GetString("ServerError", culture),
                detail: ResourceManager.GetString("ServerErrorDesc", culture),
                statusCode: StatusCodes.Status500InternalServerError
            );
        }
    }
}

public class LoginRequestValidator : AbstractValidator<LoginRequest>
{
    public LoginRequestValidator(ICultureProvider cultureProvider)
    {
        var culture = cultureProvider.GetCurrentCulture();
        RuleFor(x => x.Email).EmailAddress()
            .WithMessage(ResourceManager.GetString("EmailInvalid", culture));
        RuleFor(x => x.Password).NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture));
    }
}