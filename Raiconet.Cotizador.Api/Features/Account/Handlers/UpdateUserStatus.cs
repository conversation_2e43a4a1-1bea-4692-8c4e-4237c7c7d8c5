using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class UpdateUserStatus
{
    public static async Task<Results<Ok<UserResponse>, NotFound, BadRequest<string>, ProblemHttpResult>> <PERSON>ler(
        [FromRoute] string userId,
        [FromBody] PatchUserRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] UserManager<User> userManager,
        [FromServices] ICultureProvider cultureProvider,
        [FromServices] ILogger logger,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return TypedResults.NotFound();
            }
            
            if (request.Role != null)
            {
                var roleUpdateResult = await UpdateUserRole(user, request.Role, userManager, dbContext, cancellationToken);
                if (!roleUpdateResult.Succeeded)
                {
                    return TypedResults.BadRequest(roleUpdateResult.Error);
                }
            }
            
            if (request.Role == "TenantOwner" && !string.IsNullOrEmpty(request.TenantName))
            {
                // Verificar si ya existe un tenant con ese nombre
                var existingTenant = await dbContext.Tenants
                    .FirstOrDefaultAsync(t => t.Name == request.TenantName, cancellationToken);
                
                if (existingTenant != null)
                {
                    // Verificar si el tenant ya tiene un TenantOwner
                    var existingOwners = await dbContext.Users
                        .Where(u => u.TenantId == existingTenant.Id)
                        .Join(
                            dbContext.UserRoles,
                            user => user.Id,
                            userRole => userRole.UserId,
                            (user, userRole) => new { user, userRole }
                        )
                        .Join(
                            dbContext.Roles.Where(r => r.Name == "TenantOwner"),
                            joined => joined.userRole.RoleId,
                            role => role.Id,
                            (joined, role) => joined.user
                        )
                        .AnyAsync(cancellationToken);
                    
                    if (existingOwners)
                    {
                        return TypedResults.BadRequest($"Tenant '{request.TenantName}' already has a TenantOwner assigned");
                    }
                    
                    // Asignar el tenant existente al usuario
                    user.TenantId = existingTenant.Id;
                    await userManager.UpdateAsync(user);
                    
                    // Actualizar el customer si se proporciona un ID
                    if (request.CustomerId.HasValue)
                    {
                        var customer = await dbContext.Customers.FirstOrDefaultAsync(
                            x => x.Id == request.CustomerId, cancellationToken);
                        
                        if (customer != null)
                        {
                            customer.TenantId = existingTenant.Id;
                            await dbContext.SaveChangesAsync(cancellationToken);
                        }
                    }
                }
                else
                {
                    // Si no existe, crear uno nuevo
                    Tenant tenant = new Tenant {Name = request.TenantName};
                    await dbContext.Tenants.AddAsync(tenant, cancellationToken);
                    
                    // Guarda los cambios para que Entity Framework genere el Id
                    await dbContext.SaveChangesAsync(cancellationToken);
                    
                    // Asignar el nuevo tenant al usuario
                    user.TenantId = tenant.Id;
                    await userManager.UpdateAsync(user);
                    
                    // Actualizar el customer si se proporciona un ID
                    if (request.CustomerId.HasValue)
                    {
                        var customer = await dbContext.Customers.FirstOrDefaultAsync(
                            x => x.Id == request.CustomerId, cancellationToken);
                        
                        if (customer != null)
                        {
                            customer.TenantId = tenant.Id;
                            await dbContext.SaveChangesAsync(cancellationToken);
                        }
                    }
                }
            }
            else if (request.Role == "TenantClient" && !string.IsNullOrEmpty(request.TenantName))
            {
                var tenant = await dbContext.Tenants.FirstOrDefaultAsync(
                    x => x.Name == request.TenantName, cancellationToken);
                
                if (tenant != null)
                {
                    user.TenantId = tenant.Id;
                    await userManager.UpdateAsync(user);
                }
                else
                {
                    // Si el tenant no existe, retornar un error
                    return TypedResults.BadRequest($"Tenant with name '{request.TenantName}' does not exist");
                }
            }

            if (request.IsEnabled.HasValue)
            {
                var statusUpdateResult = await UpdateUserEnabled(user, request.IsEnabled.Value, userManager);
                if (!statusUpdateResult.Succeeded)
                {
                    return TypedResults.BadRequest(statusUpdateResult.Error);
                }
            }

            // Recargar el usuario con su perfil para la respuesta
            var userProfile = await dbContext.UserProfiles
                .AsNoTracking()
                .FirstOrDefaultAsync(up => up.UserId == userId, cancellationToken);

            var roles = await userManager.GetRolesAsync(user);

            // Obtener el nombre del tenant si existe
            string? tenantName = null;
            if (user.TenantId.HasValue)
            {
                tenantName = await dbContext.Tenants
                    .Where(t => t.Id == user.TenantId.Value)
                    .Select(t => t.Name)
                    .FirstOrDefaultAsync(cancellationToken);
            }

            return TypedResults.Ok(new UserResponse(
                UserId: user.Id,
                FirstName: userProfile?.FirstName,
                LastName: userProfile?.LastName,
                Email: user.Email!,
                PhoneNumber: user.PhoneNumber,
                IsProfileCompleted: userProfile != null,
                Role: roles.FirstOrDefault() ?? string.Empty,
                IsEnabled: user.IsEnabled,
                Tenant: user.TenantId,
                TenantName: tenantName
            ));
        }
        catch (Exception e)
        {
            logger.LogError(
                LoggerEvents.UnhandledException,
                e,
                "User update failed");
            throw;
        }
    }

    private static async Task<(bool Succeeded, string? Error)> UpdateUserRole(
        User user, 
        string newRole, 
        UserManager<User> userManager,
        AppDbContext dbContext,
        CancellationToken cancellationToken)
    {
        // Validar que el rol exista
        var roleExists = await dbContext.Roles
            .AnyAsync(r => r.Name!.ToUpper() == newRole.ToUpper(), cancellationToken);
        
        if (!roleExists)
        {
            return (false, "Invalid role specified");
        }

        var currentRoles = await userManager.GetRolesAsync(user);
        if (currentRoles.Any())
        {
            var removeResult = await userManager.RemoveFromRolesAsync(user, currentRoles);
            if (!removeResult.Succeeded)
            {
                return (false, "Failed to remove current roles");
            }
        }

        var addResult = await userManager.AddToRoleAsync(user, newRole);
        return addResult.Succeeded 
            ? (true, null) 
            : (false, "Failed to add new role");
    }

    private static async Task<(bool Succeeded, string? Error)> UpdateUserEnabled(
        User user,
        bool isEnabled,
        UserManager<User> userManager)
    {
        user.UpdateStatus(isEnabled);
        var result = await userManager.UpdateAsync(user);
        return result.Succeeded 
            ? (true, null) 
            : (false, "Failed to update user status");
    }
}

public record PatchUserRequest
{
    public string? Role { get; init; }
    public bool? IsEnabled { get; init; }
    public string? TenantName { get; init; }
    public int? CustomerId { get; init; }
}