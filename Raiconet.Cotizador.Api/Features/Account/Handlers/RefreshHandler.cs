using FluentValidation;
using Microsoft.AspNetCore.Authentication.BearerToken;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.Data;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Serilog;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class RefreshHandler
{
    public static async Task<
            Results<
                Ok<AccessTokenResponse>,
                UnauthorizedHttpResult,
                SignInHttpResult,
                ChallengeHttpResult,
                ProblemHttpResult
            >
        >
        Handler(
            [FromBody] RefreshRequest req,
            [FromServices] SignInManager<User> signInManager,
            [FromServices] IOptionsMonitor<BearerTokenOptions> bearerTokenOptions,
            [FromServices] TimeProvider timeProvider,
            [FromServices] ICultureProvider cultureProvider,
            CancellationToken cancellationToken
        )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            var refreshTokenProtector = bearerTokenOptions.Get(
                IdentityConstants.BearerScheme).RefreshTokenProtector;
            var refreshTicket = refreshTokenProtector.Unprotect(req.RefreshToken);

            // Reject the /refresh attempt with a 401 if the token expired or the security stamp validation fails
            if (refreshTicket?.Properties?.ExpiresUtc is not { } expiresUtc ||
                timeProvider.GetUtcNow() >= expiresUtc ||
                await signInManager.ValidateSecurityStampAsync(refreshTicket.Principal) is not User user)

            {
                return TypedResults.Challenge();
            }

            var newPrincipal = await signInManager.CreateUserPrincipalAsync(user);
            return TypedResults.SignIn(newPrincipal, authenticationScheme: IdentityConstants.BearerScheme);
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            return TypedResults.Problem(
                title: ResourceManager.GetString("ServerError", culture),
                detail: ResourceManager.GetString("ServerErrorDesc", culture),
                statusCode: StatusCodes.Status500InternalServerError
            );
        }
    }
}

public class RefreshRequestValidator : AbstractValidator<RefreshRequest>
{
    public RefreshRequestValidator(ICultureProvider cultureProvider)
    {
        var culture = cultureProvider.GetCurrentCulture();
        RuleFor(x => x.RefreshToken).NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture));
    }
}