using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Raiconet.Cotizador.Api.Services.MailSender;
using Serilog;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class RequestEmailVerificationCodeHandler
{
    public static async Task<Results<Created, ValidationProblem, ProblemHttpResult>> Handler(
        [FromServices] AppDbContext dbContext,
        OtpRequest req,
        IEmailSender emailSender,
        [FromServices] UserManager<User> userManager,
        [FromServices] ICultureProvider cultureProvider,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            var existingOtp = await dbContext.Otps
                .Where(o => o.Email == req.Email
                            && !o.IsVerified)
                .OrderByDescending(o => o.CreatedAt)
                .FirstOrDefaultAsync(cancellationToken);

            if (existingOtp != null && existingOtp.IsExpired() == false)
            {
                await emailSender.SendEmailVerificationCodeAsync(
                    existingOtp.Email, existingOtp.Code, Otp.DefaultExpirationMinutes.ToString());
                return TypedResults.Created();
            }
            
            var existingUser = await userManager.FindByEmailAsync(req.Email);

            if (existingUser != null)
            {
                return TypedResults.Problem(
                    statusCode: StatusCodes.Status400BadRequest,
                    title: ResourceManager.GetString("EmailAlreadyInUse", culture),
                    detail: string.Format(
                        ResourceManager.GetString("EmailAlreadyInUseDetail", culture) 
                            ?? string.Empty,
                        req.Email
                    )
                );
            }

            var otp = Otp.Create(req.Email);
            await dbContext.Otps.AddAsync(otp, cancellationToken);
            await dbContext.SaveChangesAsync(cancellationToken);
            
            await emailSender.SendEmailVerificationCodeAsync(
                otp.Email, otp.Code, Otp.DefaultExpirationMinutes.ToString());

            return TypedResults.Created();
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            return TypedResults.Problem(
                statusCode: StatusCodes.Status500InternalServerError,
                title: ResourceManager.GetString("ServerError", culture),
                detail: ResourceManager.GetString("ServerErrorDesc", culture)
            );
        }
    }
}

public record OtpRequest(string Email);

public class OtpRequestValidator : AbstractValidator<OtpRequest>
{
    public OtpRequestValidator(ICultureProvider cultureProvider)
    {
        var culture = cultureProvider.GetCurrentCulture();
        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage(ResourceManager.GetString("EmailInvalidFormat", culture));
    }
}
