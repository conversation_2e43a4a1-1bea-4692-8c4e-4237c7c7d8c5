using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Raiconet.Cotizador.Api.Services.SessionManager;
using Serilog;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class CreateUserProfile
{
    public static async Task<Results<Created, ProblemHttpResult, ValidationProblem>> Handler(
        [FromBody] CreateUserProfileRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ICultureProvider cultureProvider,
        [FromServices] ISessionManager sessionManager,
        [FromServices] UserManager<User> userManager,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            var user = await sessionManager.GetCurrentUserAsync();
            if (user == null)
            {
                throw new InvalidOperationException("user should be logged in");
            }

            var userProfile = UserProfile.Create(
                firstName: request.FirstName,
                lastName: request.LastName,
                address: UserAddress.Create(
                    country: request.Address.Country,
                    addressLine1: request.Address.AddressLine1,
                    addressLine2: request.Address.AddressLine2,
                    postalCode: request.Address.PostalCode,
                    city: request.Address.City
                ),
                user: user
            );

            await userManager.SetPhoneNumberAsync(user, request.PhoneNumber);
            await dbContext.UserProfiles.AddAsync(userProfile, cancellationToken);
            await dbContext.SaveChangesAsync(cancellationToken);

            return TypedResults.Created();
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            return TypedResults.Problem(
                title: ResourceManager.GetString("ServerError", culture),
                detail: ResourceManager.GetString("ServerErrorDesc", culture),
                statusCode: StatusCodes.Status500InternalServerError
            );
        }
    }
}

public record CreateUserProfileRequest(
    string FirstName,
    string LastName,
    AddressRequest Address,
    string PhoneNumber
);

public record AddressRequest(
    string Country,
    string AddressLine1,
    string? AddressLine2,
    string? PostalCode,
    string City
);

public class CreateUserProfileRequestValidator : AbstractValidator<CreateUserProfileRequest>
{
    public CreateUserProfileRequestValidator(ICultureProvider cultureProvider)
    {
        var culture = cultureProvider.GetCurrentCulture();
        var message = ResourceManager.GetString("MaxLength", culture);
        RuleFor(x => x.FirstName)
            .NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture))
            .MaximumLength(100)
            .WithMessage(string.Format(ResourceManager.GetString("MaxLength", culture)!, 100));

        RuleFor(x => x.LastName)
            .NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture))
            .MaximumLength(100)
            .WithMessage(
                string.Format(ResourceManager.GetString("MaxLength", culture)!, 100));

        RuleFor(x => x.PhoneNumber)
            .NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture))
            .MaximumLength(20)
            .WithMessage(
                string.Format(ResourceManager.GetString("MaxLength", culture)!, 20));

        RuleFor(x => x.Address).NotNull().SetValidator(new AddressRequestValidator(cultureProvider));
    }
}

public class AddressRequestValidator : AbstractValidator<AddressRequest>
{
    public AddressRequestValidator(ICultureProvider cultureProvider)
    {
        var culture = cultureProvider.GetCurrentCulture();

        RuleFor(x => x.Country)
            .NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture))
            .MaximumLength(100)
            .WithMessage(
                string.Format(ResourceManager.GetString("MaxLength", culture)!, 100)
            );

        RuleFor(x => x.AddressLine1)
            .NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture))
            .MaximumLength(500)
            .WithMessage(
                string.Format(ResourceManager.GetString("MaxLength", culture)!, 500)
            );

        RuleFor(x => x.AddressLine2)
            .MaximumLength(500)
            .WithMessage(
                string.Format(ResourceManager.GetString("MaxLength", culture)!, 500)
            );

        RuleFor(x => x.PostalCode)
            .MaximumLength(100)
            .WithMessage(
                string.Format(ResourceManager.GetString("MaxLength", culture)!, 100)
            );

        RuleFor(x => x.City)
            .NotEmpty()
            .WithMessage(ResourceManager.GetString("NotEmpty", culture))
            .MaximumLength(100)
            .WithMessage(
                string.Format(ResourceManager.GetString("MaxLength", culture)!, 100)
            );
    }
}