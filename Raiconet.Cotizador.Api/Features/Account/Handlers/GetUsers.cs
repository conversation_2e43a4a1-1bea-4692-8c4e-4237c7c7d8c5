using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Serilog;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;


public static class GetAllUsers
{
    public static async Task<Results<Ok<GetAllUsersResponse>, ProblemHttpResult, ValidationProblem>> Handler(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? searchTerm,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromServices] AppDbContext dbContext,
        [FromServices] ICultureProvider cultureProvider,
        [FromServices] UserManager<User> userManager,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            var query = dbContext.UserProfiles
                .AsNoTracking()
                .Include(up => up.User)
                .AsQueryable();

            // Aplicar búsqueda
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(up => 
                    up.FirstName.ToLower().Contains(searchTermLower) ||
                    up.LastName.ToLower().Contains(searchTermLower) ||
                    up.User.Email.ToLower().Contains(searchTermLower) ||
                    (up.User.PhoneNumber != null && up.User.PhoneNumber.Contains(searchTerm))
                );
            }

            // Aplicar ordenamiento
            query = sortBy?.ToLower() switch
            {
                "firstname" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(up => up.FirstName)
                    : query.OrderBy(up => up.FirstName),
                "lastname" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(up => up.LastName)
                    : query.OrderBy(up => up.LastName),
                "email" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(up => up.User.Email)
                    : query.OrderBy(up => up.User.Email),
                _ => query.OrderBy(up => up.FirstName)
            };

            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            var totalCount = await query.CountAsync(cancellationToken);
            
            var users = await query
                .Skip((actualPage - 1) * actualPageSize)
                .Take(actualPageSize)
                .ToListAsync(cancellationToken);

            // Obtener todos los TenantIds de los usuarios para hacer una sola consulta
            var tenantIds = users
                .Where(up => up.User.TenantId.HasValue)
                .Select(up => up.User.TenantId.Value)
                .Distinct()
                .ToList();
                
            // Cargar todos los tenants necesarios en una sola consulta
            var tenants = await dbContext.Tenants
                .Where(t => tenantIds.Contains(t.Id))
                .ToDictionaryAsync(t => t.Id, t => t.Name, cancellationToken);

            var userResponses = new List<UserResponse>();
            foreach (var userProfile in users)
            {
                var roles = await userManager.GetRolesAsync(userProfile.User);
                
                // Buscar el nombre del tenant de forma segura usando el diccionario
                string? tenantName = null;
                if (userProfile.User.TenantId.HasValue && tenants.ContainsKey(userProfile.User.TenantId.Value))
                {
                    tenantName = tenants[userProfile.User.TenantId.Value];
                }
                
                userResponses.Add(new UserResponse(
                    UserId: userProfile.UserId,
                    FirstName: userProfile.FirstName,
                    LastName: userProfile.LastName,
                    Email: userProfile.User.Email!,
                    PhoneNumber: userProfile.User.PhoneNumber,
                    IsProfileCompleted: true,
                    Role: roles.FirstOrDefault() ?? string.Empty,
                    IsEnabled: userProfile.User.IsEnabled,
                    Tenant: userProfile.User.TenantId,
                    TenantName: tenantName
                ));
            }

            var response = new GetAllUsersResponse(
                TotalCount: totalCount,
                PageSize: actualPageSize,
                CurrentPage: actualPage,
                TotalPages: (int)Math.Ceiling(totalCount / (double)actualPageSize),
                Users: userResponses
            );

            return TypedResults.Ok(response);
        }
        catch (Exception e)
        {
            Log.Error(
                e,
                "Users retrieval failed");
            throw;
        }
    }
}

public record GetAllUsersResponse(
    int TotalCount,
    int PageSize,
    int CurrentPage,
    int TotalPages,
    List<UserResponse> Users
);

public record UserResponse(
    string UserId,
    string? FirstName,
    string? LastName,
    string Email,
    string? PhoneNumber,
    bool IsProfileCompleted,
    string Role,
    bool IsEnabled,
    int? Tenant,
    string? TenantName
);