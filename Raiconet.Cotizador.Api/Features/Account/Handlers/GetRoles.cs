using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class GetRoles
{
    public static async Task<Results<Ok<List<RoleResponse>>, ProblemHttpResult>> Handler(
        [FromServices] AppDbContext dbContext,
        [FromServices] ICultureProvider cultureProvider,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            var roles = await dbContext.Roles
                .AsNoTracking()
                .OrderBy(r => r.Name)
                .Select(r => new RoleResponse(
                    r.Id,
                    r.Name!
                ))
                .ToListAsync(cancellationToken);

            return TypedResults.Ok(roles);
        }
        catch (Exception e)
        {
            Log.Error(e, "Roles retrieval failed");
            throw;
        }
    }
}

public record RoleResponse(
    string Id,
    string Name
);