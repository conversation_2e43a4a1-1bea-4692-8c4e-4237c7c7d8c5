using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Raiconet.Cotizador.Api.Services.MailSender;
using Serilog;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Features.Account.Handlers;

public static class RequestPasswordResetCodeHandler
{
    public static async Task<Results<Created, ValidationProblem, ProblemHttpResult>> Handler(
        [FromBody] OtpRequest req,
        [FromServices] IEmailSender emailSender,
        [FromServices] AppDbContext dbContext,
        [FromServices] ICultureProvider cultureProvider,
        [FromServices] UserManager<User> userManager,
        CancellationToken cancellationToken
    )
    {
        var culture = cultureProvider.GetCurrentCulture();
        try
        {
            var user = await userManager.FindByEmailAsync(req.Email);
            if (user == null)
            {
                return TypedResults.Created();
            }
            var existingOtp = await dbContext.Otps
                .Where(o => o.Email == req.Email
                            && o.ExpiresAt > DateTime.UtcNow
                            && !o.IsVerified)
                .OrderByDescending(o => o.CreatedAt)
                .FirstOrDefaultAsync(cancellationToken);

            if (existingOtp != null)
            {
                await emailSender.SendPasswordResetCodeAsync(
                    existingOtp.Email,
                    existingOtp.Code,
                    Otp.DefaultExpirationMinutes.ToString());
                return TypedResults.Created();
            }

            var otp = Otp.Create(req.Email);
            await dbContext.Otps.AddAsync(otp, cancellationToken);
            await dbContext.SaveChangesAsync(cancellationToken);

            await emailSender.SendPasswordResetCodeAsync(
                otp.Email,
                otp.Code,
                Otp.DefaultExpirationMinutes.ToString()
            );

            return TypedResults.Created();
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            return TypedResults.Problem(
                statusCode: StatusCodes.Status500InternalServerError,
                title: ResourceManager.GetString("ServerError", culture),
                detail: ResourceManager.GetString("ServerErrorDesc", culture)
            );
        }
    }
}