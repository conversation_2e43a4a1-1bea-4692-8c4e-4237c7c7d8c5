using Raiconet.Cotizador.Api.Features.Quotation.Handlers;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;

namespace Raiconet.Cotizador.Api.Features.Quotations;

public static class QuotationEndpoints
{
    public static void AddQuotationEndpoints(this WebApplication app)
    {
        var quotationsGroup = app.MapGroup("quotations").WithTags("Quotations"); ;
        //.RequireAuthorization();

        // Endpoint para cotización de importación
        quotationsGroup.MapPost("import", GetImportQuotation.Handler)
            .WithName("GetImportQuotation")
            .WithDescription("Calculate import quotation based on service type and packages");

        // Endpoint para guardar cotización
        quotationsGroup.MapPost("save", CreateQuotation.Handler)
            .RequireAuthorization();

        // Endpoint para obtener lista de cotizaciones
        quotationsGroup.MapGet("", GetQuotationList.Handler)
            .RequireAuthorization();

        // Endpoint para obtener lista de cotizaciones por tenant
        quotationsGroup.MapGet("by-tenant", GetQuotationListByTenant.Handler)
            .WithName("GetQuotationListByTenant")
            .WithDescription("Get quotations filtered by tenant with role-based access control")
            .RequireAuthorization();

        // Endpoint para obtener cotización por ID
        quotationsGroup.MapGet("{id:int}", GetQuotationById.Handler);

        // Nuevo endpoint para obtener cotización por código
        quotationsGroup.MapGet("by-code/{code}", GetQuotationByCode.Handler)
            .WithName("GetQuotationByCode")
            .WithDescription("Get quotation by unique code")
            .RequireAuthorization();

        // Nuevo endpoint para cotización de exportación
        quotationsGroup.MapPost("export", GetExportQuotation.Handler)
            .WithName("GetExportQuotation")
            .WithDescription("Calculate export quotation based on service type and packages");
        
        // Endpoint para actualizar el status de una cotización
        quotationsGroup.MapPut("status", UpdateQuotationStatus.Handler)
            .WithName("UpdateQuotationStatus")
            .WithDescription("Update the status of an existing quotation")
            .RequireAuthorization();

        // Endpoint para actualizar el status de una cotización
        quotationsGroup.MapPut("reject", RejectQuotation.Handler)
            .WithName("RejectQuotation")
            .WithDescription("Reject a quotation")
            .RequireAuthorization();

        // RIC specific endpoints
        var ricGroup = quotationsGroup.MapGroup("ric").WithTags("RIC Quotations");

        // Endpoint para cotización de importación RIC con lógica condicional de costos
        ricGroup.MapPost("import", GetRicImportQuotation.Handler)
            .WithName("GetRicImportQuotation")
            .WithDescription("Calculate RIC import quotation with conditional cost logic based on IsPrepaid and IncludeTaxes flags");
    }
}