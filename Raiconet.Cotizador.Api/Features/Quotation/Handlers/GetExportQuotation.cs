using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.Quotation;
using Raiconet.Cotizador.Api.Services.Quotations;

namespace Raiconet.Cotizador.Api.Features.Quotations.Handlers;

public static class GetExportQuotation
{
  public static async Task<
      Results<Ok<QuotationExportResponse>, BadRequest<string>, NotFound, IResult>
  > Handler(
      [FromBody] ExportQuotationRequest request,
      [FromServices] AppDbContext dbContext,
      [FromServices] IQuotationCalculatorExport quotationCalculator,
      [FromServices] ILogger logger
  )
  {
    try
    {
      var customer = await dbContext.Customers.FirstOrDefaultAsync(
          c => c.Id == request.CustomerId
      );

      if (customer == null)
        return TypedResults.NotFound();

      var setupZone = await dbContext.SetupZones.FirstOrDefaultAsync(
          z => z.Id == request.SetupZonesId
      );

      if (setupZone == null)
        return TypedResults.NotFound();

      List<AdditionalService> additionalServices = new();
      if (request.AdditionalServiceIds != null && request.AdditionalServiceIds.Any())
      {
        foreach (var serviceId in request.AdditionalServiceIds)
        {
          var additionalService = await dbContext.AdditionalServices.FirstOrDefaultAsync(
              a => a.Id == serviceId && a.IsActive
          );

          if (additionalService != null)
          {
            additionalServices.Add(additionalService);
          }
          else
          {
            return TypedResults.BadRequest($"Invalid additional service ID: {serviceId}");
          }
        }
      }

      // Validar que el serviceTypeId sea válido para exportación
      if (request.ServiceTypeId != 1 && request.ServiceTypeId != 2)
      {
        return TypedResults.BadRequest("El ServiceTypeId debe ser 1 (Courier Directo) o 2 (RaicoExport)");
      }

      var calculationResult = await quotationCalculator.CalculateQuotationExport(
          request.CustomerId,
          request.ServiceTypeId,
          request.PackageTypeId,
          request.TotalPackageValue,
          request.IncludeInsurance,
          request.Packages,
          setupZone,
          additionalServices,
          request.DutyPercentage
      );

      if (!calculationResult.Success)
        return TypedResults.BadRequest(calculationResult.Error);

      var response = new QuotationExportResponse
      {
        CustomerId = customer.Id,
        CustomerName = customer.BusinessName,
        ServiceTypeId = request.ServiceTypeId,
        SetupZone = new SetupZoneInfoExport
        {
          Id = setupZone.Id,
          Country = setupZone.Country,
          Zone = setupZone.Zone,
          Warehouse = setupZone.Warehouse
        },
        Packages = request.Packages
            .Select(
                p =>
                    new PackageExportResponse
                    {
                      PackageValue = p.PackageValue,
                      VolumetricWeight = p.VolumetricWeight,
                      NetWeight = p.NetWeight,
                      ChargeableWeight = Math.Max(p.VolumetricWeight, p.NetWeight)
                    }
            )
            .ToList(),
        PackageRates = calculationResult.PackageRates,
        TotalsByService = calculationResult.TotalsByService,
        AdditionalServicesCost = calculationResult.AdditionalServicesCost
      };

      return TypedResults.Ok(response);
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Failed to process export quotation");
      throw;
    }
  }

  public class ExportQuotationRequest
  {
    public int CustomerId { get; set; }
    public int ServiceTypeId { get; set; } // 1 = Courier Directo, 2 = RaicoExport
    public int PackageTypeId { get; set; }
    public int SetupZonesId { get; set; }
    public List<int>? AdditionalServiceIds { get; set; }
    public decimal DutyPercentage { get; set; }
    public decimal TotalPackageValue { get; set; }
    public bool IncludeInsurance { get; set; }
    public List<GetImportQuotation.PackageRequest> Packages { get; set; } = new();
    public string? SelectedService { get; set; }
  }

  // public class PackageRequest
  // {
  //   public decimal PackageValue { get; set; }
  //   public int PackageTypeId { get; set; }
  //   public decimal VolumetricWeight { get; set; }
  //   public decimal NetWeight { get; set; }
  // }

  public class QuotationExportResponse
  {
    public int CustomerId { get; set; }
    public string CustomerName { get; set; }
    public int ServiceTypeId { get; set; }
    public SetupZoneInfoExport SetupZone { get; set; }
    public List<PackageExportResponse> Packages { get; set; } = new();
    public List<PackageRate> PackageRates { get; set; }
    public Dictionary<string, TotalsByService> TotalsByService { get; set; }
    public decimal AdditionalServicesCost { get; set; }
  }

  public class SetupZoneInfoExport
  {
    public int Id { get; set; }
    public string Country { get; set; }
    public int? Zone { get; set; }
    public string? Warehouse { get; set; }
  }

  public class PackageExportResponse
  {
    public decimal PackageValue { get; set; }
    public decimal VolumetricWeight { get; set; }
    public decimal NetWeight { get; set; }
    public decimal ChargeableWeight { get; set; }
  }
}