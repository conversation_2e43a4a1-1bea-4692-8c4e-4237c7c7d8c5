using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Quotation.Handlers;

public static class GetQuotationById
{
    public static async Task<Results<Ok<GetQuotationList.QuotationListResponse>, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext
    )
    {
        try
        {
            var quotation = await dbContext.QuotationResults
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Id == id);
            if (quotation == null)
            {
                return TypedResults.Problem(
                    new ProblemDetails() { Title = $"Cotización con el ID {id} no existe", Status = 400 });
            }

            return TypedResults.Ok(GetQuotationList.MapToResponse(quotation));
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            throw;
        }
    }
}