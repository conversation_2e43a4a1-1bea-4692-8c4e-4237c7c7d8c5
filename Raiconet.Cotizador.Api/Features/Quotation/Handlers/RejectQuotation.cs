using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.Quotation.Handlers;

public static class RejectQuotation
{
    public record RejectQuotationRequest
    {
        public int QuotationId { get; set; }
        public int RejectReasonId { get; set; }
        public string? Notes { get; set; }
    }

    public static async Task<
        Results<Ok, BadRequest<string>, NotFound>
    > Handler(
        [FromBody] RejectQuotationRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger,
        [FromServices] ISessionManager sessionManager
    )
    {
        try
        {
            var user = await sessionManager.GetCurrentUserAsync();
            
            var quotation = await dbContext.QuotationResults
                .FirstOrDefaultAsync(q => q.Id == request.QuotationId);

            if (quotation == null)
                return TypedResults.NotFound();

            var rejectedReason = await dbContext.RejectionReasons
                .FirstOrDefaultAsync(rr => rr.Id == request.RejectReasonId);

             if (rejectedReason == null)
                return TypedResults.NotFound();

            // Actualizar solo el status
            quotation.Status = QuotationStatus.Rejected;
            quotation.RejectionReasonId = request.RejectReasonId;
            quotation.Notes = request.Notes;
            
            // Opcionalmente podríamos agregar campos de auditoría como:
            quotation.UpdatedAt = DateTime.UtcNow;
            quotation.UpdatedBy = user?.Id ?? "";

            await dbContext.SaveChangesAsync();
            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to update quotation status");
            throw;
        }
    }
}