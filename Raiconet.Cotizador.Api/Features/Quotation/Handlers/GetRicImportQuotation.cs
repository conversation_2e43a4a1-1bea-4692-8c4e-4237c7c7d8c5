using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.Quotation;
using Raiconet.Cotizador.Api.Services.Quotations;
using Raiconet.Cotizador.Api.Features.Quotation.Models;
using System.Text.Json;

namespace Raiconet.Cotizador.Api.Features.Quotations.Handlers;

public static class GetRicImportQuotation
{
  public static async Task<
      Results<Ok<QuotationResponse>, BadRequest<string>, NotFound, IResult>
  > Handler(
      [FromBody] RicImportQuotationRequest request,
      [FromQuery] bool? fromRicWeb,
      [FromServices] AppDbContext dbContext,
      [FromServices] IRicQuotationCalculator quotationCalculator,
      [FromServices] ILogger logger
  )
  {
    try
    {
      logger.LogInformation("Processing RIC import quotation for CustomerId={CustomerId}, IsPrepaid={IsPrepaid}, IncludeTaxes={IncludeTaxes}",
          request.CustomerId, request.IsPrepaid, request.IncludeTaxes);

      // Handle lead capture for RIC website requests - always save if LeadData is present
      logger.LogInformation("Lead capture check: LeadData={LeadData}",
          request.LeadData != null ? "Present" : "Null");

      if (request.LeadData != null)
      {
        logger.LogInformation("Attempting to save RIC lead quotation for {Email}", request.LeadData.Email);
        await SaveRicLeadQuotation(request, dbContext, logger);
      }
      else
      {
        logger.LogInformation("Skipping lead capture: LeadData not present");
      }

      var customer = await dbContext.Customers.FirstOrDefaultAsync(
          c => c.Id == request.CustomerId
      );

      if (customer == null)
      {
        return TypedResults.BadRequest("Cliente no encontrado");
      }

      var setupZone = await dbContext.SetupZones.FirstOrDefaultAsync(
          s => s.Id == request.SetupZonesId
      );

      if (setupZone == null)
      {
        return TypedResults.BadRequest("Zona de configuración no encontrada");
      }

      List<AdditionalService>? additionalServices = null;
      if (request.AdditionalServiceIds != null && request.AdditionalServiceIds.Any())
      {
        additionalServices = await dbContext.AdditionalServices
            .Where(a => request.AdditionalServiceIds.Contains(a.Id))
            .ToListAsync();
      }

      var calculationResult = await quotationCalculator.CalculateRicQuotation(
          request.CustomerId,
          request.ServiceTypeId,
          request.PackageTypeId,
          request.TotalPackageValue,
          request.Packages,
          setupZone,
          additionalServices,
          request.DutyPercentage,
          request.ExtraCharge,
          request.IsPrepaid,
          request.IncludeTaxes,
          request.TvhType,
          request.PickupZoneId,
          request.WarehouseCountry,
          request.IncludePickup
      );

      if (!calculationResult.Success)
      {
        return TypedResults.BadRequest(calculationResult.Error);
      }

      var response = new QuotationResponse
      {
        CustomerId = customer.Id,
        CustomerName = customer.BusinessName,
        ServiceTypeId = request.ServiceTypeId,
        SetupZone = new SetupZoneInfo
        {
          Id = setupZone.Id,
          Country = setupZone.Country,
          Zone = setupZone.Zone,
          Warehouse = setupZone.Warehouse
        },
        Packages = request.Packages
            .Select(
                p =>
                    new PackageResponse
                    {
                      PackageValue = p.PackageValue,
                      VolumetricWeight = p.VolumetricWeight,
                      NetWeight = p.NetWeight,
                      ChargeableWeight = Math.Max(p.VolumetricWeight, p.NetWeight)
                    }
            )
            .ToList(),
        PackageRates = calculationResult.PackageRates,
        TotalsByService = calculationResult.TotalsByService,
        AdditionalServicesCost = calculationResult.AdditionalServicesCost
      };

      return TypedResults.Ok(response);
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Error calculating RIC import quotation");
      return TypedResults.Problem("Error interno del servidor");
    }
  }

  public class RicImportQuotationRequest
  {
    public int CustomerId { get; set; }
    public int ServiceTypeId { get; set; }
    public int PackageTypeId { get; set; }
    public int? SetupZonesId { get; set; }
    public List<int>? AdditionalServiceIds { get; set; }
    public decimal DutyPercentage { get; set; }
    public decimal ExtraCharge { get; set; } = 0;
    public decimal TotalPackageValue { get; set; }
    public int? TvhType { get; set; }  // Para servicio TVH (1=prepaid, 2=collect)
    public int? PickupZoneId { get; set; } // Para servicio Warehouse
    public string? WarehouseCountry { get; set; } // Para servicio Warehouse (China, Madrid, Miami)
    public bool? IncludePickup { get; set; } // Para servicio Warehouse - determina si se incluye el costo de pickup
    public List<PackageRequest> Packages { get; set; } = new();
    public string? SelectedService { get; set; }
    public string? Type { get; set; }
    public TotalsByService? TotalsByService { get; set; }
    public Dictionary<string,string>? CustomTotalsByService { get; set; }
    public string? Notes { get; set; }
    public string? Description { get; set; }
    public decimal? DutyPercentageSuggested { get; set; }
    public decimal? FobPercentage { get; set; }

    // RIC specific properties for conditional cost calculation
    public bool IsPrepaid { get; set; } = false;
    public bool IncludeTaxes { get; set; } = true;

    // Lead data for RIC website requests
    public LeadData? LeadData { get; set; }
  }

  // Reuse existing models from GetImportQuotation
  public class PackageRequest
  {
    public decimal PackageValue { get; set; }
    public int PackageTypeId { get; set; }
    public decimal VolumetricWeight { get; set; }
    public decimal NetWeight { get; set; }
  }

  public class QuotationResponse
  {
    public int CustomerId { get; set; }
    public string CustomerName { get; set; }
    public int ServiceTypeId { get; set; }
    public SetupZoneInfo SetupZone { get; set; }
    public List<PackageResponse> Packages { get; set; } = new();
    public List<PackageRate> PackageRates { get; set; }
    public Dictionary<string, TotalsByService> TotalsByService { get; set; }
    public decimal AdditionalServicesCost { get; set; }
  }

  public class SetupZoneInfo
  {
    public int Id { get; set; }
    public string Country { get; set; }
    public int? Zone { get; set; }
    public string? Warehouse { get; set; }
  }

  public class PackageResponse
  {
    public decimal PackageValue { get; set; }
    public decimal VolumetricWeight { get; set; }
    public decimal NetWeight { get; set; }
    public decimal ChargeableWeight { get; set; }
  }

  public class LeadData
  {
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
  }

  private static async Task SaveRicLeadQuotation(
      RicImportQuotationRequest request,
      AppDbContext dbContext,
      ILogger logger)
  {
    try
    {
      // Validate that LeadData exists and required fields are present
      if (request.LeadData == null ||
          string.IsNullOrWhiteSpace(request.LeadData.FirstName) ||
          string.IsNullOrWhiteSpace(request.LeadData.LastName) ||
          string.IsNullOrWhiteSpace(request.LeadData.Email) ||
          string.IsNullOrWhiteSpace(request.LeadData.PhoneNumber))
      {
        logger.LogWarning("RIC lead capture attempted but required fields are missing. LeadData: {LeadData}",
            request.LeadData != null ? $"FirstName: {request.LeadData.FirstName}, LastName: {request.LeadData.LastName}, Email: {request.LeadData.Email}, PhoneNumber: {request.LeadData.PhoneNumber}" : "null");
        return; // Don't fail the quotation process, just skip lead saving
      }

      // Serialize the entire request to JSON for storage
      var quotationRequestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions
      {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
      });

      // Create the lead quotation record
      var leadQuotation = new LeadQuotation
      {
        FirstName = request.LeadData.FirstName.Trim(),
        LastName = request.LeadData.LastName.Trim(),
        Email = request.LeadData.Email.Trim().ToLowerInvariant(),
        PhoneNumber = request.LeadData.PhoneNumber.Trim(),
        QuotationRequestData = quotationRequestJson,
        CreatedAt = DateTime.UtcNow
      };

      // Save to database
      dbContext.LeadQuotations.Add(leadQuotation);
      await dbContext.SaveChangesAsync();

      logger.LogInformation("RIC lead quotation saved successfully for {Email} - {FirstName} {LastName}",
          leadQuotation.Email, leadQuotation.FirstName, leadQuotation.LastName);
    }
    catch (Exception ex)
    {
      // Log the error but don't fail the quotation process
      logger.LogError(ex, "Failed to save RIC lead quotation for {Email} - {FirstName} {LastName}",
          request.LeadData?.Email, request.LeadData?.FirstName, request.LeadData?.LastName);
    }
  }
}
