using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Quotation.Handlers;

public static class GetQuotationByCode
{
    public static async Task<Results<Ok<GetQuotationList.QuotationListResponse>, IResult>> Handler(
        [FromRoute] string code,
        [FromServices] AppDbContext dbContext
    )
    {
        try
        {
            var quotation = await dbContext.QuotationResults
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.Code == code);
            
            if (quotation == null)
            {
                return TypedResults.Problem(
                    new ProblemDetails() { Title = $"Cotización con el código {code} no existe", Status = 400 });
            }

            return TypedResults.Ok(GetQuotationList.MapToResponse(quotation));
        }
        catch (Exception e)
        {
            Log.Error(e, e.Message);
            throw;
        }
    }
}