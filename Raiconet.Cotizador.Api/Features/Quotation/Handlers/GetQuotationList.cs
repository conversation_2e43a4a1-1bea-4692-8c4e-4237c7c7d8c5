using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Quotation.Handlers;

public static class GetQuotationList
{
    public static async Task<Results<Ok<GetAllQuotationsResponse>, IResult>> Handler(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromQuery] string? searchTerm,
        [FromQuery] int? serviceTypeId,
        [FromQuery] QuotationStatus? status,
        [FromQuery] string type,
        [FromQuery] string? createdBy,
        [FromQuery] DateTime? dateFrom,
        [FromQuery] DateTime? dateTo,
        [FromServices] AppDbContext dbContext
    )
    {
        try
        {
            var query = dbContext.QuotationResults.Include(u=>u.Creator).Where(u=>u.Creator.TenantId == null).Include(r =>r.RejectionReason)
                .AsNoTracking()
                .AsQueryable();

            // Consulta base para conteos por estado (sin paginación)
            var countQuery = query;

            // Configurar fechas por defecto (últimos 2 meses)
            var now = DateTime.UtcNow;
            
            // Asegurar que las fechas sean UTC
            DateTime effectiveDateFrom;
            DateTime effectiveDateTo;
            
            if (dateFrom.HasValue)
            {
                effectiveDateFrom = dateFrom.Value.Kind == DateTimeKind.Unspecified 
                    ? DateTime.SpecifyKind(dateFrom.Value, DateTimeKind.Utc)
                    : dateFrom.Value.ToUniversalTime();
            }
            else
            {
                // Por defecto: hace 2 meses desde hoy
                effectiveDateFrom = now.AddMonths(-24).Date;
            }
            
            if (dateTo.HasValue)
            {
                effectiveDateTo = dateTo.Value.Kind == DateTimeKind.Unspecified 
                    ? DateTime.SpecifyKind(dateTo.Value, DateTimeKind.Utc)
                    : dateTo.Value.ToUniversalTime();
                
                // Si la fecha no tiene hora, establecer al final del día
                if (effectiveDateTo.Hour == 0 && effectiveDateTo.Minute == 0 && effectiveDateTo.Second == 0)
                {
                    effectiveDateTo = effectiveDateTo.Date.AddDays(1).AddTicks(-1);
                }
            }
            else
            {
                // Por defecto: hoy al final del día
                effectiveDateTo = now.Date.AddDays(1).AddTicks(-1);
            }

            // Registrar los parámetros recibidos
            Log.Information("Parámetros de búsqueda: page={Page}, pageSize={PageSize}, sortBy={SortBy}, sortOrder={SortOrder}, searchTerm={SearchTerm}, serviceTypeId={ServiceTypeId}, status={Status}, type={Type}, createdBy={CreatedBy}, dateFrom={DateFrom}, dateTo={DateTo}, effectiveDateFrom={EffectiveDateFrom}, effectiveDateTo={EffectiveDateTo}", 
                page, pageSize, sortBy, sortOrder, searchTerm, serviceTypeId, status, type, createdBy, dateFrom, dateTo, effectiveDateFrom, effectiveDateTo);

            // Aplicar filtro de fecha
            query = query.Where(x => x.CreatedAt >= effectiveDateFrom && x.CreatedAt <= effectiveDateTo);
            countQuery = countQuery.Where(x => x.CreatedAt >= effectiveDateFrom && x.CreatedAt <= effectiveDateTo);

            // Apply filters
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(x => 
                    x.CustomerName.ToLower().Contains(searchTermLower) ||
                    x.CountryOfOrigin.ToLower().Contains(searchTermLower) ||
                    x.QuotationNumber.ToString().Contains(searchTermLower) ||
                    (x.Description != null && x.Description.ToLower().Contains(searchTermLower)) ||
                    (x.Code != null && x.Code.ToLower().Contains(searchTermLower))
                );
                countQuery = countQuery.Where(x => 
                    x.CustomerName.ToLower().Contains(searchTermLower) ||
                    x.CountryOfOrigin.ToLower().Contains(searchTermLower) ||
                    x.QuotationNumber.ToString().Contains(searchTermLower) ||
                    (x.Description != null && x.Description.ToLower().Contains(searchTermLower)) ||
                    (x.Code != null && x.Code.ToLower().Contains(searchTermLower))
                );
            }

            if (serviceTypeId.HasValue)
            {
                query = query.Where(x => x.ServiceTypeId == serviceTypeId.Value);
                countQuery = countQuery.Where(x => x.ServiceTypeId == serviceTypeId.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
                countQuery = countQuery.Where(x => x.Status == status.Value);
            }

            if (!string.IsNullOrWhiteSpace(createdBy))
            {
                query = query.Where(x => x.Creator.Email == createdBy);
                countQuery = countQuery.Where(x => x.Creator.Email == createdBy);
            }

            if (!string.IsNullOrWhiteSpace(type))
            {
                query = query.Where(x => x.Type == type);
                countQuery = countQuery.Where(x => x.Type == type);
            }

            // Registrar la consulta SQL para el conteo
            Log.Information("Consulta SQL para conteo: {SqlQuery}", countQuery.ToQueryString());

            // Solución 3: Hacer una sola consulta para obtener todos los conteos
            var statusCounts = await countQuery
                .GroupBy(x => x.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count);

            // Obtener los conteos del diccionario
            var approvedCount = statusCounts.GetValueOrDefault(QuotationStatus.Accepted, 0);
            var rejectedCount = statusCounts.GetValueOrDefault(QuotationStatus.Rejected, 0);
            var pendingCount = statusCounts.GetValueOrDefault(QuotationStatus.Pending, 0);
            var createdCount = statusCounts.GetValueOrDefault(QuotationStatus.Created, 0);

            // Registrar los conteos
            Log.Information("Conteos de estados: Approved={ApprovedCount}, Rejected={RejectedCount}, Pending={PendingCount}, Created={CreatedCount}",
                approvedCount, rejectedCount, pendingCount, createdCount);

            // Apply sorting
            query = sortBy?.ToLower() switch
            {
                "quotationnumber" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.QuotationNumber)
                    : query.OrderBy(x => x.QuotationNumber),
                "createdat" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.CreatedAt)
                    : query.OrderBy(x => x.CreatedAt),
                "customername" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.CustomerName)
                    : query.OrderBy(x => x.CustomerName),
                "totalpackagevalue" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.TotalPackageValue)
                    : query.OrderBy(x => x.TotalPackageValue),
                "status" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Status)
                    : query.OrderBy(x => x.Status),
                "code" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Code)
                    : query.OrderBy(x => x.Code),
                "description" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Description)
                    : query.OrderBy(x => x.Description),
                _ => sortOrder?.ToLower() == "asc"
                    ? query.OrderByDescending(x => x.Id)
                    : query.OrderBy(x => x.Id)
            };

            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            // Registrar la consulta SQL para conteo total
            Log.Information("Consulta SQL para conteo total: {SqlQuery}", query.ToQueryString());

            var totalCount = await query.CountAsync();
            
            // Registrar el conteo total
            Log.Information("Conteo total de registros: {TotalCount}", totalCount);

            // Registrar la consulta SQL para datos paginados
            var pagedQuery = query.Skip((actualPage - 1) * actualPageSize).Take(actualPageSize);
            Log.Information("Consulta SQL para datos paginados: {SqlQuery}", pagedQuery.ToQueryString());

            var quotations = await pagedQuery.ToListAsync();
            
            // Registrar el número de registros obtenidos
            Log.Information("Número de registros obtenidos: {QuotationsCount}", quotations.Count);

            // Verificar si hay registros específicos en la respuesta
            foreach (var quotation in quotations)
            {
                Log.Information("Registro obtenido: ID={Id}, QuotationNumber={QuotationNumber}, CustomerName={CustomerName}, CreatedAt={CreatedAt}, Status={Status}, Code={Code}",
                    quotation.Id, quotation.QuotationNumber, quotation.CustomerName, quotation.CreatedAt, quotation.Status, quotation.Code);
            }

            var response = new GetAllQuotationsResponse
            {
                TotalCount = totalCount,
                PageSize = actualPageSize,
                CurrentPage = actualPage,
                TotalPages = (int)Math.Ceiling(totalCount / (double)actualPageSize),
                Quotations = quotations.Select(MapToResponse).ToList(),
                // Asignar los conteos por estado
                ApprovedCount = approvedCount,
                RejectedCount = rejectedCount,
                PendingCount = pendingCount,
                CreatedCount = createdCount,
                // Agregar información de fechas utilizadas
                DateFrom = effectiveDateFrom,
                DateTo = effectiveDateTo
            };

            // Registrar la respuesta
            Log.Information("Respuesta generada: TotalCount={TotalCount}, Quotations.Count={QuotationsCount}, DateRange={DateFrom} to {DateTo}",
                response.TotalCount, response.Quotations.Count, response.DateFrom, response.DateTo);

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error al recuperar las cotizaciones: {ErrorMessage}", ex.Message);
            if (ex.InnerException != null)
            {
                Log.Error(ex.InnerException, "Excepción interna: {InnerErrorMessage}", ex.InnerException.Message);
            }
            throw;
        }
    }

    public static QuotationListResponse MapToResponse(QuotationResult quotation) =>
        new()
        {
            Id = quotation.Id,
            QuotationNumber = quotation.QuotationNumber,
            CreatedBy = quotation.Creator?.Email ?? "Sin usuario",
            CreatedAt = quotation.CreatedAt,
            TotalPackageValue = quotation.TotalPackageValue,
            CountryOfOrigin = quotation.CountryOfOrigin,
            ServiceTypeId = quotation.ServiceTypeId,
            CustomerName = quotation.CustomerName,
            Status = quotation.Status,
            // Nuevos campos añadidos
            Description = quotation.Description,
            Code = quotation.Code,
            DutyPercentage = quotation.DutyPercentage,
            DutyPercentageSuggested = quotation.DutyPercentageSuggested,
            FobPercentage = quotation.FobPercentage,
            ApprovedByClient = quotation.ApprovedByClient,
            Notes = quotation.Notes,
            // Campo agregado para motivo de rechazo
            RejectionReason = quotation.RejectionReason,
            QuotationPayload = quotation.QuotationPayload != null ? new TotalsByServiceResponse
            {
                Flete = quotation.QuotationPayload.Flete,
                TerminalCharge = quotation.QuotationPayload.TerminalCharge,
                Insurance = quotation.QuotationPayload.Insurance,
                Storage = quotation.QuotationPayload.Storage,
                UpsFuel = quotation.QuotationPayload.UpsFuel,
                SubTotal = quotation.QuotationPayload.SubTotal,
                Estadistica = quotation.QuotationPayload.Estadistica,
                Derecho = quotation.QuotationPayload.Derecho,
                Iva = quotation.QuotationPayload.Iva,
                Total = quotation.QuotationPayload.Total,
                Label = quotation.QuotationPayload.Label,
                CourierLabel = quotation.QuotationPayload.CourierLabel,
                ExportDocument = quotation.QuotationPayload.ExportDocument,
                TasaSumaria = quotation.QuotationPayload.TasaSumaria,
                CollectFee = quotation.QuotationPayload.CollectFee
            } : null,
            CustomQuotationPayload = quotation.CustomQuotationPayload,
            Packages = quotation.Packages?.Select(p => new GetImportQuotation.PackageResponse
            {
                VolumetricWeight = p.VolumetricWeight,
                NetWeight = p.NetWeight
            }).ToList() ?? new List<GetImportQuotation.PackageResponse>()
        };

    // Los records permanecen iguales
    public record GetAllQuotationsResponse
    {
        public int TotalCount { get; init; }
        public int PageSize { get; init; }
        public int CurrentPage { get; init; }
        public int TotalPages { get; init; }
        public List<QuotationListResponse> Quotations { get; init; } = new();
        public int ApprovedCount { get; init; }
        public int RejectedCount { get; init; }
        public int PendingCount { get; init; }
        public int CreatedCount { get; init; }
        public DateTime DateFrom { get; init; }
        public DateTime DateTo { get; init; }
    }

    public record QuotationListResponse
    {
        public int Id { get; init; }
        public int QuotationNumber { get; init; }
        public string CreatedBy { get; init; }
        public string updatedBy { get; init; }
        public DateTime CreatedAt { get; init; }
        public decimal TotalPackageValue { get; init; }
        public string CountryOfOrigin { get; init; }
        public int ServiceTypeId { get; init; }
        public string CustomerName { get; init; }
        public QuotationStatus Status { get; init; }
        
        // Nuevos campos añadidos
        public string? Description { get; init; }
        public string? Code { get; init; }
        public decimal? DutyPercentage { get; init; }
        public decimal? DutyPercentageSuggested { get; init; }
        public decimal? FobPercentage { get; init; }
        public bool ApprovedByClient { get; init; }
        public string? Notes { get; init; }
        
        // Campo agregado para motivo de rechazo
        public RejectionReason? RejectionReason { get; init; }
        
        public TotalsByServiceResponse? QuotationPayload { get; init; }
        public Dictionary<string,string>? CustomQuotationPayload { get; init; }
        public List<GetImportQuotation.PackageResponse> Packages { get; init; } = new();
    }

    public record TotalsByServiceResponse
    {
        public decimal Flete { get; init; }
        public decimal TerminalCharge { get; init; }
        public decimal Insurance { get; init; }
        public decimal Storage { get; init; }
        public decimal UpsFuel { get; init; }
        public decimal SubTotal { get; init; }
        public decimal Estadistica { get; init; }
        public decimal Derecho { get; init; }
        public decimal Iva { get; init; }
        public decimal Total { get; init; }
        public string Label { get; init; }
        public string CourierLabel { get; init; }
        public decimal ExportDocument { get; init; }
        public decimal TasaSumaria { get; init; }
        public decimal CollectFee { get; init; }
    }
}