using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.Quotation.Handlers;

public static class UpdateQuotationStatus
{
    public record UpdateQuotationStatusRequest
    {
        public int QuotationId { get; set; }
        public QuotationStatus Status { get; set; }
    }

    public record UpdateQuotationStatusResponse
    {
        public int Id { get; set; }
        public int QuotationNumber { get; set; }
        public string CustomerName { get; set; }
        public QuotationStatus Status { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public static async Task<
        Results<Ok<UpdateQuotationStatusResponse>, BadRequest<string>, NotFound>
    > Handler(
        [FromBody] UpdateQuotationStatusRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger,
        [FromServices] ISessionManager sessionManager
    )
    {
        try
        {
            var user = await sessionManager.GetCurrentUserAsync();
            
            var quotation = await dbContext.QuotationResults
                .FirstOrDefaultAsync(q => q.Id == request.QuotationId);

            if (quotation == null)
                return TypedResults.NotFound();

            // Actualizar solo el status
            quotation.Status = request.Status;
            
            // Opcionalmente podríamos agregar campos de auditoría como:
            quotation.UpdatedAt = DateTime.UtcNow;
            quotation.UpdatedBy = user?.Id ?? "";

            await dbContext.SaveChangesAsync();

            var response = new UpdateQuotationStatusResponse
            {
                Id = quotation.Id,
                QuotationNumber = quotation.QuotationNumber,
                CustomerName = quotation.CustomerName,
                Status = quotation.Status,
                UpdatedAt = DateTime.UtcNow
            };

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to update quotation status");
            throw;
        }
    }
}