using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.Quotation;
using Raiconet.Cotizador.Api.Services.Quotations;
using Raiconet.Cotizador.Api.Features.Quotation.Models;
using System.Text.Json;

namespace Raiconet.Cotizador.Api.Features.Quotations.Handlers;

public static class GetImportQuotation
{
  public static async Task<
      Results<Ok<QuotationResponse>, BadRequest<string>, NotFound, IResult>
  > Handler(
      [FromBody] ImportQuotationRequest request,
      [FromQuery] bool? fromRicWeb,
      [FromServices] AppDbContext dbContext,
      [FromServices] IQuotationCalculator quotationCalculator,
      [FromServices] ILogger logger
  )
  {
    try
    {
      // Handle lead capture for RIC website requests
      // If fromRicWeb is null, assume false
      logger.LogInformation("Lead capture check: fromRicWeb={FromRicWeb}, LeadData={LeadData}",
          fromRicWeb, request.LeadData != null ? "Present" : "Null");

      if (fromRicWeb == true && request.LeadData != null)
      {
        logger.LogInformation("Attempting to save lead quotation for {Email}", request.LeadData.Email);
        await SaveLeadQuotation(request, dbContext, logger);
      }
      else
      {
        logger.LogInformation("Skipping lead capture: fromRicWeb={FromRicWeb}, LeadData={LeadDataStatus}",
            fromRicWeb, request.LeadData != null ? "Present" : "Null");
      }

      var customer = await dbContext.Customers.FirstOrDefaultAsync(
          c => c.Id == request.CustomerId
      );

      if (customer == null)
        return TypedResults.NotFound();

      var setupZone = await dbContext.SetupZones.FirstOrDefaultAsync(
          z => z.Id == request.SetupZonesId
      );

      if (setupZone == null)
        return TypedResults.NotFound();

      List<AdditionalService> additionalServices = new();
      if (request.AdditionalServiceIds != null && request.AdditionalServiceIds.Any())
      {
        foreach (var serviceId in request.AdditionalServiceIds)
        {
          var additionalService = await dbContext.AdditionalServices.FirstOrDefaultAsync(
              a => a.Id == serviceId && a.IsActive
          );

          if (additionalService != null)
          {
            additionalServices.Add(additionalService);
          }
          else
          {
            return TypedResults.BadRequest($"Invalid additional service ID: {serviceId}");
          }
        }
      }

      // Validar datos específicos según el serviceTypeId
      if (request.ServiceTypeId == 3 && (!request.PickupZoneId.HasValue || string.IsNullOrEmpty(request.WarehouseCountry)))
      {
        return TypedResults.BadRequest("Para servicios de Warehouse, se requiere pickupZoneId y warehouseCountry");
      }
      
      if (request.ServiceTypeId == 5 && !request.TvhType.HasValue)
      {
        return TypedResults.BadRequest("Para servicios TVH, se requiere tvhType (1=prepaid, 2=collect)");
      }

      var calculationResult = await quotationCalculator.CalculateQuotation(
          request.CustomerId,
          request.ServiceTypeId,
          request.PackageTypeId,
          request.TotalPackageValue,
          request.Packages,
          setupZone,
          additionalServices,
          request.DutyPercentage,
          request.ExtraCharge,
          request.TvhType,      // Para servicio TVH
          request.PickupZoneId, // Para servicio Warehouse
          request.WarehouseCountry,  // Para servicio Warehouse
          request.IncludePickup  // Nuevo parámetro para decidir si incluir pickup
      );

      if (!calculationResult.Success)
        return TypedResults.BadRequest(calculationResult.Error);

      var response = new QuotationResponse
      {
        CustomerId = customer.Id,
        CustomerName = customer.BusinessName,
        ServiceTypeId = request.ServiceTypeId,
        SetupZone = new SetupZoneInfo
        {
          Id = setupZone.Id,
          Country = setupZone.Country,
          Zone = setupZone.Zone,
          Warehouse = setupZone.Warehouse
        },
        Packages = request.Packages
            .Select(
                p =>
                    new PackageResponse
                    {
                      PackageValue = p.PackageValue,
                      VolumetricWeight = p.VolumetricWeight,
                      NetWeight = p.NetWeight,
                      ChargeableWeight = Math.Max(p.VolumetricWeight, p.NetWeight)
                    }
            )
            .ToList(),
        PackageRates = calculationResult.PackageRates,
        TotalsByService = calculationResult.TotalsByService,
        AdditionalServicesCost = calculationResult.AdditionalServicesCost
      };

      return TypedResults.Ok(response);
    }
    catch (Exception ex)
    {
      logger.LogError(ex, "Failed to process import quotation");
      throw;
    }
  }

  public class ImportQuotationRequest
  {
    public int CustomerId { get; set; }
    public int ServiceTypeId { get; set; }
    public int PackageTypeId { get; set; }
    public int? SetupZonesId { get; set; }
    public List<int>? AdditionalServiceIds { get; set; }
    public decimal DutyPercentage { get; set; }
    public decimal ExtraCharge { get; set; } = 0;
    public decimal TotalPackageValue { get; set; }
    public int? TvhType { get; set; }  // Para servicio TVH (1=prepaid, 2=collect)
    public int? PickupZoneId { get; set; } // Para servicio Warehouse
    public string? WarehouseCountry { get; set; } // Para servicio Warehouse (China, Madrid, Miami)
    public bool? IncludePickup { get; set; } // Para servicio Warehouse - determina si se incluye el costo de pickup
    public List<PackageRequest> Packages { get; set; } = new();
    public string? SelectedService { get; set; }
    public string? Type { get; set; }
    public TotalsByService? TotalsByService { get; set; }
    public Dictionary<string,string>? CustomTotalsByService { get; set; }
    public string? Notes { get; set; }
    public string? Description { get; set; }
    public decimal? DutyPercentageSuggested { get; set; }
    public decimal? FobPercentage { get; set; }

    // Lead capture data for RIC website
    public LeadCaptureRequest? LeadData { get; set; }
  }

  public class PackageRequest
  {
    public decimal PackageValue { get; set; }
    public int PackageTypeId { get; set; }
    public decimal VolumetricWeight { get; set; }
    public decimal NetWeight { get; set; }
  }

  public class QuotationResponse
  {
    public int CustomerId { get; set; }
    public string CustomerName { get; set; }
    public int ServiceTypeId { get; set; }
    public SetupZoneInfo SetupZone { get; set; }
    public List<PackageResponse> Packages { get; set; } = new();
    public List<PackageRate> PackageRates { get; set; }
    public Dictionary<string, TotalsByService> TotalsByService { get; set; }
    public decimal AdditionalServicesCost { get; set; }
  }

  public class SetupZoneInfo
  {
    public int Id { get; set; }
    public string Country { get; set; }
    public int? Zone { get; set; }
    public string? Warehouse { get; set; }
  }

  public class PackageResponse
  {
    public decimal PackageValue { get; set; }
    public decimal VolumetricWeight { get; set; }
    public decimal NetWeight { get; set; }
    public decimal ChargeableWeight { get; set; }
  }

  private static async Task SaveLeadQuotation(
      ImportQuotationRequest request,
      AppDbContext dbContext,
      ILogger logger)
  {
    try
    {
      logger.LogInformation("SaveLeadQuotation method called");

      // Validate that LeadData exists and required fields are present
      if (request.LeadData == null ||
          string.IsNullOrWhiteSpace(request.LeadData.FirstName) ||
          string.IsNullOrWhiteSpace(request.LeadData.LastName) ||
          string.IsNullOrWhiteSpace(request.LeadData.Email) ||
          string.IsNullOrWhiteSpace(request.LeadData.PhoneNumber))
      {
        logger.LogWarning("Lead capture attempted but required fields are missing. LeadData: {LeadData}",
            request.LeadData != null ? $"FirstName: {request.LeadData.FirstName}, LastName: {request.LeadData.LastName}, Email: {request.LeadData.Email}, PhoneNumber: {request.LeadData.PhoneNumber}" : "null");
        return; // Don't fail the quotation process, just skip lead saving
      }

      logger.LogInformation("Lead data validation passed for {Email}", request.LeadData.Email);

      // Serialize the entire request to JSON for storage
      var quotationRequestJson = JsonSerializer.Serialize(request, new JsonSerializerOptions
      {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
      });

      // Create the lead quotation record
      var leadQuotation = new LeadQuotation
      {
        FirstName = request.LeadData.FirstName.Trim(),
        LastName = request.LeadData.LastName.Trim(),
        Email = request.LeadData.Email.Trim().ToLowerInvariant(),
        PhoneNumber = request.LeadData.PhoneNumber.Trim(),
        QuotationRequestData = quotationRequestJson,
        CreatedAt = DateTime.UtcNow
      };

      // Save to database
      logger.LogInformation("Adding lead quotation to DbContext for {Email}", leadQuotation.Email);
      dbContext.LeadQuotations.Add(leadQuotation);

      logger.LogInformation("Calling SaveChangesAsync...");
      var result = await dbContext.SaveChangesAsync();
      logger.LogInformation("SaveChangesAsync completed. Rows affected: {RowsAffected}", result);

      logger.LogInformation("Lead quotation saved successfully for {Email} - {FirstName} {LastName}",
          leadQuotation.Email, leadQuotation.FirstName, leadQuotation.LastName);
    }
    catch (Exception ex)
    {
      // Log the error but don't fail the quotation process
      logger.LogError(ex, "Failed to save lead quotation for {Email} - {FirstName} {LastName}",
          request.LeadData?.Email, request.LeadData?.FirstName, request.LeadData?.LastName);
    }
  }
}