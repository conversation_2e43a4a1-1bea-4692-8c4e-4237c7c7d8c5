using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using Serilog;
using Microsoft.AspNetCore.Identity;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.Quotation.Handlers;

public static class GetQuotationListByTenant
{
    public static async Task<Results<Ok<GetAllQuotationsByTenantResponse>, BadRequest<string>, IResult>> Handler(
        //ClaimsPrincipal user,
        [FromQuery] int? tenantId,
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromQuery] string? searchTerm,
        [FromQuery] int? serviceTypeId,
        [FromQuery] QuotationStatus? status,
        [FromQuery] string? type,
        [FromQuery] string? createdBy,
        [FromQuery] DateTime? dateFrom,
        [FromQuery] DateTime? dateTo,
        [FromServices] AppDbContext dbContext,
        [FromServices] ISessionManager sessionManager,
        [FromServices] UserManager<User> userManager,
        HttpContext httpContext
    )
    {
        try
        {
            var currentUser = await sessionManager.GetCurrentUserAsync();
            if (currentUser == null)
            {
                throw new InvalidOperationException("user should be logged in");
            }

            var roles = await userManager.GetRolesAsync(currentUser);
            
            bool isAdmin = roles.Contains("admin") || roles.Contains("seller");
            bool isTenantOwner = roles.Contains("TenantOwner");
            bool isTenantClient = roles.Contains("TenantClient");
            
            if (currentUser == null)
            {
                return TypedResults.BadRequest("Usuario no encontrado");
            }
            
            // Determinar el tenantId a utilizar
            int? targetTenantId = null;
            
            if (isAdmin && tenantId.HasValue)
            {
                // Administradores pueden especificar cualquier tenantId
                targetTenantId = tenantId;
            }
            else if (isTenantOwner)
            {
                // TenantOwner solo puede ver su propio tenant
                // Si se especifica un tenantId, verificar que sea el suyo
                if (tenantId.HasValue && tenantId.Value != currentUser.TenantId)
                {
                    return TypedResults.BadRequest("No tiene permiso para ver cotizaciones de este tenant");
                }
                
                targetTenantId = currentUser.TenantId;
            }
            else if (isTenantClient)
            {
                // TenantClient solo puede ver sus propias cotizaciones
                // Ignoramos el tenantId proporcionado
                targetTenantId = currentUser.TenantId;
            }
            
            var query = dbContext.QuotationResults
                .Include(u => u.Creator).Where(u=>u.Creator.TenantId != null)
                .Include(r => r.RejectionReason)
                // Incluir la relación con el usuario que actualizó (UpdatedByUser)
                .Include(u => u.UpdatedByUser)
                .AsNoTracking()
                .AsQueryable();

            // Aplicar filtros según el rol
            if (isAdmin && !tenantId.HasValue)
            {
                // Admin sin tenantId específico ve todas las cotizaciones
                // No aplicamos filtro adicional
            }
            else if (isTenantOwner && targetTenantId.HasValue)
            {
                // TenantOwner ve todas las cotizaciones de su tenant
                var userIdsInTenant = await dbContext.Users
                    .Where(u => u.TenantId == targetTenantId)
                    .Select(u => u.Id)
                    .ToListAsync();
                
                query = query.Where(q => userIdsInTenant.Contains(q.CreatedById));
            }
            else if (isTenantClient)
            {
                // TenantClient solo ve sus propias cotizaciones
                query = query.Where(q => q.CreatedById == currentUser.Id);
            }
            else
            {
                // Por defecto, usuarios sin tenant o sin rol específico
                // solo ven sus propias cotizaciones
                query = query.Where(q => q.CreatedById == currentUser.Id);
            }

            // Guardar la consulta base para contar por estados (sin paginación)
            var countQuery = query;

            // Configurar fechas por defecto (últimos 2 meses)
            var now = DateTime.UtcNow;
            
            // Asegurar que las fechas sean UTC
            DateTime effectiveDateFrom;
            DateTime effectiveDateTo;
            
            if (dateFrom.HasValue)
            {
                effectiveDateFrom = dateFrom.Value.Kind == DateTimeKind.Unspecified 
                    ? DateTime.SpecifyKind(dateFrom.Value, DateTimeKind.Utc)
                    : dateFrom.Value.ToUniversalTime();
            }
            else
            {
                // Por defecto: hace 2 meses desde hoy
                effectiveDateFrom = now.AddMonths(-24).Date;
            }
            
            if (dateTo.HasValue)
            {
                effectiveDateTo = dateTo.Value.Kind == DateTimeKind.Unspecified 
                    ? DateTime.SpecifyKind(dateTo.Value, DateTimeKind.Utc)
                    : dateTo.Value.ToUniversalTime();
                
                // Si la fecha no tiene hora, establecer al final del día
                if (effectiveDateTo.Hour == 0 && effectiveDateTo.Minute == 0 && effectiveDateTo.Second == 0)
                {
                    effectiveDateTo = effectiveDateTo.Date.AddDays(1).AddTicks(-1);
                }
            }
            else
            {
                // Por defecto: hoy al final del día
                effectiveDateTo = now.Date.AddDays(1).AddTicks(-1);
            }

            // Registrar los parámetros recibidos
            Log.Information("Parámetros de búsqueda: page={Page}, pageSize={PageSize}, sortBy={SortBy}, sortOrder={SortOrder}, searchTerm={SearchTerm}, serviceTypeId={ServiceTypeId}, status={Status}, type={Type}, createdBy={CreatedBy}, dateFrom={DateFrom}, dateTo={DateTo}, effectiveDateFrom={EffectiveDateFrom}, effectiveDateTo={EffectiveDateTo}", 
                page, pageSize, sortBy, sortOrder, searchTerm, serviceTypeId, status, type, createdBy, dateFrom, dateTo, effectiveDateFrom, effectiveDateTo);

            // Aplicar filtro de fecha
            query = query.Where(x => x.CreatedAt >= effectiveDateFrom && x.CreatedAt <= effectiveDateTo);
            countQuery = countQuery.Where(x => x.CreatedAt >= effectiveDateFrom && x.CreatedAt <= effectiveDateTo);

            // Apply filters
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(x => 
                    x.CustomerName.ToLower().Contains(searchTermLower) ||
                    x.CountryOfOrigin.ToLower().Contains(searchTermLower) ||
                    x.QuotationNumber.ToString().Contains(searchTermLower) ||
                    (x.Description != null && x.Description.ToLower().Contains(searchTermLower)) ||
                    (x.Code != null && x.Code.ToLower().Contains(searchTermLower))
                );
                countQuery = countQuery.Where(x => 
                    x.CustomerName.ToLower().Contains(searchTermLower) ||
                    x.CountryOfOrigin.ToLower().Contains(searchTermLower) ||
                    x.QuotationNumber.ToString().Contains(searchTermLower) ||
                    (x.Description != null && x.Description.ToLower().Contains(searchTermLower)) ||
                    (x.Code != null && x.Code.ToLower().Contains(searchTermLower))
                );
            }

            if (serviceTypeId.HasValue)
            {
                query = query.Where(x => x.ServiceTypeId == serviceTypeId.Value);
                countQuery = countQuery.Where(x => x.ServiceTypeId == serviceTypeId.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
                countQuery = countQuery.Where(x => x.Status == status.Value);
            }

            // Filtro por email del creador
            if (!string.IsNullOrWhiteSpace(createdBy))
            {
                query = query.Where(x => x.Creator.Email == createdBy);
                countQuery = countQuery.Where(x => x.Creator.Email == createdBy);
            }

            // Modified type filter to properly handle nullable string
            if (httpContext.Request.Query.ContainsKey("type"))
            {
                if (string.IsNullOrEmpty(type))
                {
                    query = query.Where(x => x.Type == null);
                    countQuery = countQuery.Where(x => x.Type == null);
                }
                else
                {
                    query = query.Where(x => x.Type == type);
                    countQuery = countQuery.Where(x => x.Type == type);
                }
            }

            // Registrar la consulta SQL para el conteo
            Log.Information("Consulta SQL para conteo: {SqlQuery}", countQuery.ToQueryString());

            // Calcular los conteos por estado con una sola consulta
            var statusCounts = await countQuery
                .GroupBy(x => x.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count);

            // Obtener los conteos por estado
            var approvedCount = statusCounts.GetValueOrDefault(QuotationStatus.Accepted, 0);
            var rejectedCount = statusCounts.GetValueOrDefault(QuotationStatus.Rejected, 0);
            var pendingCount = statusCounts.GetValueOrDefault(QuotationStatus.Pending, 0);
            var createdCount = statusCounts.GetValueOrDefault(QuotationStatus.Created, 0);

            // Registrar los conteos
            Log.Information("Conteos de estados: Approved={ApprovedCount}, Rejected={RejectedCount}, Pending={PendingCount}, Created={CreatedCount}",
                approvedCount, rejectedCount, pendingCount, createdCount);

            // Apply sorting
            query = sortBy?.ToLower() switch
            {
                "quotationnumber" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.QuotationNumber)
                    : query.OrderBy(x => x.QuotationNumber),
                "createdat" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.CreatedAt)
                    : query.OrderBy(x => x.CreatedAt),
                "customername" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.CustomerName)
                    : query.OrderBy(x => x.CustomerName),
                "totalpackagevalue" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.TotalPackageValue)
                    : query.OrderBy(x => x.TotalPackageValue),
                "status" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Status)
                    : query.OrderBy(x => x.Status),
                "code" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Code)
                    : query.OrderBy(x => x.Code),
                "description" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Description)
                    : query.OrderBy(x => x.Description),
                _ => sortOrder?.ToLower() == "asc"
                    ? query.OrderByDescending(x => x.Id)
                    : query.OrderBy(x => x.Id)
            };

            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            // Registrar la consulta SQL para conteo total
            Log.Information("Consulta SQL para conteo total: {SqlQuery}", query.ToQueryString());

            var totalCount = await query.CountAsync();
            
            // Registrar el conteo total
            Log.Information("Conteo total de registros: {TotalCount}", totalCount);

            // Registrar la consulta SQL para datos paginados
            var pagedQuery = query.Skip((actualPage - 1) * actualPageSize).Take(actualPageSize);
            Log.Information("Consulta SQL para datos paginados: {SqlQuery}", pagedQuery.ToQueryString());

            var quotations = await pagedQuery.ToListAsync();
            
            // Registrar el número de registros obtenidos
            Log.Information("Número de registros obtenidos: {QuotationsCount}", quotations.Count);

            // Verificar si hay registros específicos en la respuesta
            foreach (var quotation in quotations)
            {
                Log.Information("Registro obtenido: ID={Id}, QuotationNumber={QuotationNumber}, CustomerName={CustomerName}, CreatedAt={CreatedAt}, Status={Status}, Code={Code}",
                    quotation.Id, quotation.QuotationNumber, quotation.CustomerName, quotation.CreatedAt, quotation.Status, quotation.Code);
            }

            var response = new GetAllQuotationsByTenantResponse
            {
                TotalCount = totalCount,
                PageSize = actualPageSize,
                CurrentPage = actualPage,
                TotalPages = (int)Math.Ceiling(totalCount / (double)actualPageSize),
                Quotations = quotations.Select(MapToResponse).ToList(),
                CurrentUserRole = isAdmin ? "Admin" : (isTenantOwner ? "TenantOwner" : (isTenantClient ? "TenantClient" : "Other")),
                // Agregar los conteos por estado
                ApprovedCount = approvedCount,
                RejectedCount = rejectedCount,
                PendingCount = pendingCount,
                CreatedCount = createdCount,
                // Agregar información de fechas utilizadas
                DateFrom = effectiveDateFrom,
                DateTo = effectiveDateTo
            };

            // Registrar la respuesta
            Log.Information("Respuesta generada: TotalCount={TotalCount}, Quotations.Count={QuotationsCount}, DateRange={DateFrom} to {DateTo}",
                response.TotalCount, response.Quotations.Count, response.DateFrom, response.DateTo);

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to retrieve quotations by tenant");
            throw;
        }
    }

    // Método MapToResponse actualizado para usar el email del UpdatedByUser
    public static GetQuotationList.QuotationListResponse MapToResponse(QuotationResult quotation) =>
        new()
        {
            Id = quotation.Id,
            QuotationNumber = quotation.QuotationNumber,
            CreatedBy = quotation.Creator?.Email ?? "Sin usuario",
            // Cambiar para usar el email del usuario que actualizó
            updatedBy = quotation.UpdatedByUser?.Email ?? quotation.UpdatedBy,
            // Alternativa más explícita:
            // updatedBy = quotation.UpdatedByUser?.Email ?? (string.IsNullOrEmpty(quotation.UpdatedBy) ? null : quotation.UpdatedBy),
            CreatedAt = quotation.CreatedAt,
            TotalPackageValue = quotation.TotalPackageValue,
            CountryOfOrigin = quotation.CountryOfOrigin,
            ServiceTypeId = quotation.ServiceTypeId,
            CustomerName = quotation.CustomerName,
            Status = quotation.Status,
            // Agregar las nuevas propiedades
            Description = quotation.Description,
            Code = quotation.Code,
            DutyPercentage = quotation.DutyPercentage,
            DutyPercentageSuggested = quotation.DutyPercentageSuggested,
            FobPercentage = quotation.FobPercentage,
            ApprovedByClient = quotation.ApprovedByClient,
            Notes = quotation.Notes,
            // Campo agregado para motivo de rechazo
            RejectionReason = quotation.RejectionReason,
            QuotationPayload = quotation.QuotationPayload != null ? new GetQuotationList.TotalsByServiceResponse
            {
                Flete = quotation.QuotationPayload.Flete,
                TerminalCharge = quotation.QuotationPayload.TerminalCharge,
                Insurance = quotation.QuotationPayload.Insurance,
                Storage = quotation.QuotationPayload.Storage,
                UpsFuel = quotation.QuotationPayload.UpsFuel,
                SubTotal = quotation.QuotationPayload.SubTotal,
                Estadistica = quotation.QuotationPayload.Estadistica,
                Derecho = quotation.QuotationPayload.Derecho,
                Iva = quotation.QuotationPayload.Iva,
                Total = quotation.QuotationPayload.Total,
                Label = quotation.QuotationPayload.Label,
                CourierLabel = quotation.QuotationPayload.CourierLabel,
                ExportDocument = quotation.QuotationPayload.ExportDocument,
                TasaSumaria = quotation.QuotationPayload.TasaSumaria,
                CollectFee = quotation.QuotationPayload.CollectFee
            } : null,
            CustomQuotationPayload = quotation.CustomQuotationPayload,
            Packages = quotation.Packages?.Select(p => new GetImportQuotation.PackageResponse
            {
                VolumetricWeight = p.VolumetricWeight,
                NetWeight = p.NetWeight
            }).ToList() ?? new List<GetImportQuotation.PackageResponse>()
        };

    public record GetAllQuotationsByTenantResponse
    {
        public int TotalCount { get; init; }
        public int PageSize { get; init; }
        public int CurrentPage { get; init; }
        public int TotalPages { get; init; }
        public List<GetQuotationList.QuotationListResponse> Quotations { get; init; } = new();
        public string CurrentUserRole { get; init; } // Para depuración
        public int ApprovedCount { get; init; }
        public int RejectedCount { get; init; }
        public int PendingCount { get; init; }
        public int CreatedCount { get; init; } // Nueva propiedad agregada
        public DateTime DateFrom { get; init; }
        public DateTime DateTo { get; init; }
    }
}