using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Quotations.Handlers;
using Raiconet.Cotizador.Api.Services.Quotation;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.Quotation.Handlers;

public static class CreateQuotation
{
    public static async Task<Results<Ok<GetImportQuotation.QuotationResponse>, BadRequest<string>, NotFound, IResult>> Handler(
        [FromBody] GetImportQuotation.ImportQuotationRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] IQuotationCalculator quotationCalculator,
        [FromServices] ISessionManager sessionManager,
        [FromServices] UserManager<User> userManager
    )
    {
        try
        {
            var user = await sessionManager.GetCurrentUserAsync();
            if (user == null)
            {
                throw new InvalidOperationException("User should be logged in");
            }

            // Obtener los roles del usuario actual
            var roles = await userManager.GetRolesAsync(user);
            
            // Verificar los roles del usuario
            bool isAdmin = roles.Contains("Admin");
            bool isTenantOwner = roles.Contains("TenantOwner");
            bool isTenantClient = roles.Contains("TenantClient");
            
            // Determinar el estado inicial de la cotización basado en el rol del usuario
            QuotationStatus initialStatus = QuotationStatus.Pending; // Default status (0)
            
            // Si es TenantOwner o TenantClient, asignar status 3 (Created)
            if (isTenantOwner || isTenantClient)
            {
                initialStatus = QuotationStatus.Created; // Status 3
            }
            
            var customer = await dbContext.Customers.FirstOrDefaultAsync(
                c => c.Id == request.CustomerId
            );

            if (customer == null)
                return TypedResults.NotFound();

            var setupZone = await dbContext.SetupZones.FirstOrDefaultAsync(
                z => z.Id == request.SetupZonesId
            );

            if (setupZone == null)
                return TypedResults.NotFound();

            List<AdditionalService> additionalServices = new();
            if (request.AdditionalServiceIds != null && request.AdditionalServiceIds.Any())
            {
                foreach (var serviceId in request.AdditionalServiceIds)
                {
                    var additionalService = await dbContext.AdditionalServices.FirstOrDefaultAsync(
                        a => a.Id == serviceId && a.IsActive
                    );

                    if (additionalService != null)
                    {
                        additionalServices.Add(additionalService);
                    }
                    else
                    {
                        return TypedResults.BadRequest($"Invalid additional service ID: {serviceId}");
                    }
                }
            }

            // Validar datos específicos según el serviceTypeId
            if (request.ServiceTypeId == 3 &&
                (!request.PickupZoneId.HasValue || string.IsNullOrEmpty(request.WarehouseCountry)))
            {
                return TypedResults.BadRequest(
                    "Para servicios de Warehouse, se requiere pickupZoneId y warehouseCountry");
            }

            if (request.ServiceTypeId == 5 && !request.TvhType.HasValue)
            {
                return TypedResults.BadRequest("Para servicios TVH, se requiere tvhType (1=prepaid, 2=collect)");
            }

            // Generar un código único (uid)
            string uniqueCode = Guid.NewGuid().ToString();

            if (request.TotalsByService != null || request.CustomTotalsByService!=null)
            {
                var lq = await dbContext.QuotationResults.OrderByDescending(t => t.QuotationNumber)
                    .FirstOrDefaultAsync();

                var q = new QuotationResult()
                {
                    CustomerName = customer.BusinessName,
                    ServiceTypeId = request.ServiceTypeId,
                    Packages = request.Packages.Select(p => new Package()
                    {
                        NetWeight = p.NetWeight,
                        VolumetricWeight = p.VolumetricWeight
                    }).ToList(),
                    TotalPackageValue = request.TotalPackageValue,
                    Status = initialStatus, // Usar el status basado en el rol
                    CreatedAt = DateTime.UtcNow,
                    CreatedById = user.Id,
                    QuotationPayload = request.TotalsByService,
                    CustomQuotationPayload = request.CustomTotalsByService,
                    CountryOfOrigin = setupZone.Country,
                    QuotationNumber = (lq != null) ? lq.QuotationNumber + 1 : 1,
                    Type = request.Type,
                    Description = request.Description, // Agregar descripción del request
                    Code = uniqueCode, // Agregar código único autogenerado
                    DutyPercentage = request.DutyPercentage, // Agregar DutyPercentage del request
                    DutyPercentageSuggested = request.DutyPercentageSuggested, // Valor sugerido del duty
                    FobPercentage = request.FobPercentage, // Agregar FobPercentage del request
                    ApprovedByClient = false // Por defecto, no está aprobado por el cliente
                };

                await dbContext.QuotationResults.AddAsync(q);
                await dbContext.SaveChangesAsync();

                return TypedResults.Ok(q);
            }

            var calculationResult = await quotationCalculator.CalculateQuotation(
                request.CustomerId,
                request.ServiceTypeId,
                request.PackageTypeId,
                request.TotalPackageValue,
                request.Packages,
                setupZone,
                additionalServices,
                request.DutyPercentage,
                request.ExtraCharge,
                request.TvhType,      // Para servicio TVH
                request.PickupZoneId, // Para servicio Warehouse
                request.WarehouseCountry,  // Para servicio Warehouse
                request.IncludePickup  // Nuevo parámetro para decidir si incluir pickup
            );

            if (!calculationResult.Success)
                return TypedResults.BadRequest(calculationResult.Error);

            var response = new GetImportQuotation.QuotationResponse
            {
                CustomerId = customer.Id,
                CustomerName = customer.BusinessName,
                ServiceTypeId = request.ServiceTypeId,
                SetupZone = new GetImportQuotation.SetupZoneInfo
                {
                    Id = setupZone.Id,
                    Country = setupZone.Country,
                    Zone = setupZone.Zone,
                    Warehouse = setupZone.Warehouse
                },
                Packages = request.Packages
                    .Select(
                        p =>
                            new GetImportQuotation.PackageResponse
                            {
                                PackageValue = p.PackageValue,
                                VolumetricWeight = p.VolumetricWeight,
                                NetWeight = p.NetWeight,
                                ChargeableWeight = Math.Max(p.VolumetricWeight, p.NetWeight)
                            }
                    )
                    .ToList(),
                PackageRates = calculationResult.PackageRates,
                TotalsByService = calculationResult.TotalsByService,
                AdditionalServicesCost = calculationResult.AdditionalServicesCost
            };

            // var serviceExist = calculationResult.TotalsByService.ContainsKey(request.SelectedService ?? "");
            //
            // if (!serviceExist)
            // {
            //     return TypedResults.BadRequest("Service not found");
            // }

            var lastQuotation = await dbContext.QuotationResults.OrderByDescending(t => t.QuotationNumber)
                .FirstOrDefaultAsync();

            var quotation = new QuotationResult()
            {
                CustomerName = customer.BusinessName,
                ServiceTypeId = request.ServiceTypeId,
                Packages = request.Packages.Select(p => new Package()
                {
                    NetWeight = p.NetWeight,
                    VolumetricWeight = p.VolumetricWeight
                }).ToList(),
                TotalPackageValue = request.TotalPackageValue,
                Status = initialStatus, // Usar el status basado en el rol
                CreatedAt = DateTime.UtcNow,
                CreatedById = user.Id,
                QuotationPayload = calculationResult.TotalsByService[request.SelectedService!],
                CustomQuotationPayload = request.CustomTotalsByService,
                CountryOfOrigin = setupZone.Country,
                QuotationNumber = (lastQuotation != null) ? lastQuotation.QuotationNumber + 1 : 1,
                Type = request.Type,
                Notes = request.Notes,
                Description = request.Description, // Agregar descripción del request
                Code = uniqueCode, // Agregar código único autogenerado
                DutyPercentage = request.DutyPercentage, // Agregar DutyPercentage del request
                DutyPercentageSuggested = request.DutyPercentageSuggested, // Valor sugerido del duty
                FobPercentage = request.FobPercentage, // Agregar FobPercentage del request
                ApprovedByClient = false // Por defecto, no está aprobado por el cliente
            };

            await dbContext.QuotationResults.AddAsync(quotation);
            await dbContext.SaveChangesAsync();

            return TypedResults.Ok(quotation);
        }
        catch (Exception ex)
        {
            throw;
        }
    }
}