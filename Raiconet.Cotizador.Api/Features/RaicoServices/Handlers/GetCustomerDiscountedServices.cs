using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Customer.Handlers;

public static class GetCustomerServices
{
    public static async Task<Results<Ok<CustomerServicesResponse>, NotFound<string>, BadRequest<string>, IResult>> Handler(
        [FromRoute] int tenantId,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            // Verificar que el usuario existe
            var customer = await dbContext.Customers
                .FirstOrDefaultAsync(c => c.TenantId == tenantId);

            if (customer == null)
                return TypedResults.NotFound($"Customer with tenant ID {tenantId} not found");

            // Obtener todos los SetupRaicoService disponibles
            var allServices = await dbContext.SetupRaicoServices
                .ToListAsync();

            // Obtener todos los CustomerBillingReason para este cliente
            var customerBillingReasons = await dbContext.CustomerBillingReason
                .Where(cbr => cbr.CustomerId == customer.Id)
                .Include(cbr => cbr.BillingReason)
                .ToListAsync();

            // Mapear a DTOs
            var serviceDtos = new List<ServiceDto>();
            
            foreach (var service in allServices)
            {
                // Buscar si hay algún CustomerBillingReason para este servicio
                var customerBillingReasonsForService = customerBillingReasons
                    .Where(cbr => cbr.BillingReason.SetupRaicoServiceId == service.Id)
                    .ToList();
                
                // Si hay CustomerBillingReason con descuento, el servicio está habilitado
                bool isEnabled = customerBillingReasonsForService.Any(cbr => cbr.Percentage > 0);
                
                // Obtener el porcentaje de descuento, si existe (usamos el primero que encontremos)
                var discountPercentage = isEnabled 
                    ? customerBillingReasonsForService.First(cbr => cbr.Percentage > 0).Percentage 
                    : 0;
                
                // Obtener el BillingReason asociado al descuento, si existe
                var billingReason = isEnabled
                    ? customerBillingReasonsForService.First(cbr => cbr.Percentage > 0).BillingReason
                    : null;
                
                var serviceDto = new ServiceDto
                {
                    ServiceId = service.Id,
                    ServiceCode = service.Code,
                    ServiceDescription = service.Description,
                    Status = service.Status,
                    BillingReasonId = billingReason?.Id ?? 0,
                    BillingReasonCode = billingReason?.Code ?? 0,
                    BillingReasonDescription = billingReason?.Description ?? string.Empty,
                    BillingType = billingReason?.Type != null 
                        ? (BillingTypeCustomer)billingReason.Type 
                        : BillingTypeCustomer.Import, // Valor predeterminado
                    EnableService = isEnabled,
                    DiscountPercentage = discountPercentage
                };
                
                serviceDtos.Add(serviceDto);
            }

            return TypedResults.Ok(new CustomerServicesResponse
            {
                CustomerId = customer.Id,
                UserName = customer.BusinessName,
                TaxId = customer.TaxId,
                Services = serviceDtos
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve services for customer");
            throw;
        }
    }

    public record CustomerServicesResponse
    {
        public int CustomerId { get; init; }
        public string UserName { get; init; }
        public string TaxId { get; init; }
        public List<ServiceDto> Services { get; init; } = new();
    }
    public enum BillingTypeCustomer
    {
        Import = 1,
        Export = 2
    }

    public record ServiceDto
    {
        public int ServiceId { get; init; }
        public string ServiceCode { get; init; }
        public string ServiceDescription { get; init; }
        public bool Status { get; init; }
        public int BillingReasonId { get; init; }
        public int BillingReasonCode { get; init; }
        public string BillingReasonDescription { get; init; }
        public BillingTypeCustomer BillingType { get; init; }
        public bool EnableService { get; init; }
        public decimal DiscountPercentage { get; init; }
    }
}