using Raiconet.Cotizador.Api.Features.Customer.Handlers;

namespace Raiconet.Cotizador.Api.Features.CustomerServices;

public static class CustomerServicesEndpoints
{
    public static void RaicoServicesEndpoints(this WebApplication app)
    {
        var customerServicesGroup = app.MapGroup("customer-services").WithTags("Customer Services");
        //.RequireAuthorization();

        // Customer Services Endpoints
        customerServicesGroup.MapGet("user/{tenantId}/discounted", GetCustomerServices.Handler)
            .WithName("GetCustomerDiscountedServices")
            .WithDescription("Get all services with discount for a specific customer by user ID");
    }
}