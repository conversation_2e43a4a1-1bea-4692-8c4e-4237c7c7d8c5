using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Customer.Handlers;

public static class GetCustomerById
{
    public static async Task<Results<Ok<CustomerByIdResponse>, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var customer = await dbContext.Customers
                .Include(x => x.Type)
                .Include(x => x.AccountExecutiveUser)
                .Include(x => x.AccountExternalExecutive)
                .Include(x => x.Addresses)
                .Include(x => x.Files)
                .Include(x => x.CustomerBillingReason)
                    .ThenInclude(x => x.BillingReason)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (customer == null)
            {
                return TypedResults.NotFound();
            }

            return TypedResults.Ok(new CustomerByIdResponse
            {
                Id = customer.Id,
                UserId = customer.UserId,
                TaxId = customer.TaxId,
                BusinessName = customer.BusinessName,
                CustomerCode = customer.CustomerCode,
                Email = customer.Email,
                TwoFactorEnabled = customer.TwoFactorEnabled,
                ComissionFee = customer.ComissionFee,
                AccountExternalExecutive = customer.AccountExternalExecutive != null ? new AccountExternalExecutiveResponse()
                {
                    Id = customer.AccountExternalExecutive.Id,
                    Name = customer.AccountExternalExecutive.Name,
                    Email = customer.AccountExternalExecutive.Email,
                    AccountExecutiveTypeId = customer.AccountExternalExecutive.AccountExecutiveTypeId
                } : null,
                AccountExecutive = customer.AccountExecutiveUser != null ? new AccountExecutiveOnCustomerResponse()
                {
                    Id = customer.AccountExecutiveUser.Id,
                    Name = customer.AccountExecutiveUser.NormalizedUserName,
                    Email = customer.AccountExecutiveUser.Email,
                } : null,
                
                CustomerType = new CustomerTypeOnClientResponse
                {
                    Id = customer.Type.Id,
                    Name = customer.Type.Name
                },
                PaymentTerm =customer.PaymentTerm,
                Addresses = customer.Addresses?.Select(a => new AddressOnClientResponse
                {
                    Id = a.Id,
                    CountryCode = a.CountryCode,
                    Province = a.Province,
                    City = a.City,
                    StreetAddress = a.StreetAddress,
                    AddressComments = a.AddressComments,
                    PostalCode = a.PostalCode,
                    Phone = a.Phone,
                    Mobile = a.Mobile,
                    LegalAddress = a.LegalAddress,
                    CommercialAddress = a.CommercialAddress,
                    CommercialContact1 = a.CommercialContact1,
                    BillingAddress = a.BillingAddress,
                    RedispatchAddress = a.RedispatchAddress,
                    DeliveryAddress = a.DeliveryAddress,
                    DeliveryContact1 = a.DeliveryContact1,
                }).ToList(),
                Files = customer.Files?.Select(f => new CustomerFileOnClientResponse
                {
                    Id = f.Id,
                    Description = f.Description,
                    Url = f.Url
                }).ToList(),
                BillingReasons = customer.CustomerBillingReason?.Select(br => new CustomerBillingReasonOnClientResponse
                {
                    Id = br.Id,
                    BillingReasonId = br.BillingReasonId,
                    BillingReasonDescription = br.BillingReason.Description,
                    Percentage = br.Percentage,
                    Type = br.BillingReason.Type
                }).ToList()
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve customer");
            throw;
        }
    }

    public record CustomerByIdResponse
    {
        public int Id { get; init; }
        public string? UserId { get; init; }
        public string TaxId { get; init; }
        public string BusinessName { get; init; }
        public int? CustomerCode { get; init; }
        public string Email { get; init; }
        public bool? TwoFactorEnabled { get; init; }
        public AccountExternalExecutiveResponse? AccountExternalExecutive { get; init; }
        public AccountExecutiveOnCustomerResponse? AccountExecutive { get; init; }
        public CustomerTypeOnClientResponse CustomerType { get; init; }
        public int PaymentTerm { get; init; }
        public decimal? ComissionFee { get; init; }
        public List<AddressOnClientResponse> Addresses { get; init; } = new();
        public List<CustomerFileOnClientResponse> Files { get; init; } = new();
        public List<CustomerBillingReasonOnClientResponse> BillingReasons { get; init; } = new();
    }

    public record AccountExternalExecutiveResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
        public string Email { get; init; }
        public int AccountExecutiveTypeId { get; init; }
    }
    public record AccountExecutiveOnCustomerResponse
    {
        public string Id { get; init; }
        public string Name { get; init; }
        public string Email { get; init; }
    }

    public record CustomerTypeOnClientResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
    }

    public record PaymentTermResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
        public int Days { get; init; }
    }

    public record AddressOnClientResponse
    {
        public int Id { get; init; }
        public string CountryCode { get; init; }
        public string Province { get; init; }
        public string City { get; init; }
        public string StreetAddress { get; init; }
        public string? AddressComments { get; init; }
        public string PostalCode { get; init; }
        public string Phone { get; init; }
        public string Mobile { get; init; }
        public string? LegalAddress { get; init; }
        public string? CommercialAddress { get; init; }
        public string? CommercialContact1 { get; init; }
        public string? BillingAddress { get; init; }
        public string? RedispatchAddress { get; init; }
        public string? DeliveryAddress { get; init; }
        public string? DeliveryContact1 { get; init; }
    }

    public record CustomerFileOnClientResponse
    {
        public int Id { get; init; }
        public string Description { get; init; }
        public string Url { get; init; }
    }

    public record CustomerBillingReasonOnClientResponse
    {
        public int Id { get; init; }
        public int BillingReasonId { get; init; }
        public string BillingReasonDescription { get; init; }
        public decimal Percentage { get; init; }
        public BillingType Type { get; init; }
    }
}