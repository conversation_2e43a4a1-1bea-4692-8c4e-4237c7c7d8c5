
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;

namespace Raiconet.Cotizador.Api.Features.Customer.Handlers;

public static class DeleteCustomer
{
    public static async Task<Results<Ok, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var customer = await dbContext.Customers
                .Include(x => x.Addresses)
                .Include(x => x.CustomerBillingReason)
                .Include(x => x.Files)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (customer == null)
            {
                return TypedResults.NotFound();
            }

            // Eliminar relaciones
            dbContext.Addresses.RemoveRange(customer.Addresses);
            dbContext.CustomerBillingReason.RemoveRange(customer.CustomerBillingReason);
            dbContext.CustomerFiles.RemoveRange(customer.Files);

            // Eliminar cliente
            dbContext.Customers.Remove(customer);
            
            await dbContext.SaveChangesAsync();

            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to delete customer");
            throw;
        }
    }
}