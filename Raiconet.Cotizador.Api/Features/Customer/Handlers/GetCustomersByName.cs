using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;

namespace Raiconet.Cotizador.Api.Features.Customer.Handlers;

public static class GetCustomersByName
{
    public static async Task<Results<Ok<List<CustomerSearchResponse>>, IResult>> Handler(
        [FromQuery] string? searchTerm,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.Customers.AsQueryable();
            
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(x => 
                    x.BusinessName.ToLower().Contains(searchTermLower) ||
                    x.TaxId.ToLower().Contains(searchTermLower)
                );
            }

            var customers = await query
                .OrderBy(x => x.BusinessName)
                .Select(x => new CustomerSearchResponse
                {
                    Id = x.Id,
                    BusinessName = x.BusinessName,
                    TaxId = x.TaxId
                })
                .Take(30)
                .ToListAsync();

            return TypedResults.Ok(customers);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve customers for search");
            throw;
        }
    }

    public class CustomerSearchResponse
    {
        public int Id { get; set; }
        public string BusinessName { get; set; }
        public string TaxId { get; set; }
    }
}