using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Customer.Handlers;

public static class CreateCustomer
{
    public static async Task<Results<Ok<CustomerOnCreateResponse>, BadRequest<string>, IResult>> Handler(
        [FromBody] CreateCustomerRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            // Validar existencia de entidades relacionadas
            if (request.AccountExecutiveUserId is not null)
            {
                var accountExecutive = await dbContext.AccountExecutives
                    .FirstOrDefaultAsync(x => x.UserId == request.AccountExecutiveUserId);
                if (accountExecutive == null)
                {
                    var accountExec = new AccountExecutive
                    {
                        UserId = request.AccountExecutiveUserId,
                        Name = (await dbContext.Users.FirstOrDefaultAsync(x=>x.Id == request.AccountExecutiveUserId)).Email,
                        AccountExecutiveTypeId = 1,
                        Email = ""
                    };

                    await dbContext.SaveChangesAsync();
                }
            }

            var customerType = await dbContext.CustomerType
                .FirstOrDefaultAsync(x => x.Id == request.CustomerTypeId);
            if (customerType == null)
                return TypedResults.BadRequest("Invalid customer type ID");

            var paymentTerm = await dbContext.PaymentTerms
                .FirstOrDefaultAsync(x => x.Id == request.PaymentTermId);
            if (paymentTerm == null)
                return TypedResults.BadRequest("Invalid payment term ID");

            // Validar que no exista otro cliente con el mismo TaxId
            var existingCustomer = await dbContext.Customers
                .FirstOrDefaultAsync(x => x.TaxId == request.TaxId);
            if (existingCustomer != null)
                return TypedResults.BadRequest("A customer with this tax ID already exists");

            var customer = new Domain.Customer
            {
                UserId = request.UserId,
                TaxId = request.TaxId,
                BusinessName = request.BusinessName,
                Email = request.Email,
                TwoFactorEnabled = request.TwoFactorEnabled,
                AccountExternalExecutiveId = request.AccountExternalExecutiveId,
                AccountExecutiveUserId = request.AccountExecutiveUserId,
                CustomerTypeId = request.CustomerTypeId,
                PaymentTerm = request.PaymentTerm,
                PaymentTermId = request.PaymentTermId,
                CustomerCode = request.CustomerCode,
                ComissionFee = request.ComissionFee,
                Addresses = request.Addresses.Select(a => new Address
                {
                    CountryCode = a.CountryCode,
                    Province = a.Province,
                    City = a.City,
                    StreetAddress = a.StreetAddress,
                    AddressComments = a.AddressComments,
                    PostalCode = a.PostalCode,
                    Phone = a.Phone,
                    Mobile = a.Mobile,
                    LegalAddress = a.LegalAddress,
                    CommercialAddress = a.CommercialAddress,
                    CommercialContact1 = a.CommercialContact1,
                    BillingAddress = a.BillingAddress,
                    RedispatchAddress = a.RedispatchAddress,
                    DeliveryAddress = a.DeliveryAddress,
                    DeliveryContact1 = a.DeliveryContact1
                }).ToList(),
                CustomerBillingReason = request.BillingReasons.Select(br => new CustomerBillingReason
                {
                    BillingReasonId = br.BillingReasonId,
                    Percentage = br.Percentage
                }).ToList()
            };

            dbContext.Customers.Add(customer);
            await dbContext.SaveChangesAsync();

            // Recargar el cliente con todas sus relaciones
            customer = await dbContext.Customers
                .Include(x => x.Type)
                .Include(x => x.AccountExternalExecutive)
                .Include(x => x.AccountExecutiveUser)
                .Include(x => x.Addresses)
                .Include(x => x.Files)
                .Include(x => x.CustomerBillingReason)
                    .ThenInclude(x => x.BillingReason)
                .FirstAsync(x => x.Id == customer.Id);

            return TypedResults.Ok(MapToResponse(customer));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create customer");
            throw;
        }
    }

    private static CustomerOnCreateResponse MapToResponse(Domain.Customer customer) =>
        new()
        {
            Id = customer.Id,
            UserId = customer.UserId,
            TaxId = customer.TaxId,
            BusinessName = customer.BusinessName,
            CustomerCode = customer.CustomerCode,
            Email = customer.Email,
            TwoFactorEnabled = customer.TwoFactorEnabled,
            ComissionFee = customer.ComissionFee,
            AccountExecutive = customer.AccountExternalExecutive != null ? new AccountExecutiveOnCreateResponse
            {
                Id = customer.AccountExternalExecutive.Id,
                Name = customer.AccountExternalExecutive.Name,
                Email = customer.AccountExternalExecutive.Email,
                AccountExecutiveTypeId = customer.AccountExternalExecutive.AccountExecutiveTypeId
            } : null,
            CustomerType = new CustomerTypeOnCreateResponse
            {
                Id = customer.Type.Id,
                Name = customer.Type.Name
            },
            PaymentTerm= customer.PaymentTerm,
            PaymentTermId= customer.PaymentTermId,
            Addresses = customer.Addresses?.Select(a => new AddressOnCreateResponse
            {
                Id = a.Id,
                CountryCode = a.CountryCode,
                Province = a.Province,
                City = a.City,
                StreetAddress = a.StreetAddress,
                AddressComments = a.AddressComments,
                PostalCode = a.PostalCode,
                Phone = a.Phone,
                Mobile = a.Mobile,
                LegalAddress = a.LegalAddress,
                CommercialAddress = a.CommercialAddress,
                CommercialContact1 = a.CommercialContact1,
                BillingAddress = a.BillingAddress,
                RedispatchAddress = a.RedispatchAddress,
                DeliveryAddress = a.DeliveryAddress,
                DeliveryContact1 = a.DeliveryContact1
            }).ToList(),
            Files = customer.Files?.Select(f => new CustomerFileOnCreateResponse
            {
                Id = f.Id,
                Description = f.Description,
                Url = f.Url
            }).ToList(),
            BillingReasons = customer.CustomerBillingReason?.Select(br => new CustomerBillingReasonResponse
            {
                Id = br.Id,
                BillingReasonId = br.BillingReasonId,
                BillingReasonDescription = br.BillingReason.Description,
                Percentage = br.Percentage
            }).ToList()
        };

    public record CreateCustomerRequest
    {
        public string? UserId { get; init; }
        public required string TaxId { get; init; }
        public required string BusinessName { get; init; }
        public int? CustomerCode { get; init; }
        public required string Email { get; init; }
        public bool? TwoFactorEnabled { get; init; }
        public string? AccountExecutiveUserId { get; init; }
        public int? AccountExternalExecutiveId { get; init; }
        public int CustomerTypeId { get; init; }
        public int PaymentTerm { get; init; }
        public int PaymentTermId { get; init; }
        public decimal? ComissionFee { get; init; }
        public List<CreateAddressRequest> Addresses { get; init; } = new();
        public List<CreateCustomerBillingReasonRequest> BillingReasons { get; init; } = new();
    }

    public record CreateAddressRequest
    {
        public required string CountryCode { get; init; }
        public required string Province { get; init; }
        public required string City { get; init; }
        public required string StreetAddress { get; init; }
        public string? AddressComments { get; init; }
        public required string PostalCode { get; init; }
        public required string Phone { get; init; }
        public required string Mobile { get; init; }
        public string? LegalAddress { get; init; }
        public string? CommercialAddress { get; init; }
        public string? CommercialContact1 { get; init; }
        public string? BillingAddress { get; init; }
        public string? RedispatchAddress { get; init; }
        public string? DeliveryAddress { get; init; }
        public string? DeliveryContact1 { get; init; }

    }

    public record CreateCustomerBillingReasonRequest
    {
        public int BillingReasonId { get; init; }
        public decimal Percentage { get; init; }
    }

    public record CustomerOnCreateResponse
    {
        public int Id { get; init; }
        public string? UserId { get; init; }
        public string TaxId { get; init; }
        public string BusinessName { get; init; }
        public int? CustomerCode { get; init; }
        public string Email { get; init; }
        public bool? TwoFactorEnabled { get; init; }
        public AccountExecutiveOnCreateResponse? AccountExecutive { get; init; }
        public CustomerTypeOnCreateResponse CustomerType { get; init; }
        public int PaymentTerm { get; init; }
        public int PaymentTermId { get; init; }
        public decimal? ComissionFee { get; init; }
        public List<AddressOnCreateResponse> Addresses { get; init; } = new();
        public List<CustomerFileOnCreateResponse> Files { get; init; } = new();
        public List<CustomerBillingReasonResponse> BillingReasons { get; init; } = new();
    }

    public record AccountExecutiveOnCreateResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
        public string Email { get; init; }
        public int AccountExecutiveTypeId { get; init; }
    }

    public record CustomerTypeOnCreateResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
    }

    public record PaymentTermOnCreateResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
        public int Days { get; init; }
    }

    public record AddressOnCreateResponse
    {
        public int Id { get; init; }
        public string CountryCode { get; init; }
        public string Province { get; init; }
        public string City { get; init; }
        public string StreetAddress { get; init; }
        public string? AddressComments { get; init; }
        public string PostalCode { get; init; }
        public string Phone { get; init; }
        public string Mobile { get; init; }
        public string? LegalAddress { get; init; }
        public string? CommercialAddress { get; init; }
        public string? CommercialContact1 { get; init; }
        public string? BillingAddress { get; init; }
        public string? RedispatchAddress { get; init; }
        public string? DeliveryAddress { get; init; }
        public string? DeliveryContact1 { get; init; }

    }

    public record CustomerFileOnCreateResponse
    {
        public int Id { get; init; }
        public string Description { get; init; }
        public string Url { get; init; }
    }

    public record CustomerBillingReasonResponse
    {
        public int Id { get; init; }
        public int BillingReasonId { get; init; }
        public string BillingReasonDescription { get; init; }
        public decimal Percentage { get; init; }
    }
}