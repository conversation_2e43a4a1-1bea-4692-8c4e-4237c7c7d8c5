using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Customer.Handlers;

public static class GetCustomers
{
    public static async Task<Results<Ok<GetAllCustomersResponse>, IResult>> Handler(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromQuery] string? searchTerm,
        [FromQuery] int? customerTypeId,
        [FromQuery] string? accountExecutiveId,
        [FromServices] AppDbContext dbContext
    )
    {
        try
        {
            var query = dbContext.Customers
                .Include(x => x.Type)
                .Include(x => x.AccountExecutiveUser)
                .Include(x => x.Addresses)
                .Include(x => x.Files)
                .Include(x => x.CustomerBillingReason)
                    .ThenInclude(x => x.BillingReason)
                .AsQueryable();

            // Aplicar filtros
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(x => 
                    x.BusinessName.ToLower().Contains(searchTermLower) ||
                    x.TaxId.ToLower().Contains(searchTermLower) ||
                    x.Email.ToLower().Contains(searchTermLower)
                );
            }

            if (customerTypeId.HasValue)
            {
                query = query.Where(x => x.CustomerTypeId == customerTypeId.Value);
            }

            if (accountExecutiveId is not null)
            {
                query = query.Where(x => x.AccountExecutiveUserId == accountExecutiveId);
            }

            // Aplicar ordenamiento
            query = sortBy?.ToLower() switch
            {
                "businessname" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.BusinessName)
                    : query.OrderBy(x => x.BusinessName),
                "taxid" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.TaxId)
                    : query.OrderBy(x => x.TaxId),
                "email" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Email)
                    : query.OrderBy(x => x.Email),
                _ => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Id)
                    : query.OrderBy(x => x.Id)
            };

            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            var totalCount = await query.CountAsync();
            var customers = await query
                .Skip((actualPage - 1) * actualPageSize)
                .Take(actualPageSize)
                .ToListAsync();

            var response = new GetAllCustomersResponse
            {
                TotalCount = totalCount,
                PageSize = actualPageSize,
                CurrentPage = actualPage,
                TotalPages = (int)Math.Ceiling(totalCount / (double)actualPageSize),
                Customers = customers.Select(MapToResponse).ToList()
            };

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to retrieve customers");
            throw;
        }
    }

    private static CustomerAllResponse MapToResponse(Domain.Customer customer) =>
        new()
        {
            Id = customer.Id,
            UserId = customer.UserId,
            CustomerCode = customer.CustomerCode,
            TaxId = customer.TaxId,
            BusinessName = customer.BusinessName,
            Email = customer.Email,
            TwoFactorEnabled = customer.TwoFactorEnabled,
            ComissionFee = customer.ComissionFee,
            
            AccountExternalExecutive = customer.AccountExternalExecutive != null ? new AccountExternalExecutiveOnGetResponse()
            {
                Id = customer.AccountExternalExecutive.Id,
                Name = customer.AccountExternalExecutive.Name,
                Email = customer.AccountExternalExecutive.Email,
                AccountExecutiveTypeId = customer.AccountExternalExecutive.AccountExecutiveTypeId
            } : null,
            AccountExecutive = customer.AccountExecutiveUser != null ? new AccountExecutiveOnGetResponse()
            {
                Id = customer.AccountExecutiveUser.Id,
                Name = customer.AccountExecutiveUser.NormalizedUserName,
                Email = customer.AccountExecutiveUser.Email,
            } : null,
            CustomerType = new CustomerTypeOnGetResponse
            {
                Id = customer.Type.Id,
                Name = customer.Type.Name
            },
            PaymentTerm = customer.PaymentTerm,
            Addresses = customer.Addresses?.Select(a => new AddressOnGetResponse
            {
                Id = a.Id,
                CountryCode = a.CountryCode,
                Province = a.Province,
                City = a.City,
                StreetAddress = a.StreetAddress,
                AddressComments = a.AddressComments,
                PostalCode = a.PostalCode,
                Phone = a.Phone,
                Mobile = a.Mobile,
                LegalAddress = a.LegalAddress,
                CommercialAddress = a.CommercialAddress,
                CommercialContact1 = a.CommercialContact1,
                BillingAddress = a.BillingAddress,
                RedispatchAddress = a.RedispatchAddress,
                DeliveryAddress = a.DeliveryAddress,
                DeliveryContact1 = a.DeliveryContact1,
            }).ToList(),
            Files = customer.Files?.Select(f => new CustomerFileOnGetResponse
            {
                Id = f.Id,
                Description = f.Description,
                Url = f.Url
            }).ToList(),
            BillingReasons = customer.CustomerBillingReason?.Select(br => new CustomerBillingReasonOnGetResponse
            {
                Id = br.Id,
                BillingReasonId = br.BillingReasonId,
                BillingReasonDescription = br.BillingReason.Description,
                Percentage = br.Percentage,
                Type = br.BillingReason.Type,
            }).ToList()
        };

    public record GetAllCustomersResponse
    {
        public int TotalCount { get; init; }
        public int PageSize { get; init; }
        public int CurrentPage { get; init; }
        public int TotalPages { get; init; }
        public List<CustomerAllResponse> Customers { get; init; } = new();
    }

    public record CustomerAllResponse
    {
        public int Id { get; init; }
        public string UserId { get; init; }
        public int? CustomerCode { get; init; }
        public string TaxId { get; init; }
        public string BusinessName { get; init; }
        public string Email { get; init; }
        public bool? TwoFactorEnabled { get; init; }
        public AccountExternalExecutiveOnGetResponse? AccountExternalExecutive { get; init; }
        public AccountExecutiveOnGetResponse? AccountExecutive { get; init; }
        public CustomerTypeOnGetResponse CustomerType { get; init; }
        public int PaymentTerm { get; init; }
        public decimal? ComissionFee { get; init; }
        public List<AddressOnGetResponse> Addresses { get; init; } = new();
        public List<CustomerFileOnGetResponse> Files { get; init; } = new();
        public List<CustomerBillingReasonOnGetResponse> BillingReasons { get; init; } = new();
    }

    public record AccountExternalExecutiveOnGetResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
        public string Email { get; init; }
        public int AccountExecutiveTypeId { get; init; }
    }
    public record AccountExecutiveOnGetResponse
    {
        public string Id { get; init; }
        public string Name { get; init; }
        public string Email { get; init; }
    }

    public record CustomerTypeOnGetResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
    }

    public record PaymentTermResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
        public int Days { get; init; }
    }

    public record AddressOnGetResponse
    {
        public int Id { get; init; }
        public string CountryCode { get; init; }
        public string Province { get; init; }
        public string City { get; init; }
        public string StreetAddress { get; init; }
        public string AddressComments { get; init; }
        public string PostalCode { get; init; }
        public string Phone { get; init; }
        public string Mobile { get; init; }
        public string? LegalAddress { get; init; }
        public string? CommercialAddress { get; init; }
        public string? CommercialContact1 { get; init; }
        public string? BillingAddress { get; init; }
        public string? RedispatchAddress { get; init; }
        public string? DeliveryAddress { get; init; }
        public string? DeliveryContact1 { get; init; }
    }

    public record CustomerFileOnGetResponse
    {
        public int Id { get; init; }
        public string Description { get; init; }
        public string Url { get; init; }
    }

    public record CustomerBillingReasonOnGetResponse
    {
        public int Id { get; init; }
        public int BillingReasonId { get; init; }
        public string BillingReasonDescription { get; init; }
        public BillingType Type { get; init; }
        public decimal Percentage { get; init; }
    }
}