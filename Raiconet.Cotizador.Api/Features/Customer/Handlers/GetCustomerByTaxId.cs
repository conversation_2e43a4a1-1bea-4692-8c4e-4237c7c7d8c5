using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using System.Text.RegularExpressions;
using Raiconet.Cotizador.Api.Features.Setup.Handlers;

namespace Raiconet.Cotizador.Api.Features.Customer.Handlers;

public static class GetCustomerByTaxId
{
    public static async Task<Results<Ok<CustomerTaxIdResponse>, NotFound, BadRequest<string>, IResult>> Handler(
        [FromQuery] string taxId,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            if (string.IsNullOrWhiteSpace(taxId))
            {
                return TypedResults.BadRequest("El CUIT es requerido");
            }

            // Validar formato del CUIT (XX-XXXXXXXX-X)
            var cuitRegex = new Regex(@"^\d{2}-\d{8}-\d{1}$");
            if (!cuitRegex.IsMatch(taxId))
            {
                return TypedResults.BadRequest("Formato de CUIT inválido. El formato esperado es XX-XXXXXXXX-X (ej. 30-********-4)");
            }

            // Validar el dígito verificador del CUIT
            if (!ValidarCuitDigitoVerificador(taxId))
            {
                return TypedResults.BadRequest("El CUIT ingresado no es válido (dígito verificador incorrecto)");
            }

            var customer = await dbContext.Customers
                .Include(x => x.Type)
                .Include(x => x.AccountExecutiveUser)
                .Include(x => x.AccountExternalExecutive)
                .Include(x => x.Addresses)
                .Include(x => x.Files)
                .Include(x => x.CustomerBillingReason)
                    .ThenInclude(x => x.BillingReason)
                .FirstOrDefaultAsync(x => x.TaxId == taxId);

            if (customer == null)
            {
                return TypedResults.NotFound();
            }

            return TypedResults.Ok(new CustomerTaxIdResponse
            {
                Id = customer.Id,
                UserId = customer.UserId,
                TaxId = customer.TaxId,
                BusinessName = customer.BusinessName,
                CustomerCode = customer.CustomerCode,
                Email = customer.Email,
                TenantId = customer.TenantId,
                TwoFactorEnabled = customer.TwoFactorEnabled,
                ComissionFee = customer.ComissionFee,
                AccountExternalExecutive = customer.AccountExternalExecutive != null ? new GetCustomerById.AccountExternalExecutiveResponse()
                {
                    Id = customer.AccountExternalExecutive.Id,
                    Name = customer.AccountExternalExecutive.Name,
                    Email = customer.AccountExternalExecutive.Email,
                    AccountExecutiveTypeId = customer.AccountExternalExecutive.AccountExecutiveTypeId
                } : null,
                AccountExecutive = customer.AccountExecutiveUser != null ? new AccountExecutiveResponse()
                {
                    Id = customer.AccountExecutiveUser.Id,
                    Name = customer.AccountExecutiveUser.NormalizedUserName,
                    Email = customer.AccountExecutiveUser.Email
                } : null,
                CustomerType = new GetCustomerTypes.CustomerTypeResponse
                {
                    Id = customer.Type.Id,
                    Name = customer.Type.Name
                },
                PaymentTerm = customer.PaymentTerm,
                Addresses = customer.Addresses?.Select(a => new AddressResponse
                {
                    Id = a.Id,
                    CountryCode = a.CountryCode,
                    Province = a.Province,
                    City = a.City,
                    StreetAddress = a.StreetAddress,
                    AddressComments = a.AddressComments,
                    PostalCode = a.PostalCode,
                    Phone = a.Phone,
                    Mobile = a.Mobile,
                    LegalAddress = a.LegalAddress,
                    CommercialAddress = a.CommercialAddress,
                    CommercialContact1 = a.CommercialContact1,
                    BillingAddress = a.BillingAddress,
                    RedispatchAddress = a.RedispatchAddress,
                    DeliveryAddress = a.DeliveryAddress,
                    DeliveryContact1 = a.DeliveryContact1
                }).ToList() ?? new List<AddressResponse>(),
                Files = customer.Files?.Select(f => new CustomerFileResponse
                {
                    Id = f.Id,
                    Description = f.Description,
                    Url = f.Url
                }).ToList() ?? new List<CustomerFileResponse>(),
                BillingReasons = customer.CustomerBillingReason?.Select(br => new CreateCustomer.CustomerBillingReasonResponse
                {
                    Id = br.Id,
                    BillingReasonId = br.BillingReasonId,
                    BillingReasonDescription = br.BillingReason.Description,
                    Percentage = br.Percentage
                }).ToList() ?? new List<CreateCustomer.CustomerBillingReasonResponse>()
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error al obtener cliente por CUIT");
            throw;
        }
    }

    /// <summary>
    /// Valida el dígito verificador del CUIT según el algoritmo oficial
    /// </summary>
    /// <param name="cuit">CUIT en formato XX-XXXXXXXX-X</param>
    /// <returns>True si el dígito verificador es correcto</returns>
    private static bool ValidarCuitDigitoVerificador(string cuit)
    {
        // Eliminar guiones
        string cuitLimpio = cuit.Replace("-", "");
        
        if (cuitLimpio.Length != 11 || !long.TryParse(cuitLimpio, out _))
        {
            return false;
        }

        // Factores para cada posición
        int[] factores = { 5, 4, 3, 2, 7, 6, 5, 4, 3, 2 };
        
        // Calcular suma de productos
        int suma = 0;
        for (int i = 0; i < 10; i++)
        {
            suma += int.Parse(cuitLimpio[i].ToString()) * factores[i];
        }

        // Calcular dígito verificador
        int resto = suma % 11;
        int verificador;
        
        if (resto == 0)
        {
            verificador = 0;
        }
        else if (resto == 1)
        {
            // Casos especiales según AFIP
            char primerDigito = cuitLimpio[0];
            char segundoDigito = cuitLimpio[1];
            
            // Este código debe tener un manejo especial cuando el resto da 1
            if (primerDigito == '2' && segundoDigito == '0')
            {
                // Masculino: 20 se reemplaza por 23
                return false; // Debería ser CUIT tipo 23
            }
            else if (primerDigito == '2' && segundoDigito == '7')
            {
                // Femenino: 27 se reemplaza por 23
                return false; // Debería ser CUIT tipo 23
            }
            else if (primerDigito == '3' && segundoDigito == '0')
            {
                // Empresa: 30 se reemplaza por 33
                return false; // Debería ser CUIT tipo 33
            }
            else
            {
                verificador = 9;
            }
        }
        else
        {
            verificador = 11 - resto;
        }

        // Comparar con el último dígito del CUIT
        int digitoIngresado = int.Parse(cuitLimpio[10].ToString());
        return digitoIngresado == verificador;
    }
    
    public class CustomerTaxIdResponse
    {
        public int Id { get; set; }
        public string? UserId { get; set; }
        public string TaxId { get; set; }
        public string BusinessName { get; set; }
        public int? CustomerCode { get; set; }
        public string Email { get; set; }
        public bool? TwoFactorEnabled { get; set; }
        public int? TenantId { get; set; }
        public GetCustomerById.AccountExternalExecutiveResponse? AccountExternalExecutive { get; set; }
        public AccountExecutiveResponse? AccountExecutive { get; set; }
        public GetCustomerTypes.CustomerTypeResponse CustomerType { get; set; }
        public int PaymentTerm { get; set; }
        public decimal? ComissionFee { get; set; }
        public List<AddressResponse> Addresses { get; set; } = new();
        public List<CustomerFileResponse> Files { get; set; } = new();
        public List<CreateCustomer.CustomerBillingReasonResponse> BillingReasons { get; set; } = new();
    }
    //
    // public class AccountExternalExecutiveResponse
    // {
    //     public int Id { get; set; }
    //     public string Name { get; set; }
    //     public string Email { get; set; }
    //     public int AccountExecutiveTypeId { get; set; }
    // }
    
    public class AccountExecutiveResponse
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
    }
    
    // public class CustomerTypeResponse
    // {
    //     public int Id { get; set; }
    //     public string Name { get; set; }
    // }
    
    public class AddressResponse
    {
        public int Id { get; set; }
        public string CountryCode { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string StreetAddress { get; set; }
        public string? AddressComments { get; set; }
        public string PostalCode { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }
        public string? LegalAddress { get; set; }
        public string? CommercialAddress { get; set; }
        public string? CommercialContact1 { get; set; }
        public string? BillingAddress { get; set; }
        public string? RedispatchAddress { get; set; }
        public string? DeliveryAddress { get; set; }
        public string? DeliveryContact1 { get; set; }
    }
    
    public class CustomerFileResponse
    {
        public int Id { get; set; }
        public string Description { get; set; }
        public string Url { get; set; }
    }
    
    public class CustomerBillingReasonResponse
    {
        public int Id { get; set; }
        public int BillingReasonId { get; set; }
        public string BillingReasonDescription { get; set; }
        public decimal Percentage { get; set; }
        public BillingType Type { get; set; }
    }
}