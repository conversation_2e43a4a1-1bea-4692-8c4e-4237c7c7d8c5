using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Customer.Handlers;

public static class UpdateCustomer
{
    public static async Task<Results<Ok<CustomerOnUpdateResponse>, NotFound, BadRequest<string>, IResult>> Handler(
        [FromRoute] int id,
        [FromBody] PatchCustomerRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var customer = await dbContext.Customers
                .Include(x => x.Type)
                .Include(x => x.AccountExecutiveUser)
                .Include(x => x.Addresses)
                .Include(x => x.Files)
                .Include(x => x.CustomerBillingReason)
                    .ThenInclude(x => x.BillingReason)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (customer == null)
            {
                return TypedResults.NotFound();
            }

            // Actualizar solo los campos proporcionados
            if (request.BusinessName != null)
                customer.BusinessName = request.BusinessName;

            if (request.Email != null)
                customer.Email = request.Email;

            if (request.TaxId != null)
            {
                // Validar TaxId único si se está actualizando
                var existingCustomer = await dbContext.Customers
                    .FirstOrDefaultAsync(x => x.TaxId == request.TaxId && x.Id != id);
                if (existingCustomer != null)
                    return TypedResults.BadRequest("A customer with this tax ID already exists");
                
                customer.TaxId = request.TaxId;
            }

            if (request.TwoFactorEnabled.HasValue)
                customer.TwoFactorEnabled = request.TwoFactorEnabled;
            
            if (request.ComissionFee.HasValue)
                customer.ComissionFee = request.ComissionFee;

            if (request.AccountExecutiveId.HasValue)
            {
                // Asegúrate de que esta entidad exista en tu base de datos
                var accountExecutive = await dbContext.AccountExecutives
                    .FirstOrDefaultAsync(x => x.Id == request.AccountExecutiveId);
                if (accountExecutive == null)
                    return TypedResults.BadRequest("Invalid account executive ID");
                
                customer.AccountExecutiveUserId = request.AccountExecutiveId.Value.ToString();
            }

            if (request.CustomerTypeId.HasValue)
            {
                var customerType = await dbContext.CustomerType
                    .FirstOrDefaultAsync(x => x.Id == request.CustomerTypeId);
                if (customerType == null)
                    return TypedResults.BadRequest("Invalid customer type ID");

                customer.CustomerTypeId = request.CustomerTypeId.Value;
            }

            if (request.PaymentTermId.HasValue)
            {
                var paymentTerm = await dbContext.PaymentTerms
                    .FirstOrDefaultAsync(x => x.Id == request.PaymentTermId);
                if (paymentTerm == null)
                    return TypedResults.BadRequest("Invalid payment term ID");

                customer.PaymentTermId = request.PaymentTermId.Value;
            }

            // Actualizar archivos si se proporcionan
            if (request.Files != null)
            {
                var filesToUpdate = new List<CustomerFile>();
                
                foreach (var fileDto in request.Files)
                {
                    // Si tiene ID, actualizar el existente
                    if (fileDto.Id.HasValue)
                    {
                        var existingFile = customer.Files.FirstOrDefault(f => f.Id == fileDto.Id.Value);
                        if (existingFile != null)
                        {
                            if (fileDto.Description != null)
                                existingFile.Description = fileDto.Description;
                            
                            if (fileDto.Url != null)
                                existingFile.Url = fileDto.Url;
                            
                            filesToUpdate.Add(existingFile);
                        }
                    }
                    else
                    {
                        // Validar que los campos necesarios estén presentes
                        if (string.IsNullOrEmpty(fileDto.Description) || string.IsNullOrEmpty(fileDto.Url))
                        {
                            logger.LogWarning("Attempt to add new file with missing required fields. Skipping this file.");
                            continue;
                        }
                        
                        // Agregar nuevo archivo
                        filesToUpdate.Add(new CustomerFile
                        {
                            Description = fileDto.Description,
                            Url = fileDto.Url
                        });
                    }
                }
                
                // Eliminar archivos que ya no existen en la solicitud
                var filesToRemove = customer.Files
                    .Where(f => !request.Files.Any(rf => rf.Id.HasValue && rf.Id.Value == f.Id))
                    .ToList();
                    
                foreach (var fileToRemove in filesToRemove)
                {
                    customer.Files.Remove(fileToRemove);
                    dbContext.CustomerFiles.Remove(fileToRemove);
                }
                
                // Limpiar y establecer los nuevos archivos
                customer.Files.Clear();
                foreach (var file in filesToUpdate)
                {
                    customer.Files.Add(file);
                }
            }

            // Actualizar direcciones si se proporcionan
            if (request.Addresses != null)
            {
                // Enfoque para actualizar direcciones evitando duplicación de claves
                var addressesToUpdate = new List<Address>();
                
                foreach (var addressDto in request.Addresses)
                {
                    // Si tiene ID, actualizar la existente
                    if (addressDto.Id.HasValue)
                    {
                        var existingAddress = customer.Addresses.FirstOrDefault(a => a.Id == addressDto.Id.Value);
                        if (existingAddress != null)
                        {
                            // Solo actualizar las propiedades que están presentes en el payload
                            if (addressDto.CountryCode != null)
                                existingAddress.CountryCode = addressDto.CountryCode;
                            
                            if (addressDto.Province != null)
                                existingAddress.Province = addressDto.Province;
                            
                            if (addressDto.City != null)
                                existingAddress.City = addressDto.City;
                            
                            if (addressDto.StreetAddress != null)
                                existingAddress.StreetAddress = addressDto.StreetAddress;
                            
                            if (addressDto.AddressComments != null)
                                existingAddress.AddressComments = addressDto.AddressComments;
                            
                            if (addressDto.PostalCode != null)
                                existingAddress.PostalCode = addressDto.PostalCode;
                            
                            if (addressDto.Phone != null)
                                existingAddress.Phone = addressDto.Phone;
                            
                            if (addressDto.Mobile != null)
                                existingAddress.Mobile = addressDto.Mobile;
                            
                            if (addressDto.LegalAddress != null)
                                existingAddress.LegalAddress = addressDto.LegalAddress;
                            
                            if (addressDto.CommercialAddress != null)
                                existingAddress.CommercialAddress = addressDto.CommercialAddress;
                            
                            if (addressDto.CommercialContact1 != null)
                                existingAddress.CommercialContact1 = addressDto.CommercialContact1;
                            
                            if (addressDto.BillingAddress != null)
                                existingAddress.BillingAddress = addressDto.BillingAddress;
                            
                            if (addressDto.RedispatchAddress != null)
                                existingAddress.RedispatchAddress = addressDto.RedispatchAddress;
                            
                            if (addressDto.DeliveryAddress != null)
                                existingAddress.DeliveryAddress = addressDto.DeliveryAddress;
                            
                            if (addressDto.DeliveryContact1 != null)
                                existingAddress.DeliveryContact1 = addressDto.DeliveryContact1;
                            
                            addressesToUpdate.Add(existingAddress);
                        }
                    }
                    else
                    {
                        // Agregar nueva dirección
                        // Para nuevas direcciones, asegúrate de que los campos requeridos estén presentes
                        if (addressDto.CountryCode == null || 
                            addressDto.Province == null || 
                            addressDto.City == null || 
                            addressDto.StreetAddress == null || 
                            addressDto.PostalCode == null || 
                            addressDto.Phone == null || 
                            addressDto.Mobile == null)
                        {
                            // Loguear advertencia sobre campos requeridos faltantes para nuevas direcciones
                            logger.LogWarning("Attempt to add new address with missing required fields. Skipping this address.");
                            continue;
                        }
                        
                        addressesToUpdate.Add(new Address
                        {
                            CountryCode = addressDto.CountryCode,
                            Province = addressDto.Province,
                            City = addressDto.City,
                            StreetAddress = addressDto.StreetAddress,
                            AddressComments = addressDto.AddressComments,
                            PostalCode = addressDto.PostalCode,
                            Phone = addressDto.Phone,
                            Mobile = addressDto.Mobile,
                            LegalAddress = addressDto.LegalAddress,
                            CommercialAddress = addressDto.CommercialAddress,
                            CommercialContact1 = addressDto.CommercialContact1,
                            BillingAddress = addressDto.BillingAddress,
                            RedispatchAddress = addressDto.RedispatchAddress,
                            DeliveryAddress = addressDto.DeliveryAddress,
                            DeliveryContact1 = addressDto.DeliveryContact1,
                        });
                    }
                }
                
                // Eliminar direcciones que ya no existen en la solicitud
                var addressesToRemove = customer.Addresses
                    .Where(a => !request.Addresses.Any(ra => ra.Id.HasValue && ra.Id.Value == a.Id))
                    .ToList();
                    
                foreach (var addressToRemove in addressesToRemove)
                {
                    customer.Addresses.Remove(addressToRemove);
                    dbContext.Addresses.Remove(addressToRemove);
                }
                
                // Limpiar y establecer las nuevas direcciones
                customer.Addresses.Clear();
                foreach (var address in addressesToUpdate)
                {
                    customer.Addresses.Add(address);
                }
            }

            // Actualizar razones de facturación si se proporcionan
            if (request.BillingReasons != null)
            {
                // Enfoque para actualizar BillingReasons evitando problemas de duplicación
                var billingReasonsToUpdate = new List<CustomerBillingReason>();
                
                foreach (var billingReasonDto in request.BillingReasons)
                {
                    // Si tiene ID, actualizar la existente
                    if (billingReasonDto.Id.HasValue)
                    {
                        var existingBillingReason = customer.CustomerBillingReason.FirstOrDefault(br => br.Id == billingReasonDto.Id.Value);
                        if (existingBillingReason != null)
                        {
                            // Solo actualizar cuando se proporciona un valor
                            if (billingReasonDto.BillingReasonId > 0)
                                existingBillingReason.BillingReasonId = billingReasonDto.BillingReasonId;
                            
                            // Para valores decimales, verificar si es diferente de default(decimal)
                            if (billingReasonDto.Percentage != default)
                                existingBillingReason.Percentage = billingReasonDto.Percentage;
                            
                            billingReasonsToUpdate.Add(existingBillingReason);
                        }
                    }
                    else
                    {
                        // Agregar nueva razón de facturación
                        billingReasonsToUpdate.Add(new CustomerBillingReason
                        {
                            BillingReasonId = billingReasonDto.BillingReasonId,
                            Percentage = billingReasonDto.Percentage
                        });
                    }
                }
                
                // Eliminar razones que ya no existen en la solicitud
                var billingReasonsToRemove = customer.CustomerBillingReason
                    .Where(br => !request.BillingReasons.Any(rbr => rbr.Id.HasValue && rbr.Id.Value == br.Id))
                    .ToList();
                    
                foreach (var billingReasonToRemove in billingReasonsToRemove)
                {
                    customer.CustomerBillingReason.Remove(billingReasonToRemove);
                    dbContext.CustomerBillingReason.Remove(billingReasonToRemove);
                }
                
                // Limpiar y establecer las nuevas razones
                customer.CustomerBillingReason.Clear();
                foreach (var billingReason in billingReasonsToUpdate)
                {
                    customer.CustomerBillingReason.Add(billingReason);
                }
            }

            await dbContext.SaveChangesAsync();

            // Recargar el cliente para obtener todas las relaciones actualizadas
            await dbContext.Entry(customer).ReloadAsync();

            return TypedResults.Ok(MapToResponse(customer));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to patch customer");
            throw;
        }
    }

    public record PatchCustomerRequest
    {
        public string? TaxId { get; init; }
        public string? BusinessName { get; init; }
        public string? Email { get; init; }
        public bool? TwoFactorEnabled { get; init; }
        public int? AccountExecutiveId { get; init; }
        public int? CustomerTypeId { get; init; }
        public int? PaymentTermId { get; init; }
        public int? ComissionFee { get; init; }
        public List<PatchAddressRequest>? Addresses { get; init; }
        public List<PatchCustomerBillingReasonRequest>? BillingReasons { get; init; }
        public List<PatchCustomerFileRequest>? Files { get; init; }
    }

    public record PatchAddressRequest
    {
        public int? Id { get; init; }
        public string? CountryCode { get; init; }
        public string? Province { get; init; }
        public string? City { get; init; }
        public string? StreetAddress { get; init; }
        public string? AddressComments { get; init; }
        public string? PostalCode { get; init; }
        public string? Phone { get; init; }
        public string? Mobile { get; init; }
        public string? LegalAddress { get; init; }
        public string? CommercialAddress { get; init; }
        public string? CommercialContact1 { get; init; }
        public string? BillingAddress { get; init; }
        public string? RedispatchAddress { get; init; }
        public string? DeliveryAddress { get; init; }
        public string? DeliveryContact1 { get; init; }
    }

    public record PatchCustomerBillingReasonRequest
    {
        public int? Id { get; init; }
        public int BillingReasonId { get; init; }
        public decimal Percentage { get; init; }
    }
    
    public record PatchCustomerFileRequest
    {
        public int? Id { get; init; }
        public string? Description { get; init; }
        public string? Url { get; init; }
    }
    
    private static CustomerOnUpdateResponse MapToResponse(Domain.Customer customer) =>
        new()
        {
            Id = customer.Id,
            UserId = customer.UserId,
            TaxId = customer.TaxId,
            BusinessName = customer.BusinessName,
            Email = customer.Email,
            TwoFactorEnabled = customer.TwoFactorEnabled,
            ComissionFee = customer.ComissionFee,
            AccountExecutive = customer.AccountExternalExecutive != null ? new AccountExecutiveOnUpdateResponse
            {
                Id = customer.AccountExternalExecutive.Id,
                Name = customer.AccountExternalExecutive.Name,
                Email = customer.AccountExternalExecutive.Email,
                AccountExecutiveTypeId = customer.AccountExternalExecutive.AccountExecutiveTypeId
            } : null,
            CustomerType = new CustomerTypeOnUpdateResponse
            {
                Id = customer.Type.Id,
                Name = customer.Type.Name
            },
            PaymentTerm = customer.PaymentTerm,
            Addresses = customer.Addresses?.Select(a => new AddressOnUpdateResponse
            {
                Id = a.Id,
                CountryCode = a.CountryCode,
                Province = a.Province,
                City = a.City,
                StreetAddress = a.StreetAddress,
                AddressComments = a.AddressComments,
                PostalCode = a.PostalCode,
                Phone = a.Phone,
                Mobile = a.Mobile,
                LegalAddress = a.LegalAddress,
                CommercialAddress = a.CommercialAddress,
                CommercialContact1 = a.CommercialContact1,
                BillingAddress = a.BillingAddress,
                RedispatchAddress = a.RedispatchAddress,
                DeliveryAddress = a.DeliveryAddress,
                DeliveryContact1 = a.DeliveryContact1
            }).ToList(),
            Files = customer.Files?.Select(f => new CustomerFileOnUpdateResponse
            {
                Id = f.Id,
                Description = f.Description,
                Url = f.Url
            }).ToList(),
            BillingReasons = customer.CustomerBillingReason?.Select(br => new CustomerBillingReasonOnUpdateResponse
            {
                Id = br.Id,
                BillingReasonId = br.BillingReasonId,
                BillingReasonDescription = br.BillingReason?.Description,
                Percentage = br.Percentage
            }).ToList()
        };
    
    

    public record CustomerOnUpdateResponse
    {
        public int Id { get; init; }
        public string UserId { get; init; }
        public string TaxId { get; init; }
        public string BusinessName { get; init; }
        public string Email { get; init; }
        public bool? TwoFactorEnabled { get; init; }
        public AccountExecutiveOnUpdateResponse AccountExecutive { get; init; }
        public CustomerTypeOnUpdateResponse CustomerType { get; init; }
        public int PaymentTerm { get; init; }
        public decimal? ComissionFee { get; init; }
        public List<AddressOnUpdateResponse> Addresses { get; init; } = new();
        public List<CustomerFileOnUpdateResponse> Files { get; init; } = new();
        public List<CustomerBillingReasonOnUpdateResponse> BillingReasons { get; init; } = new();
    }
    
    public record AccountExecutiveOnUpdateResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
        public string Email { get; init; }
        public int AccountExecutiveTypeId { get; init; }
    }

    public record CustomerTypeOnUpdateResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
    }

    public record AddressOnUpdateResponse
    {
        public int Id { get; init; }
        public string CountryCode { get; init; }
        public string Province { get; init; }
        public string City { get; init; }
        public string StreetAddress { get; init; }
        public string AddressComments { get; init; }
        public string PostalCode { get; init; }
        public string Phone { get; init; }
        public string Mobile { get; init; }
        public string? CommercialContact { get; init; }
        public string? LegalAddress { get; init; }
        public string? LegalContact1 { get; init; }
        public string? LegalContact2 { get; init; }
        public string? CommercialAddress { get; init; }
        public string? CommercialContact1 { get; init; }
        public string? CommercialContact2 { get; init; }
        public string? BillingAddress { get; init; }
        public string? BillingContact1 { get; init; }
        public string? BillingContact2 { get; init; }
        public string? RedispatchAddress { get; init; }
        public string? RedispatchContact1 { get; init; }
        public string? RedispatchContact2 { get; init; }
        public string? DeliveryAddress { get; init; }
        public string? DeliveryContact1 { get; init; }
        public string? DeliveryContact2 { get; init; }
    }

    public record CustomerFileOnUpdateResponse
    {
        public int Id { get; init; }
        public string Description { get; init; }
        public string Url { get; init; }
    }

    public record CustomerBillingReasonOnUpdateResponse
    {
        public int Id { get; init; }
        public int BillingReasonId { get; init; }
        public string BillingReasonDescription { get; init; }
        public decimal Percentage { get; init; }
    }
}