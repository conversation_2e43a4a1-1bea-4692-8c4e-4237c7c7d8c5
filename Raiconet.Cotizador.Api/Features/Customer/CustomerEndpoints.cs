using Raiconet.Cotizador.Api.Features.Customer.Handlers;

namespace Raiconet.Cotizador.Api.Features.Customer;

public static class CustomerEndpoints
{
    public static void GetCustomerEndpoints(this WebApplication app)
    {
        var customersGroup = app.MapGroup("customers").WithTags("Customers"); ;
        //.RequireAuthorization();

        // Customer Management Endpoints
        customersGroup.MapGet("all", GetCustomers.Handler)
            .WithName("GetAllCustomers")
            .WithDescription("Get all customers with pagination and filtering options");

        customersGroup.MapGet("{id}", GetCustomerById.Handler)
            .WithName("GetCustomerById")
            .WithDescription("Get a specific customer by ID with all related information");
        
        customersGroup.MapGet("search", GetCustomersByName.Handler)
            .WithName("GetCustomersForSearch")
            .WithDescription("Get simplified customer list for searchbar/dropdown");

        customersGroup.MapGet("by-tax-id", GetCustomerByTaxId.Handler)
            .WithName("GetCustomerByTaxId")
            .WithDescription("Get a specific customer by CUIT with format and digit validation");

        customersGroup.MapPost("", CreateCustomer.Handler)
            .WithName("CreateCustomer")
            .WithDescription("Create a new customer with addresses and billing reasons");

        customersGroup.MapPatch("{id}", UpdateCustomer.Handler)
            .WithName("UpdateCustomer")
            .WithDescription("Update an existing customer and their related information");

        customersGroup.MapDelete("{id}", DeleteCustomer.Handler)
            .WithName("DeleteCustomer")
            .WithDescription("Delete a customer and all their related information");
    }
}