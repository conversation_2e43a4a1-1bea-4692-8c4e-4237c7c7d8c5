using Raiconet.Cotizador.Api.Features.RejectionReasons.Handlers;

namespace Raiconet.Cotizador.Api.Features.RejectionReasons;

public static class RejectionReasonEndpoints
{
    public static void MapRejectionReasonEndpoints(this WebApplication app)
    {
        var rejectionReasonGroup = app.MapGroup("rejection-reasons")
            .WithTags("RejectionReasons")
            .RequireAuthorization();

        // Rejection Reason Management Endpoints
        rejectionReasonGroup.MapGet("", GetRejectionReasons.Handler)
            .WithName("GetRejectionReasons")
            .WithDescription("Get all rejection reasons with optional filtering by search term");

        rejectionReasonGroup.MapGet("{id}", GetRejectionReasonById.Handler)
            .WithName("GetRejectionReasonById")
            .WithDescription("Get a specific rejection reason by ID");

        rejectionReasonGroup.MapPost("", CreateRejectionReason.Handler)
            .WithName("CreateRejectionReason")
            .WithDescription("Create a new rejection reason");

        rejectionReasonGroup.MapPut("{id}", UpdateRejectionReason.Handler)
            .WithName("UpdateRejectionReason")
            .WithDescription("Update an existing rejection reason");

        rejectionReasonGroup.MapDelete("{id}", DeleteRejectionReason.Handler)
            .WithName("DeleteRejectionReason")
            .WithDescription("Delete a rejection reason if it's not in use");
    }
}