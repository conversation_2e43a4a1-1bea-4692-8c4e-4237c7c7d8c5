using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.RejectionReasonService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.RejectionReasons.Handlers;

public static class GetRejectionReasonById
{
    public static async Task<Results<Ok<Domain.RejectionReason>, NotFound, UnauthorizedHttpResult>> Handler(
        [FromRoute] int id,
        [FromServices] IRejectionReasonService rejectionReasonService,
        [FromServices] ISessionManager sessionManager)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        var reason = await rejectionReasonService.GetByIdAsync(id);
        if (reason == null)
        {
            return TypedResults.NotFound();
        }

        return TypedResults.Ok(reason);
    }
}