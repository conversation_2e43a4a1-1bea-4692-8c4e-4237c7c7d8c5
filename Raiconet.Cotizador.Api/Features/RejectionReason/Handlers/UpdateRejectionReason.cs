using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.RejectionReasonService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.RejectionReasons.Handlers;

public static class UpdateRejectionReason
{
    public record UpdateRejectionReasonRequest
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public static async Task<Results<Ok<Domain.RejectionReason>, BadRequest<string>, NotFound, UnauthorizedHttpResult>> Handler(
        [FromRoute] int id,
        [FromBody] UpdateRejectionReasonRequest request,
        [FromServices] IRejectionReasonService rejectionReasonService,
        [FromServices] ISessionManager sessionManager)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        if (string.IsNullOrWhiteSpace(request.Name))
        {
            return TypedResults.BadRequest("Name is required");
        }

        var rejectionReason = new Domain.RejectionReason
        {
            Name = request.Name,
            Description = request.Description
        };

        var updatedReason = await rejectionReasonService.UpdateAsync(id, rejectionReason);
        if (updatedReason == null)
        {
            return TypedResults.NotFound();
        }

        return TypedResults.Ok(updatedReason);
    }
}