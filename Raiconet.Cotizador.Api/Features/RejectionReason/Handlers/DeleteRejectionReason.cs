using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Services.RejectionReasonService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.RejectionReasons.Handlers;

public static class DeleteRejectionReason
{
    public static async Task<Results<Ok, BadRequest<string>, NotFound, UnauthorizedHttpResult>> Handler(
        [FromRoute] int id,
        [FromServices] IRejectionReasonService rejectionReasonService,
        [FromServices] ISessionManager sessionManager)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        // Verificar si el motivo está en uso
        if (await rejectionReasonService.IsInUseAsync(id))
        {
            return TypedResults.BadRequest("Cannot delete a rejection reason that is in use");
        }

        var result = await rejectionReasonService.DeleteAsync(id);
        if (!result)
        {
            return TypedResults.NotFound();
        }

        return TypedResults.Ok();
    }
}