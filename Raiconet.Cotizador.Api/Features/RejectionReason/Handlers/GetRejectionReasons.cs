using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.RejectionReasonService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.RejectionReasons.Handlers;

public static class GetRejectionReasons
{
    public static async Task<Results<Ok<List<Domain.RejectionReason>>, UnauthorizedHttpResult>> Handler(
        [FromQuery] string? searchTerm,
        [FromServices] IRejectionReasonService rejectionReasonService,
        [FromServices] ISessionManager sessionManager)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        var reasons = await rejectionReasonService.GetAllAsync(searchTerm);
        return TypedResults.Ok(reasons);
    }
}