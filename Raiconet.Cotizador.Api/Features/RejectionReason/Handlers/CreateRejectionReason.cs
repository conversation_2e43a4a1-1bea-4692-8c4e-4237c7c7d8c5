using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.RejectionReasonService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.RejectionReasons.Handlers;

public static class CreateRejectionReason
{
    public record CreateRejectionReasonRequest
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public static async Task<Results<Ok<Domain.RejectionReason>, BadRequest<string>, UnauthorizedHttpResult>> Handler(
        [FromBody] CreateRejectionReasonRequest request,
        [FromServices] IRejectionReasonService rejectionReasonService,
        [FromServices] ISessionManager sessionManager)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        if (string.IsNullOrWhiteSpace(request.Name))
        {
            return TypedResults.BadRequest("Name is required");
        }

        var rejectionReason = new Domain.RejectionReason
        {
            Name = request.Name,
            Description = request.Description
        };

        var createdReason = await rejectionReasonService.CreateAsync(rejectionReason);
        return TypedResults.Ok(createdReason);
    }
}