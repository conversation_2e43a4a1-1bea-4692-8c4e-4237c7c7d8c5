using Raiconet.Cotizador.Api.Features.CustomerServiceRequests.Handlers;

namespace Raiconet.Cotizador.Api.Features.CustomerServiceRequests;

public static class CustomerServiceRequestEndpoints
{
    public static void MapCustomerServiceRequestEndpoints(this WebApplication app)
    {
        var customerServiceRequestGroup = app.MapGroup("customer-service-requests")
            .WithTags("CustomerServiceRequests")
            .RequireAuthorization();

        // Customer Service Request Management Endpoints
        customerServiceRequestGroup.MapGet("", GetCustomerServiceRequests.Handler)
            .WithName("GetCustomerServiceRequests")
            .WithDescription("Get all customer service requests with optional filtering by customer and status");

        customerServiceRequestGroup.MapGet("{id}", GetCustomerServiceRequest.Handler)
            .WithName("GetCustomerServiceRequestById")
            .WithDescription("Get a specific customer service request by ID with all related information");

        customerServiceRequestGroup.MapPost("", CreateCustomerServiceRequest.Handler)
            .WithName("CreateCustomerServiceRequest")
            .WithDescription("Create a new customer service request");

        customerServiceRequestGroup.MapPut("{id}", UpdateCustomerServiceRequest.Handler)
            .WithName("UpdateCustomerServiceRequest")
            .WithDescription("Update an existing customer service request");

        customerServiceRequestGroup.MapDelete("{id}", DeleteCustomerServiceRequest.Handler)
            .WithName("DeleteCustomerServiceRequest")
            .WithDescription("Delete a customer service request");

        customerServiceRequestGroup.MapPut("{id}/status", UpdateCustomerServiceRequestStatus.Handler)
            .WithName("UpdateCustomerServiceRequestStatus")
            .WithDescription("Update the status of a customer service request");
    }
}