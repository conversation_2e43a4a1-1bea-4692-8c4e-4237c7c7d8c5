using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CustomerService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.CustomerServiceRequests.Handlers;

public static class GetCustomerServiceRequests
{
    public static async Task<Results<Ok<GetCustomerServiceRequestsResponse>, UnauthorizedHttpResult>> Handler(
        [FromQuery] int? tenantId,
        [FromQuery] CustomerServiceRequestStatus? status,
        [FromServices] ICustomerServiceRequestService requestService,
        [FromServices] AppDbContext dbContext,
        [FromServices] UserManager<User> userManager,
        [FromServices] ISessionManager sessionManager)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        // Verificar si el usuario es administrador

        var roles = await userManager.GetRolesAsync(user);
        bool isAdmin = roles.Contains("admin");
        
        int? customerId = null;
        
        // Solo buscar el customerId si no es admin o si explícitamente se proporciona un tenantId
        if (!isAdmin && tenantId.HasValue)
        {
            var customer = await dbContext.Customers.FirstOrDefaultAsync(c => c.TenantId == tenantId);
            if (customer == null)
            {
                return TypedResults.Ok(new GetCustomerServiceRequestsResponse 
                { 
                    Requests = new List<CustomerServiceRequestDto>(),
                    TotalCount = 0,
                    PendingCount = 0,
                    CompletedCount = 0,
                    RejectedCount = 0
                });
            }
            
            customerId = customer.Id;
        }

        // Obtener las solicitudes usando el servicio (pasará null como customerId para el administrador)
        var requests = await requestService.GetAllAsync(isAdmin ? null : customerId, status);
        
        // Consultar los conteos por estado (ahora deben considerar todos o un cliente específico)
        var query = dbContext.CustomerServiceRequests.AsQueryable();
        if (!isAdmin && customerId.HasValue)
        {
            query = query.Where(r => r.CustomerId == customerId.Value);
        }
        
        var pendingCount = await query
            .Where(r => r.Status == CustomerServiceRequestStatus.Pending)
            .CountAsync();
            
        var completedCount = await query
            .Where(r => r.Status == CustomerServiceRequestStatus.Accepted)
            .CountAsync();
            
        var rejectedCount = await query
            .Where(r => r.Status == CustomerServiceRequestStatus.Rejected)
            .CountAsync();

        // Mapear a DTOs simplificados
        var requestDtos = requests.Select(r => new CustomerServiceRequestDto
        {
            Id = r.Id,
            CustomerId = r.CustomerId,
            CustomerName = r.Customer?.BusinessName ?? "Cliente desconocido",
            ServiceDescription = r.SetupRaicoService?.Description ?? "Servicio desconocido",
            ServiceCode = r.SetupRaicoService?.Code,
            Status = r.Status,
            CreatedAt = r.CreatedAt,
            CreatedBy = r.Creator?.UserName ?? "Usuario desconocido"
        }).ToList();

        // Crear la respuesta
        var response = new GetCustomerServiceRequestsResponse
        {
            Requests = requestDtos,
            TotalCount = requestDtos.Count,
            PendingCount = pendingCount,
            CompletedCount = completedCount,
            RejectedCount = rejectedCount
        };

        return TypedResults.Ok(response);
    }

    // DTOs para respuesta compacta
    public class CustomerServiceRequestDto
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string ServiceDescription { get; set; } = string.Empty;
        public string? ServiceCode { get; set; }
        public CustomerServiceRequestStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class GetCustomerServiceRequestsResponse
    {
        public List<CustomerServiceRequestDto> Requests { get; set; } = new();
        public int TotalCount { get; set; }
        public int PendingCount { get; set; }
        public int CompletedCount { get; set; }
        public int RejectedCount { get; set; }
    }
}