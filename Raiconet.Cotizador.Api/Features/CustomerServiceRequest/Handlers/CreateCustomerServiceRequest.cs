using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Services.CustomerService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.CustomerServiceRequests.Handlers;

public static class CreateCustomerServiceRequest
{
    public record CreateCustomerServiceRequestRequest
    {
        public int TenantId { get; set; }
        public int SetupRaicoServiceId { get; set; }
    }

    public static async Task<Results<Ok<Domain.CustomerServiceRequest>, BadRequest<string>, NotFound, UnauthorizedHttpResult>> Handler(
        [FromBody] CreateCustomerServiceRequestRequest request,
        [FromServices] ICustomerServiceRequestService requestService,
        [FromServices] ISessionManager sessionManager,
        [FromServices] AppDbContext dbContext)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        // Validar que el cliente existe
        var customer = await dbContext.Customers.FirstOrDefaultAsync(c => c.TenantId == request.TenantId);
        if (customer == null)
        {
            return TypedResults.NotFound();
        }

        // Validar que el servicio existe
        var service = await dbContext.SetupRaicoServices.FirstOrDefaultAsync(s => s.Id == request.SetupRaicoServiceId);
        if (service == null)
        {
            return TypedResults.NotFound();
        }

        var customerServiceRequest = new Domain.CustomerServiceRequest
        {
            CustomerId = customer.Id,
            SetupRaicoServiceId = request.SetupRaicoServiceId
        };

        var createdRequest = await requestService.CreateAsync(customerServiceRequest, user);
        return TypedResults.Ok(createdRequest);
    }
}