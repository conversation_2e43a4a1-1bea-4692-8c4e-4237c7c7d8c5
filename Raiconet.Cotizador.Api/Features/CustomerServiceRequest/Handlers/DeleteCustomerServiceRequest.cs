using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Services.CustomerService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.CustomerServiceRequests.Handlers;

public static class DeleteCustomerServiceRequest
{
    public static async Task<Results<Ok, NotFound, UnauthorizedHttpResult>> <PERSON><PERSON>(
        [FromRoute] int id,
        [FromServices] ICustomerServiceRequestService requestService,
        [FromServices] ISessionManager sessionManager)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        var result = await requestService.DeleteAsync(id);
        if (!result)
        {
            return TypedResults.NotFound();
        }

        return TypedResults.Ok();
    }
}