using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CustomerService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.CustomerServiceRequests.Handlers;

public static class UpdateCustomerServiceRequestStatus
{
    public record UpdateStatusRequest
    {
        public CustomerServiceRequestStatus Status { get; set; }
    }

    public static async Task<Results<Ok<CustomerServiceRequestStatusUpdateResponse>, NotFound, UnauthorizedHttpResult>> Handler(
        [FromRoute] int id,
        [FromBody] UpdateStatusRequest request,
        [FromServices] ICustomerServiceRequestService requestService,
        [FromServices] ISessionManager sessionManager,
        [FromServices] AppDbContext dbContext)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        var result = await requestService.UpdateStatusAsync(id, request.Status, user.Id);
        if (!result)
        {
            return TypedResults.NotFound();
        }

        // Cargar el objeto con la información necesaria para la respuesta
        var updatedRequest = await dbContext.CustomerServiceRequests
            .Include(c => c.Customer)
            .Include(c => c.SetupRaicoService)
            .Include(c => c.Creator)
            .FirstOrDefaultAsync(c => c.Id == id);

        if (updatedRequest == null)
        {
            return TypedResults.NotFound();
        }

        // Mapear a un DTO compacto para la respuesta
        var responseDto = new CustomerServiceRequestStatusUpdateResponse
        {
            Id = updatedRequest.Id,
            CustomerId = updatedRequest.CustomerId,
            CustomerName = updatedRequest.Customer?.BusinessName ?? "Cliente desconocido",
            
            PreviousStatus = request.Status != updatedRequest.Status ? request.Status : updatedRequest.Status,
            CurrentStatus = updatedRequest.Status,
            StatusText = GetStatusText(updatedRequest.Status),
            
            UpdatedAt = updatedRequest.UpdatedAt,
            UpdatedBy = user.UserName ?? user.Email ?? user.Id,
            
            Success = true,
            Message = $"El estado de la solicitud ha sido actualizado a {GetStatusText(updatedRequest.Status)}"
        };

        return TypedResults.Ok(responseDto);
    }

    private static string GetStatusText(CustomerServiceRequestStatus status)
    {
        return status switch
        {
            CustomerServiceRequestStatus.Pending => "Pendiente",
            CustomerServiceRequestStatus.Accepted => "Aprobada",
            CustomerServiceRequestStatus.Rejected => "Rechazado",
            _ => "Desconocido"
        };
    }

    // DTO para respuesta de actualización de estado
    public class CustomerServiceRequestStatusUpdateResponse
    {
        // Información básica
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        
        // Información de estado
        public CustomerServiceRequestStatus PreviousStatus { get; set; }
        public CustomerServiceRequestStatus CurrentStatus { get; set; }
        public string StatusText { get; set; } = string.Empty;
        
        // Información de la actualización
        public DateTime UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
        
        // Información de resultado
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}