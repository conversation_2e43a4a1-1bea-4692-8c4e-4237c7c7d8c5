using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CustomerService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.CustomerServiceRequests.Handlers;

public static class GetCustomerServiceRequest
{
    public static async Task<Results<Ok<CustomerServiceRequestDetailDto>, NotFound, UnauthorizedHttpResult>> Handler(
        [FromRoute] int id,
        [FromServices] ICustomerServiceRequestService requestService,
        [FromServices] ISessionManager sessionManager)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        var request = await requestService.GetByIdAsync(id);
        if (request == null)
        {
            return TypedResults.NotFound();
        }

        // Mapear a un DTO detallado pero compacto
        var requestDto = new CustomerServiceRequestDetailDto
        {
            Id = request.Id,
            CustomerId = request.CustomerId,
            CustomerName = request.Customer?.BusinessName ?? "Cliente desconocido",
            CustomerCode = request.Customer?.CustomerCode ?? 0,
            CustomerEmail = request.Customer?.Email,
            
            ServiceId = request.SetupRaicoServiceId,
            ServiceDescription = request.SetupRaicoService?.Description ?? "Servicio desconocido",
            ServiceCode = request.SetupRaicoService?.Code,
            
            Status = request.Status,
            StatusText = GetStatusText(request.Status),
            
            CreatedAt = request.CreatedAt,
            CreatedBy = request.Creator?.UserName ?? "Usuario desconocido",
            CreatedById = request.CreatedById,
            
            UpdatedAt = request.UpdatedAt,
            UpdatedBy = request.UpdatedBy
        };

        return TypedResults.Ok(requestDto);
    }

    private static string GetStatusText(CustomerServiceRequestStatus status)
    {
        return status switch
        {
            CustomerServiceRequestStatus.Pending => "Pendiente",
            CustomerServiceRequestStatus.Accepted => "Aprobada",
            CustomerServiceRequestStatus.Rejected => "Rechazado",
            _ => "Desconocido"
        };
    }

    // DTO para respuesta detallada pero compacta
    public class CustomerServiceRequestDetailDto
    {
        // Información básica
        public int Id { get; set; }
        
        // Información del cliente
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public int CustomerCode { get; set; }
        public string? CustomerEmail { get; set; }
        
        // Información del servicio
        public int ServiceId { get; set; }
        public string ServiceDescription { get; set; } = string.Empty;
        public string? ServiceCode { get; set; }
        
        // Información de estado
        public CustomerServiceRequestStatus Status { get; set; }
        public string StatusText { get; set; } = string.Empty;
        
        // Información de auditoría
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string CreatedById { get; set; } = string.Empty;
        public DateTime UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
    }
}