using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CustomerService;
using Raiconet.Cotizador.Api.Services.SessionManager;

namespace Raiconet.Cotizador.Api.Features.CustomerServiceRequests.Handlers;

public static class UpdateCustomerServiceRequest
{
    public record UpdateCustomerServiceRequestRequest
    {
        public int CustomerId { get; set; }
        public int SetupRaicoServiceId { get; set; }
    }

    public static async Task<Results<Ok<CustomerServiceRequestUpdateResponse>, BadRequest<string>, NotFound, UnauthorizedHttpResult>> Handler(
        [FromRoute] int id,
        [FromBody] UpdateCustomerServiceRequestRequest request,
        [FromServices] ICustomerServiceRequestService requestService,
        [FromServices] ISessionManager sessionManager,
        [FromServices] AppDbContext dbContext)
    {
        var user = await sessionManager.GetCurrentUserAsync();
        if (user == null)
        {
            return TypedResults.Unauthorized();
        }

        // Validar que el cliente existe
        var customer = await dbContext.Customers.FirstOrDefaultAsync(c => c.Id == request.CustomerId);
        if (customer == null)
        {
            return TypedResults.BadRequest("Cliente no encontrado");
        }

        // Validar que el servicio existe
        var service = await dbContext.SetupRaicoServices.FirstOrDefaultAsync(s => s.Id == request.SetupRaicoServiceId);
        if (service == null)
        {
            return TypedResults.BadRequest("Servicio no encontrado");
        }

        // Obtener la solicitud actual para comparar valores antiguos/nuevos
        var existingRequest = await dbContext.CustomerServiceRequests
            .FirstOrDefaultAsync(c => c.Id == id);
            
        if (existingRequest == null)
        {
            return TypedResults.NotFound();
        }
        
        // Guardar valores anteriores para la respuesta
        var previousCustomerId = existingRequest.CustomerId;
        var previousServiceId = existingRequest.SetupRaicoServiceId;

        var customerServiceRequest = new Domain.CustomerServiceRequest
        {
            CustomerId = request.CustomerId,
            SetupRaicoServiceId = request.SetupRaicoServiceId
        };

        var updatedRequest = await requestService.UpdateAsync(id, customerServiceRequest, user.Id);
        if (updatedRequest == null)
        {
            return TypedResults.NotFound();
        }

        // Cargar las relaciones para la respuesta
        updatedRequest = await dbContext.CustomerServiceRequests
            .Include(c => c.Customer)
            .Include(c => c.SetupRaicoService)
            .FirstOrDefaultAsync(c => c.Id == id);

        if (updatedRequest == null)
        {
            return TypedResults.NotFound();
        }

        // Buscar el cliente y servicio anteriores para la respuesta
        var previousCustomer = await dbContext.Customers
            .FirstOrDefaultAsync(c => c.Id == previousCustomerId);
            
        var previousService = await dbContext.SetupRaicoServices
            .FirstOrDefaultAsync(s => s.Id == previousServiceId);

        // Mapear a un DTO compacto para la respuesta
        var responseDto = new CustomerServiceRequestUpdateResponse
        {
            Id = updatedRequest.Id,
            
            // Información actual
            CustomerId = updatedRequest.CustomerId,
            CustomerName = updatedRequest.Customer?.BusinessName ?? "Cliente desconocido",
            ServiceId = updatedRequest.SetupRaicoServiceId,
            ServiceDescription = updatedRequest.SetupRaicoService?.Description ?? "Servicio desconocido",
            ServiceCode = updatedRequest.SetupRaicoService?.Code,
            Status = updatedRequest.Status,
            StatusText = GetStatusText(updatedRequest.Status),
            
            // Información anterior
            PreviousCustomerId = previousCustomerId,
            PreviousCustomerName = previousCustomer?.BusinessName ?? "Cliente desconocido",
            PreviousServiceId = previousServiceId,
            PreviousServiceDescription = previousService?.Description ?? "Servicio desconocido",
            
            // Información de la actualización
            UpdatedAt = updatedRequest.UpdatedAt,
            UpdatedBy = user.UserName ?? user.Email ?? user.Id,
            
            // Información de resultado
            Success = true,
            Message = "La solicitud de servicio ha sido actualizada correctamente"
        };

        return TypedResults.Ok(responseDto);
    }

    private static string GetStatusText(CustomerServiceRequestStatus status)
    {
        return status switch
        {
            CustomerServiceRequestStatus.Pending => "Pendiente",
            CustomerServiceRequestStatus.Accepted => "Aprobada",
            CustomerServiceRequestStatus.Rejected => "Rechazado",
            _ => "Desconocido"
        };
    }

    // DTO para respuesta de actualización de solicitud
    public class CustomerServiceRequestUpdateResponse
    {
        // Información actual
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public int ServiceId { get; set; }
        public string ServiceDescription { get; set; } = string.Empty;
        public string? ServiceCode { get; set; }
        public CustomerServiceRequestStatus Status { get; set; }
        public string StatusText { get; set; } = string.Empty;
        
        // Información anterior
        public int PreviousCustomerId { get; set; }
        public string PreviousCustomerName { get; set; } = string.Empty;
        public int PreviousServiceId { get; set; }
        public string PreviousServiceDescription { get; set; } = string.Empty;
        
        // Información de la actualización
        public DateTime UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
        
        // Información de resultado
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}