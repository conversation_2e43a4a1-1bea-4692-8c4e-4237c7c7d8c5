using Raiconet.Cotizador.Api.Features.Courriers.Handlers;

namespace Raiconet.Cotizador.Api.Features.Courriers;

public static class CourrierEndpoints
{
    public static void GetCourrierEndpoints(this WebApplication app)
    {
        var courriersGroup = app.MapGroup("courriers").WithTags("Courriers");
            //.RequireAuthorization();

        // UPS Endpoints
        var upsGroup = courriersGroup.MapGroup("ups");
        upsGroup.MapGet("zones", GetUpsZones.Handler)
            .WithDescription("Get UPS zones for import or export based on type parameter");

        upsGroup.MapGet("services", GetUpsServices.Handler)
            .WithDescription("Get UPS services based on type (import/export) and service type");

        // Fedex Endpoints
        var fedexGroup = courriersGroup.MapGroup("fedex");
        fedexGroup.MapGet("zones", GetFedexZones.Handler)
            .WithDescription("Get Fedex zones for import or export based on type parameter");

        fedexGroup.MapGet("services", GetFedexServices.Handler)
            .WithDescription("Get Fedex services based on type (import/export) and service type");
    }
}