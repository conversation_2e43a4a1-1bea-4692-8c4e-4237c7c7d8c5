using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Courriers.Handlers;

public static class GetUpsServices
{
    public static async Task<Results<Ok<GetUpsServicesResponse>, IResult>> Handler(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? type,
        [FromQuery] string? serviceType,
        [FromQuery] decimal? minWeight,
        [FromQuery] decimal? maxWeight,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromServices] AppDbContext dbContext
    )
    {
        try
        {
            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            var response = new GetUpsServicesResponse
            {
                PageSize = actualPageSize,
                CurrentPage = actualPage,
            };

            if (type?.ToLower() == "import")
            {
                switch (serviceType?.ToLower())
                {
                    case "saver":
                        await HandleImportSaver(dbContext, minWeight, maxWeight, sortBy, sortOrder, response,
                            actualPageSize, actualPage);
                        break;
                    case "expedited":
                        await HandleImportExpedited(dbContext, minWeight, maxWeight, sortBy, sortOrder, response,
                            actualPageSize, actualPage);
                        break;
                    case "expressfreight":
                        await HandleImportExpressFreight(dbContext, minWeight, maxWeight, sortBy, sortOrder, response,
                            actualPageSize, actualPage);
                        break;
                    default:
                        return TypedResults.BadRequest(
                            "Invalid import service type. Must be 'saver', 'expedited', or 'expressfreight'.");
                }
            }
            else if (type?.ToLower() == "export")
            {
                switch (serviceType?.ToLower())
                {
                    case "saver":
                        await HandleExportSaver(dbContext, minWeight, maxWeight, sortBy, sortOrder, response,
                            actualPageSize, actualPage);
                        break;
                    case "express":
                        await HandleExportExpress(dbContext, minWeight, maxWeight, sortBy, sortOrder, response,
                            actualPageSize, actualPage);
                        break;
                    case "expedited":
                        await HandleExportExpedited(dbContext, minWeight, maxWeight, sortBy, sortOrder, response,
                            actualPageSize, actualPage);
                        break;
                    default:
                        return TypedResults.BadRequest(
                            "Invalid export service type. Must be 'saver', 'express', or 'expedited'.");
                }
            }
            else
            {
                return TypedResults.BadRequest("Invalid type specified. Must be 'import' or 'export'.");
            }

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            Log.Error(
                ex,
                "UPS Services retrieval failed");
            throw;
        }
    }

    private static async Task HandleImportSaver(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? sortBy,
        string? sortOrder,
        GetUpsServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.UpsImportServicesSavers, minWeight, maxWeight, sortBy, sortOrder);
        var totalCount = await query.CountAsync();

        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ImportSaverServices = services.Select(t => MapToServiceResponse(t, "saver")).ToList();
    }

    private static async Task HandleImportExpedited(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? sortBy,
        string? sortOrder,
        GetUpsServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.UpsImportServicesExpediteds, minWeight, maxWeight, sortBy, sortOrder);
        var totalCount = await query.CountAsync();

        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ImportExpeditedServices = services.Select(t => MapToServiceResponse(t, "expedited")).ToList();
    }

    private static async Task HandleImportExpressFreight(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? sortBy,
        string? sortOrder,
        GetUpsServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.UpsImportServicesExpressFreights, minWeight, maxWeight, sortBy,
            sortOrder);
        var totalCount = await query.CountAsync();

        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ImportExpressFreightServices = services.Select(t => MapToServiceResponse(t, "expressfreight")).ToList();
    }

    private static async Task HandleExportSaver(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? sortBy,
        string? sortOrder,
        GetUpsServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.UpsExportServicesSavers, minWeight, maxWeight, sortBy, sortOrder);
        var totalCount = await query.CountAsync();

        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ExportSaverServices = services.Select(t => MapToServiceResponse(t, "saver")).ToList();
    }

    private static async Task HandleExportExpress(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? sortBy,
        string? sortOrder,
        GetUpsServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.UpsExportServicesExpresses, minWeight, maxWeight, sortBy, sortOrder);
        var totalCount = await query.CountAsync();

        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ExportExpressServices = services.Select(t => MapToServiceResponse(t, "express")).ToList();
    }

    private static async Task HandleExportExpedited(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? sortBy,
        string? sortOrder,
        GetUpsServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.UpsExportServicesExpediteds, minWeight, maxWeight, sortBy, sortOrder);
        var totalCount = await query.CountAsync();

        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ExportExpeditedServices = services.Select(t => MapToServiceResponse(t, "expedited")).ToList();
    }

    private static IQueryable<T> GetFilteredQuery<T>(
        IQueryable<T> query,
        decimal? minWeight,
        decimal? maxWeight,
        string? sortBy,
        string? sortOrder)
        where T : UpsServices
    {
        query = query.Include(t => t.UpsPackageType);
        if (minWeight.HasValue)
        {
            query = query.Where(s => s.Weight >= minWeight.Value);
        }

        if (maxWeight.HasValue)
        {
            query = query.Where(s => s.Weight <= maxWeight.Value);
        }

        query = sortBy?.ToLower() switch
        {
            "weight" => sortOrder?.ToLower() == "desc"
                ? query.OrderByDescending(s => s.Weight)
                : query.OrderBy(s => s.Weight),
            _ => sortOrder?.ToLower() == "desc"
                ? query.OrderByDescending(s => s.Id)
                : query.OrderBy(s => s.Id)
        };

        return query;
    }

    private static UpsServiceResponse MapToServiceResponse<T>(T service, string serviceType) where T : UpsServices
    {
        return new UpsServiceResponse
        {
            Id = service.Id,
            UpsPackageType = service.UpsPackageType,
            UpsServiceType = serviceType,
            Weight = service.Weight,
            Zone1 = service.Zone1,
            Zone2 = service.Zone2,
            Zone3 = service.Zone3,
            Zone4 = service.Zone4,
            Zone5 = service.Zone5,
            Zone6 = service.Zone6
        };
    }

    public record GetUpsServicesResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public List<UpsServiceResponse>? ImportSaverServices { get; set; }
        public List<UpsServiceResponse>? ImportExpeditedServices { get; set; }
        public List<UpsServiceResponse>? ImportExpressFreightServices { get; set; }
        public List<UpsServiceResponse>? ExportSaverServices { get; set; }
        public List<UpsServiceResponse>? ExportExpressServices { get; set; }
        public List<UpsServiceResponse>? ExportExpeditedServices { get; set; }
    }

    public record UpsServiceResponse
    {
        public int Id { get; set; }
        public UpsPackageType UpsPackageType { get; set; }
        public string? UpsServiceType { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Zone1 { get; set; }
        public decimal? Zone2 { get; set; }
        public decimal? Zone3 { get; set; }
        public decimal? Zone4 { get; set; }
        public decimal? Zone5 { get; set; }
        public decimal? Zone6 { get; set; }
    }
}