using Raiconet.Cotizador.Api.Domain;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Database;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Courriers.Handlers;

public static class GetFedexZones
{
    public static async Task<Results<Ok<GetFedexZonesResponse>, IResult>> Handler(
        [AsParameters] GetFedexZonesRequest request,
        [FromServices] AppDbContext dbContext
    )
    {
        try
        {
            IQueryable<FedexZonesImport> importQuery = dbContext.FedexZonesImports;
            
            IQueryable<FedexZonesExport> exportQuery = dbContext.FedexZonesExports;

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower();
                importQuery = importQuery.Where(z =>
                    z.Country.ToLower().Contains(searchTerm)
                );
                exportQuery = exportQuery.Where(z =>
                    z.Country.ToLower().Contains(searchTerm)
                );
            }

            importQuery = request.SortBy?.ToLower() switch
            {
                "country" => request.SortOrder?.ToLower() == "desc"
                    ? importQuery.OrderByDescending(z => z.Country)
                    : importQuery.OrderBy(z => z.Country),
                _ => request.SortOrder?.ToLower() == "desc"
                    ? importQuery.OrderByDescending(z => z.Id)
                    : importQuery.OrderBy(z => z.Id)
            };

            exportQuery = request.SortBy?.ToLower() switch
            {
                "country" => request.SortOrder?.ToLower() == "desc"
                    ? exportQuery.OrderByDescending(z => z.Country)
                    : exportQuery.OrderBy(z => z.Country),
                _ => request.SortOrder?.ToLower() == "desc"
                    ? exportQuery.OrderByDescending(z => z.Id)
                    : exportQuery.OrderBy(z => z.Id)
            };

            var pageSize = request.PageSize ?? 10;
            var pageNumber = request.Page ?? 1;

            var response = new GetFedexZonesResponse
            {
                PageSize = pageSize,
                CurrentPage = pageNumber
            };

            if (request.Type?.ToLower() == "import")
            {
                var totalCount = await importQuery.CountAsync();
                response.TotalCount = totalCount;
                response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var imports = await importQuery
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                response.ImportZones = imports.Select(z => new FedexZonesImportResponse
                {
                    Id = z.Id,
                    Country = z.Country,
                    Ip = z.Ip,
                    Ie = z.Ie,
                    Ipf = z.Ipf,
                    Ief = z.Ief
                }).ToList();
            }
            else if (request.Type?.ToLower() == "export")
            {
                var totalCount = await exportQuery.CountAsync();
                response.TotalCount = totalCount;
                response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var exports = await exportQuery
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                response.ExportZones = exports.Select(z => new FedexZonesExportResponse
                {
                    Id = z.Id,
                    Country = z.Country,
                    Ip = z.Ip,
                    Ie = z.Ie,
                    Ipf = z.Ipf,
                    Ief = z.Ief,
                    Ipe = z.Ipe
                }).ToList();
            }
            else
            {
                return TypedResults.BadRequest("Invalid zone type specified. Must be 'import' or 'export'.");
            }

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            Log.Error(
                ex,
                "Fedex Zones retrieval failed");
            throw;
        }
    }

    public record GetFedexZonesRequest
    {
        public int? Page { get; set; }
        public int? PageSize { get; set; }
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public string? SortOrder { get; set; }
        public string? Type { get; set; }  // "import" or "export"
    }

    public record GetFedexZonesResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public List<FedexZonesImportResponse>? ImportZones { get; set; }
        public List<FedexZonesExportResponse>? ExportZones { get; set; }
    }

    public record FedexZonesImportResponse
    {
        public int Id { get; set; }
        public string Country { get; set; }
        public string? Ip { get; set; }
        public string? Ie { get; set; }
        public string? Ipf { get; set; }
        public string? Ief { get; set; }
    }

    public record FedexZonesExportResponse : FedexZonesImportResponse
    {
        public string? Ipe { get; set; }
    }
}