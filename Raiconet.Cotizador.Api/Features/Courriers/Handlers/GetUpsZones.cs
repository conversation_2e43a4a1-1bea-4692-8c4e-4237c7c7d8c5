using Raiconet.Cotizador.Api.Domain;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Database;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Courriers.Handlers;

public static class GetUpsZones
{
    public static async Task<Results<Ok<GetUpsZonesResponse>, IResult>> Handler(
        [AsParameters] GetUpsZonesRequest request,
        [FromServices] AppDbContext dbContext 
    )
    {
        try
        {
            IQueryable<UpsZonesImport> importQuery = dbContext.UpsZonesImports;

            IQueryable<UpsZonesExport> exportQuery = dbContext.UpsZonesExports;

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLower();
                importQuery = importQuery.Where(z =>
                    z.Country.ToLower().Contains(searchTerm) ||
                    z.IataCode.ToLower().Contains(searchTerm)
                );
                exportQuery = exportQuery.Where(z =>
                    z.Country.ToLower().Contains(searchTerm) ||
                    z.IataCode.ToLower().Contains(searchTerm)
                );
            }

            importQuery = request.SortBy?.ToLower() switch
            {
                "country" => request.SortOrder?.ToLower() == "desc"
                    ? importQuery.OrderByDescending(z => z.Country)
                    : importQuery.OrderBy(z => z.Country),
                "iatacode" => request.SortOrder?.ToLower() == "desc"
                    ? importQuery.OrderByDescending(z => z.IataCode)
                    : importQuery.OrderBy(z => z.IataCode),
                _ => request.SortOrder?.ToLower() == "desc"
                    ? importQuery.OrderByDescending(z => z.Id)
                    : importQuery.OrderBy(z => z.Id)
            };

            exportQuery = request.SortBy?.ToLower() switch
            {
                "country" => request.SortOrder?.ToLower() == "desc"
                    ? exportQuery.OrderByDescending(z => z.Country)
                    : exportQuery.OrderBy(z => z.Country),
                "iatacode" => request.SortOrder?.ToLower() == "desc"
                    ? exportQuery.OrderByDescending(z => z.IataCode)
                    : exportQuery.OrderBy(z => z.IataCode),
                _ => request.SortOrder?.ToLower() == "desc"
                    ? exportQuery.OrderByDescending(z => z.Id)
                    : exportQuery.OrderBy(z => z.Id)
            };

            var pageSize = request.PageSize ?? 10;
            var pageNumber = request.Page ?? 1;

            var response = new GetUpsZonesResponse
            {
                PageSize = pageSize,
                CurrentPage = pageNumber
            };

            if (request.Type?.ToLower() == "import")
            {
                var totalCount = await importQuery.CountAsync();
                response.TotalCount = totalCount;
                response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var imports = await importQuery
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                response.ImportZones = imports.Select(z => new UpsZonesImportResponse
                {
                    Id = z.Id,
                    Country = z.Country,
                    IataCode = z.IataCode,
                    Express = z.Express,
                    ExpressFreight = z.ExpressFreight,
                    ExpressSaver = z.ExpressSaver,
                    Expedited = z.Expedited
                }).ToList();
            }
            else if (request.Type?.ToLower() == "export")
            {
                var totalCount = await exportQuery.CountAsync();
                response.TotalCount = totalCount;
                response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var exports = await exportQuery
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                response.ExportZones = exports.Select(z => new UpsZonesExportResponse
                {
                    Id = z.Id,
                    Country = z.Country,
                    IataCode = z.IataCode,
                    ExpressPlus = z.ExpressPlus,
                    Express = z.Express,
                    FreightMidday = z.FreightMidday,
                    ExpressFreight = z.ExpressFreight,
                    ExpressSaver = z.ExpressSaver,
                    Expedited = z.Expedited
                }).ToList();
            }
            else
            {
                return TypedResults.BadRequest("Invalid zone type specified. Must be 'import' or 'export'.");
            }

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "UPS Zones retrieval failed");
            throw;
        }
    }

    public record GetUpsZonesRequest
    {
        public int? Page { get; set; }
        public int? PageSize { get; set; }
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public string? SortOrder { get; set; }
        public string? Type { get; set; } // "import" or "export"
    }

    public record GetUpsZonesResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public List<UpsZonesImportResponse>? ImportZones { get; set; }
        public List<UpsZonesExportResponse>? ExportZones { get; set; }
    }

    public record UpsZonesImportResponse
    {
        public int Id { get; set; }
        public string Country { get; set; }
        public string IataCode { get; set; }
        public int? Express { get; set; }
        public int? ExpressFreight { get; set; }
        public int? ExpressSaver { get; set; }
        public int? Expedited { get; set; }
    }

    public record UpsZonesExportResponse
    {
        public int Id { get; set; }
        public string Country { get; set; }
        public string IataCode { get; set; }
        public int? ExpressPlus { get; set; }
        public int? Express { get; set; }
        public int? FreightMidday { get; set; }
        public int? ExpressFreight { get; set; }
        public int? ExpressSaver { get; set; }
        public int? Expedited { get; set; }
    }
}