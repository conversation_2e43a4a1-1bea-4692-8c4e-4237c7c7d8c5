using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Configuration;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Serilog;

namespace Raiconet.Cotizador.Api.Features.Courriers.Handlers;

public static class GetFedexServices
{
    public static async Task<Results<Ok<GetFedexServicesResponse>, IResult>> Handler(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? type,
        [FromQuery] string? serviceType,
        [FromQuery] decimal? minWeight,
        [FromQuery] decimal? maxWeight,
        [FromQuery] string? weightRange,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromServices] AppDbContext dbContext
    )
    {
        try
        {
            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;
            
            var response = new GetFedexServicesResponse
            {
                PageSize = actualPageSize,
                CurrentPage = actualPage,
            };

            if (type?.ToLower() == "import")
            {
                switch (serviceType?.ToLower())
                {
                    case "ie":
                        await HandleImportIe(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    case "ief":
                        await HandleImportIef(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    case "ip":
                        await HandleImportIp(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    case "ipf":
                        await HandleImportIpf(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    default:
                        return TypedResults.BadRequest("Invalid import service type. Must be 'ie', 'ief', 'ip', or 'ipf'.");
                }
            }
            else if (type?.ToLower() == "export")
            {
                switch (serviceType?.ToLower())
                {
                    case "ie":
                        await HandleExportIe(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    case "ief":
                        await HandleExportIef(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    case "ip":
                        await HandleExportIp(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    case "ipf":
                        await HandleExportIpf(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    case "ipe":
                        await HandleExportIpe(dbContext, minWeight, maxWeight, weightRange, sortBy, sortOrder, response, actualPageSize, actualPage);
                        break;
                    default:
                        return TypedResults.BadRequest("Invalid export service type. Must be 'ie', 'ief', 'ip', 'ipf', or 'ipe'.");
                }
            }
            else
            {
                return TypedResults.BadRequest("Invalid type specified. Must be 'import' or 'export'.");
            }

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
             Log.Error(
                ex,
                "Fedex Services retrieval failed");
            throw;
        }
    }

    private static async Task HandleImportIe(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexImportServiceIes, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ImportIeServices = services.Select(t => MapToServiceResponse(t, "ie")).ToList();
    }

    private static async Task HandleImportIef(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexImportServiceIefs, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ImportIefServices = services.Select(t => MapToServiceResponse(t, "ief")).ToList();
    }

    private static async Task HandleImportIp(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexImportServiceIps, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ImportIpServices = services.Select(t => MapToServiceResponse(t, "ip")).ToList();
    }

    private static async Task HandleImportIpf(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexImportServiceIpfs, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ImportIpfServices = services.Select(t => MapToServiceResponse(t, "ipf")).ToList();
    }

    private static async Task HandleExportIe(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexExportServicesIes, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ExportIeServices = services.Select(t => MapToServiceResponse(t, "ie")).ToList();
    }

    private static async Task HandleExportIef(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexExportServiceIefs, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ExportIefServices = services.Select(t => MapToServiceResponse(t, "ief")).ToList();
    }

    private static async Task HandleExportIp(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexExportServiceIps, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ExportIpServices = services.Select(t => MapToServiceResponse(t, "ip")).ToList();
    }

    private static async Task HandleExportIpf(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexExportServiceIpfs, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ExportIpfServices = services.Select(t => MapToServiceResponse(t, "ipf")).ToList();
    }

    private static async Task HandleExportIpe(
        AppDbContext dbContext,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder,
        GetFedexServicesResponse response,
        int pageSize,
        int pageNumber)
    {
        var query = GetFilteredQuery(dbContext.FedexExportServicesIpes, minWeight, maxWeight, weightRange, sortBy, sortOrder);
        var totalCount = await query.CountAsync();
        
        response.TotalCount = totalCount;
        response.TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
        
        var services = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        response.ExportIpeServices = services.Select(t => MapToServiceResponse(t, "ipe")).ToList();
    }

    private static IQueryable<T> GetFilteredQuery<T>(
        IQueryable<T> query,
        decimal? minWeight,
        decimal? maxWeight,
        string? weightRange,
        string? sortBy,
        string? sortOrder) 
        where T : FedexServices
    {
        query = query.Include(t => t.FedexPackageType);
        
        if (minWeight.HasValue)
        {
            query = query.Where(s => s.Weight >= minWeight.Value);
        }

        if (maxWeight.HasValue)
        {
            query = query.Where(s => s.Weight <= maxWeight.Value);
        }

        if (!string.IsNullOrWhiteSpace(weightRange))
        {
            query = query.Where(s => s.WeightRange == weightRange);
        }

        query = sortBy?.ToLower() switch
        {
            "weight" => sortOrder?.ToLower() == "desc"
                ? query.OrderByDescending(s => s.Weight)
                : query.OrderBy(s => s.Weight),
            "weightrange" => sortOrder?.ToLower() == "desc"
                ? query.OrderByDescending(s => s.WeightRange)
                : query.OrderBy(s => s.WeightRange),
            _ => sortOrder?.ToLower() == "desc"
                ? query.OrderByDescending(s => s.Id)
                : query.OrderBy(s => s.Id)
        };

        return query;
    }

    private static FedexServiceResponse MapToServiceResponse<T>(T service, string serviceType) where T : FedexServices
    {
        return new FedexServiceResponse
        {
            Id = service.Id,
            FedexPackageType = service.FedexPackageType,
            FedexServiceType = serviceType,
            Weight = service.Weight,
            WeightRange = service.WeightRange,
            ZoneA = service.ZoneA,
            ZoneB = service.ZoneB,
            ZoneC = service.ZoneC,
            ZoneD = service.ZoneD,
            ZoneE = service.ZoneE,
            ZoneF = service.ZoneF,
            ZoneG = service.ZoneG
        };
    }

    public record GetFedexServicesResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        // Import Services
        public List<FedexServiceResponse>? ImportIeServices { get; set; }
        public List<FedexServiceResponse>? ImportIefServices { get; set; }
        public List<FedexServiceResponse>? ImportIpServices { get; set; }
        public List<FedexServiceResponse>? ImportIpfServices { get; set; }
        // Export Services
        public List<FedexServiceResponse>? ExportIeServices { get; set; }
        public List<FedexServiceResponse>? ExportIefServices { get; set; }
        public List<FedexServiceResponse>? ExportIpServices { get; set; }
        public List<FedexServiceResponse>? ExportIpfServices { get; set; }
        public List<FedexServiceResponse>? ExportIpeServices { get; set; }
    }
    
    public record FedexServiceResponse
    {
        public int Id { get; set; }
        public FedexPackageType FedexPackageType { get; set; }
        public string? FedexServiceType { get; set; }
        public decimal? Weight { get; set; }
        public string? WeightRange { get; set; }
        public decimal? ZoneA { get; set; }
        public decimal? ZoneB { get; set; }
        public decimal? ZoneC { get; set; }
        public decimal? ZoneD { get; set; }
        public decimal? ZoneE { get; set; }
        public decimal? ZoneF { get; set; }
        public decimal? ZoneG { get; set; }
    }
}