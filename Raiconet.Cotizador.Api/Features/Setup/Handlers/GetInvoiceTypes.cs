using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetInvoicesTypes
{
    public static async Task<Results<Ok<List<InvoiceTypeResponse>>, IResult>> Handler(
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var invoicesTypes = await dbContext.InvoiceTypes
                .OrderBy(x => x.Name)
                .Select(x => new InvoiceTypeResponse
                {
                    Id = x.Id,
                    Name = x.Name
                })
                .ToListAsync();

            return TypedResults.Ok(invoicesTypes);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve invoice types");
            throw;
        }
    }

    public record InvoiceTypeResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
    }
}