using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetSetupZonesByName
{
    public static async Task<Results<Ok<List<GetSetupZones.SetupZoneResponse>>, IResult>> Handler(
        [FromQuery] string? searchTerm,
        [FromQuery] string? serviceType,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.SetupZones.AsQueryable();
            
            // Aplicar filtro por serviceType si se proporciona
            if (!string.IsNullOrWhiteSpace(serviceType))
            {
                // Filtrar exactamente por el valor proporcionado en serviceType
                logger.LogInformation($"Filtrando por serviceType={serviceType}");
                query = query.Where(x => x.Warehouse != null && x.Warehouse.Contains(serviceType));
            }
            
            // Aplicar filtro de búsqueda por término si se proporciona
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(x => 
                    x.Country.ToLower().Contains(searchTermLower) || 
                    (x.Warehouse != null && x.Warehouse.ToLower().Contains(searchTermLower)) ||
                    (x.Zone.HasValue && x.Zone.Value.ToString().Contains(searchTermLower))
                );
            }

            var zones = await query
                .OrderBy(x => x.Country)
                .Select(x => new GetSetupZones.SetupZoneResponse
                {
                    Id = x.Id,
                    Country = x.Country,
                    Zone = x.Zone,
                    Warehouse = x.Warehouse
                })
                .ToListAsync();

            // Registrar el número de resultados obtenidos
            logger.LogInformation($"Se encontraron {zones.Count} zonas con los filtros aplicados");

            return TypedResults.Ok(zones);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve setup zones for search");
            throw;
        }
    }

    // public class SetupZoneResponse
    // {
    //     public int Id { get; set; }
    //     public string Country { get; set; }
    //     public int? Zone { get; set; }
    //     public string? Warehouse { get; set; }
    // }
}