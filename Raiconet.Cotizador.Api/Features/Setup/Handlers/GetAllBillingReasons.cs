using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetAllBillingReasons
{
    public static async Task<Results<Ok<List<BillingReasonOnAllResponse>>, IResult>> <PERSON><PERSON>(
        [FromQuery] BillingType? type,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.BillingReasons
                .Include(x => x.BillingConcepts)
                .AsQueryable();

            if (type.HasValue)
            {
                query = query.Where(x => x.Type == type.Value);
            }

            var reasons = await query
                .OrderBy(x => x.Description)
                .Select(x => new BillingReasonOnAllResponse
                {
                    Id = x.Id,
                    Code = x.Code,
                    Description = x.Description,
                    Type = x.Type,
                })
                .ToListAsync();

            return TypedResults.Ok(reasons);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve billing reasons for search");
            throw;
        }
    }

    public class BillingReasonOnAllResponse
    {
        public int Id { get; set; }
        public int Code { get; set; }
        public string Description { get; set; }
        public BillingType Type { get; set; }
    }
    
}