using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class UpdateBillingConcept
{
    public static async Task<Results<Ok<CreateBillingConcept.BillingConceptResponse>, NotFound, BadRequest<string>, IResult>> <PERSON><PERSON>(
        [FromRoute] int id,
        [FromBody] UpdateBillingConceptRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var concept = await dbContext.BillingConcepts
                .Include(x => x.Reason)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (concept == null)
            {
                return TypedResults.NotFound();
            }

            if (concept.BillingReasonId != request.BillingReasonId)
            {
                var reasonExists = await dbContext.BillingReasons
                    .AnyAsync(x => x.Id == request.BillingReasonId);

                if (!reasonExists)
                {
                    return TypedResults.BadRequest("The specified billing reason does not exist");
                }
            }

            concept.Concept = request.Concept;
            concept.BillingReasonId = request.BillingReasonId;
            concept.Amount = request.Amount;
            concept.Percentage = request.Percentage;
            concept.Min = request.Min;
            concept.Max = request.Max;
            concept.Detail = request.Detail;

            await dbContext.SaveChangesAsync();

            // Reload to get updated Reason info
            await dbContext.Entry(concept).Reference(x => x.Reason).LoadAsync();

            return TypedResults.Ok(new CreateBillingConcept.BillingConceptResponse
            {
                Id = concept.Id,
                Concept = concept.Concept,
                BillingReasonId = concept.BillingReasonId,
                Amount = concept.Amount,
                Percentage = concept.Percentage,
                Min = concept.Min,
                Max = concept.Max,
                Detail = concept.Detail,
                ReasonDescription = concept.Reason?.Description
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to update billing concept");
            throw;
        }
    }

    public record UpdateBillingConceptRequest
    {
        public required string Concept { get; set; }
        public int BillingReasonId { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public decimal Min { get; set; }
        public decimal Max { get; set; }
        public decimal Detail { get; set; }
    }

    // public record BillingConceptResponse
    // {
    //     public int Id { get; set; }
    //     public string Concept { get; set; }
    //     public int BillingReasonId { get; set; }
    //     public string? ReasonDescription { get; set; }
    //     public decimal Amount { get; set; }
    //     public decimal Percentage { get; set; }
    //     public decimal Min { get; set; }
    //     public decimal Max { get; set; }
    //     public decimal Detail { get; set; }
    // }
}