
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class CreateBillingReason
{
    public static async Task<Results<Ok<BillingReasonOnCreateResponse>, BadRequest<string>, IResult>> <PERSON><PERSON>(
        [FromBody] CreateBillingReasonOnCreateRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var exists = await dbContext.BillingReasons
                .AnyAsync(x => x.Code == request.Code);

            if (exists)
            {
                return TypedResults.BadRequest("A billing reason with this code already exists");
            }

            var reason = new BillingReasons
            {
                Code = request.Code,
                Description = request.Description,
                Type = request.Type,
                BillingConcepts = request.BillingConcepts.Select(c => new BillingConcepts
                {
                    Concept = c.Concept,
                    Amount = c.Amount,
                    Percentage = c.Percentage,
                    Min = c.Min,
                    Max = c.Max,
                    Detail = c.Detail
                }).ToList()
            };

            dbContext.BillingReasons.Add(reason);
            await dbContext.SaveChangesAsync();

            return TypedResults.Ok(new BillingReasonOnCreateResponse
            {
                Id = reason.Id,
                Code = reason.Code,
                Description = reason.Description,
                Type = reason.Type,
                BillingConcepts = reason.BillingConcepts.Select(c => new BillingConceptOnCreateResponse
                {
                    Id = c.Id,
                    Concept = c.Concept,
                    Amount = c.Amount,
                    Percentage = c.Percentage,
                    Min = c.Min,
                    Max = c.Max,
                    Detail = c.Detail
                }).ToList()
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create billing reason");
            throw;
        }
    }

    public record CreateBillingReasonOnCreateRequest
    {
        public int Code { get; set; }
        public required string Description { get; set; }
        public BillingType Type { get; set; }
        public List<CreateBillingConcept.CreateBillingConceptRequest> BillingConcepts { get; set; } = new();
    }

    // public record CreateBillingConceptRequest
    // {
    //     public required string Concept { get; set; }
    //     public decimal Amount { get; set; }
    //     public decimal Percentage { get; set; }
    //     public decimal Min { get; set; }
    //     public decimal Max { get; set; }
    //     public decimal Detail { get; set; }
    // }

    public record BillingReasonOnCreateResponse
    {
        public int Id { get; set; }
        public int Code { get; set; }
        public string Description { get; set; }
        public BillingType Type { get; set; }
        public List<BillingConceptOnCreateResponse> BillingConcepts { get; set; } = new();
    }

    public record BillingConceptOnCreateResponse
    {
        public int Id { get; set; }
        public string Concept { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public decimal Min { get; set; }
        public decimal Max { get; set; }
        public decimal Detail { get; set; }
    }
}