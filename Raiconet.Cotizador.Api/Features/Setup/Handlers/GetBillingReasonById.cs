using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetBillingReason
{
    public static async Task<Results<Ok<BillingReasonOnResponse>, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var reason = await dbContext.BillingReasons
                .Include(x => x.BillingConcepts)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (reason == null)
            {
                return TypedResults.NotFound();
            }

            return TypedResults.Ok(new BillingReasonOnResponse
            {
                Id = reason.Id,
                Code = reason.Code,
                Description = reason.Description,
                Type = reason.Type,
                BillingConcepts = reason.BillingConcepts?.Select(c => new BillingConceptOnReasonResponse
                {
                    Id = c.Id,
                    Concept = c.Concept,
                    Amount = c.Amount,
                    Percentage = c.Percentage,
                    Min = c.Min,
                    Max = c.Max,
                    Detail = c.Detail
                }).ToList() ?? new List<BillingConceptOnReasonResponse>()
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve billing reason");
            throw;
        }
    }

    public record BillingReasonOnResponse
    {
        public int Id { get; set; }
        public int Code { get; set; }
        public string Description { get; set; }
        public BillingType Type { get; set; }
        public List<BillingConceptOnReasonResponse> BillingConcepts { get; set; } = new();
    }

    public record BillingConceptOnReasonResponse
    {
        public int Id { get; set; }
        public string Concept { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public decimal Min { get; set; }
        public decimal Max { get; set; }
        public decimal Detail { get; set; }
    }
}