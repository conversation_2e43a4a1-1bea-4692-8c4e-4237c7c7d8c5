using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetPaymentTerms
{
    public static async Task<Results<Ok<List<PaymentTermResponse>>, IResult>> Handler(
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var paymentTerms = await dbContext.PaymentTerms
                .OrderBy(x => x.Days)
                .Select(x => new PaymentTermResponse
                {
                    Id = x.Id,
                    Name = x.Name,
                    Days = x.Days
                })
                .ToListAsync();

            return TypedResults.Ok(paymentTerms);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve payment terms");
            throw;
        }
    }

    public record PaymentTermResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
        public int Days { get; init; }
    }
}