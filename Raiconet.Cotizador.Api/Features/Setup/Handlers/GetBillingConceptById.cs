
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetBillingConceptById
{
    public static async Task<Results<Ok<CreateBillingConcept.BillingConceptResponse>, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var concept = await dbContext.BillingConcepts
                .Include(x => x.Reason)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (concept == null)
            {
                return TypedResults.NotFound();
            }

            return TypedResults.Ok(new CreateBillingConcept.BillingConceptResponse
            {
                Id = concept.Id,
                Concept = concept.Concept,
                BillingReasonId = concept.BillingReasonId,
                Amount = concept.Amount,
                Percentage = concept.Percentage,
                Min = concept.Min,
                Max = concept.Max,
                Detail = concept.Detail,
                ReasonDescription = concept.Reason?.Description
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve billing concept");
            throw;
        }
    }

    // public record BillingConceptResponse
    // {
    //     public int Id { get; set; }
    //     public string Concept { get; set; }
    //     public int BillingReasonId { get; set; }
    //     public string? ReasonDescription { get; set; }
    //     public decimal Amount { get; set; }
    //     public decimal Percentage { get; set; }
    //     public decimal Min { get; set; }
    //     public decimal Max { get; set; }
    //     public decimal Detail { get; set; }
    // }
}