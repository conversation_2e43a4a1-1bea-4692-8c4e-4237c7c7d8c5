
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;


public static class GetBillingConcepts
{
    public static async Task<Results<Ok<GetAllBillingConceptsResponse>, IResult>> Handler(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromQuery] int? billingReasonId,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.BillingConcepts
                .Include(x => x.Reason)
                .AsQueryable();

            if (billingReasonId.HasValue)
            {
                query = query.Where(x => x.BillingReasonId == billingReasonId.Value);
            }

            query = sortBy?.ToLower() switch
            {
                "concept" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Concept)
                    : query.OrderBy(x => x.Concept),
                "amount" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Amount)
                    : query.OrderBy(x => x.Amount),
                "percentage" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Percentage)
                    : query.OrderBy(x => x.Percentage),
                _ => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Id)
                    : query.OrderBy(x => x.Id)
            };

            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            var totalCount = await query.CountAsync();
            var concepts = await query
                .Skip((actualPage - 1) * actualPageSize)
                .Take(actualPageSize)
                .ToListAsync();

            var response = new GetAllBillingConceptsResponse
            {
                TotalCount = totalCount,
                PageSize = actualPageSize,
                CurrentPage = actualPage,
                TotalPages = (int)Math.Ceiling(totalCount / (double)actualPageSize),
                BillingConcepts = concepts.Select(MapToResponse).ToList()
            };

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve billing concepts");
            throw;
        }
    }

    private static CreateBillingConcept.BillingConceptResponse MapToResponse(BillingConcepts concept) =>
        new()
        {
            Id = concept.Id,
            Concept = concept.Concept,
            BillingReasonId = concept.BillingReasonId,
            Amount = concept.Amount,
            Percentage = concept.Percentage,
            Min = concept.Min,
            Max = concept.Max,
            Detail = concept.Detail,
            ReasonDescription = concept.Reason?.Description
        };

    public record GetAllBillingConceptsResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public List<CreateBillingConcept.BillingConceptResponse> BillingConcepts { get; set; } = new();
    }

    // public record BillingConceptResponse
    // {
    //     public int Id { get; set; }
    //     public string Concept { get; set; }
    //     public int BillingReasonId { get; set; }
    //     public string? ReasonDescription { get; set; }
    //     public decimal Amount { get; set; }
    //     public decimal Percentage { get; set; }
    //     public decimal Min { get; set; }
    //     public decimal Max { get; set; }
    //     public decimal Detail { get; set; }
    // }
}