using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class UpdateRateTime
{
    public static async Task<Results<Ok<RateTimeOnUpdateResponse>, NotFound, BadRequest<string>, IResult>> <PERSON>ler(
        [FromRoute] int id,
        [FromBody] UpdateRateTimeRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var rateTime = await dbContext.RateTimes
                .FirstOrDefaultAsync(rt => rt.Id == id && rt.DeletedAt == null);

            if (rateTime == null)
            {
                return TypedResults.NotFound();
            }

            if (request.Zone != rateTime.Zone)
            {
                var exists = await dbContext.RateTimes
                    .AnyAsync(rt => rt.Zone == request.Zone && rt.Id != id && rt.DeletedAt == null);

                if (exists)
                {
                    return TypedResults.BadRequest("A rate time for this zone already exists");
                }
            }

            rateTime.Zone = request.Zone;
            rateTime.PriorityService = request.PriorityService;
            rateTime.ExpressService = request.ExpressService;

            await dbContext.SaveChangesAsync();

            return TypedResults.Ok(new RateTimeOnUpdateResponse
            {
                Id = rateTime.Id,
                Zone = rateTime.Zone,
                PriorityService = rateTime.PriorityService,
                ExpressService = rateTime.ExpressService,
                CreatedAt = rateTime.CreatedAt
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to update rate time");
            throw;
        }
    }

    public record UpdateRateTimeRequest
    {
        public int Zone { get; set; }
        public required string PriorityService { get; set; }
        public required string ExpressService { get; set; }
    }

    public record RateTimeOnUpdateResponse
    {
        public int Id { get; set; }
        public int Zone { get; set; }
        public string PriorityService { get; set; }
        public string ExpressService { get; set; }
        public DateTime? CreatedAt { get; set; }
    }
}