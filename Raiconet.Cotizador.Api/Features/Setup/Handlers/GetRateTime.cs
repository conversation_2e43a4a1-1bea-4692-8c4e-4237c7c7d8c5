using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;
public static class GetRateTime
{
    public static async Task<Results<Ok<GetAllRateTimesResponse>, IResult>> Handler(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.RateTimes
                .Where(rt => rt.DeletedAt == null);

            query = sortBy?.ToLower() switch
            {
                "zone" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(rt => rt.Zone)
                    : query.OrderBy(rt => rt.Zone),
                "priorityservice" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(rt => rt.PriorityService)
                    : query.OrderBy(rt => rt.PriorityService),
                "expressservice" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(rt => rt.ExpressService)
                    : query.OrderBy(rt => rt.ExpressService),
                _ => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(rt => rt.Id)
                    : query.OrderBy(rt => rt.Id)
            };

            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            var totalCount = await query.CountAsync();
            var rateTimes = await query
                .Skip((actualPage - 1) * actualPageSize)
                .Take(actualPageSize)
                .ToListAsync();

            var response = new GetAllRateTimesResponse
            {
                TotalCount = totalCount,
                PageSize = actualPageSize,
                CurrentPage = actualPage,
                TotalPages = (int)Math.Ceiling(totalCount / (double)actualPageSize),
                RateTimes = rateTimes.Select(MapToResponse).ToList()
            };

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve all rate times");
            throw;
        }
    }

    private static CreateRateTime.RateTimeResponse MapToResponse(RateTime rateTime) =>
        new()
        {
            Id = rateTime.Id,
            Zone = rateTime.Zone,
            PriorityService = rateTime.PriorityService,
            ExpressService = rateTime.ExpressService,
            CreatedAt = rateTime.CreatedAt
        };

    public record GetAllRateTimesResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public List<CreateRateTime.RateTimeResponse> RateTimes { get; set; } = new();
    }

    // public record RateTimeResponse
    // {
    //     public int Id { get; set; }
    //     public int Zone { get; set; }
    //     public string PriorityService { get; set; }
    //     public string ExpressService { get; set; }
    //     public DateTime? CreatedAt { get; set; }
    // }
}