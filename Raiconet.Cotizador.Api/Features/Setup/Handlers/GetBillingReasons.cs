
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetBillingReasons
{
    public static async Task<Results<Ok<GetAllBillingReasonsAllResponse>, IResult>> <PERSON><PERSON>(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromQuery] BillingType? type,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.BillingReasons
                .Include(x => x.BillingConcepts)
                .AsQueryable();

            if (type.HasValue)
            {
                query = query.Where(x => x.Type == type.Value);
            }

            query = sortBy?.ToLower() switch
            {
                "code" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Code)
                    : query.OrderBy(x => x.Code),
                "description" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Description)
                    : query.OrderBy(x => x.Description),
                _ => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Id)
                    : query.OrderBy(x => x.Id)
            };

            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            var totalCount = await query.CountAsync();
            var reasons = await query
                .Skip((actualPage - 1) * actualPageSize)
                .Take(actualPageSize)
                .ToListAsync();

            var response = new GetAllBillingReasonsAllResponse
            {
                TotalCount = totalCount,
                PageSize = actualPageSize,
                CurrentPage = actualPage,
                TotalPages = (int)Math.Ceiling(totalCount / (double)actualPageSize),
                BillingReasons = reasons.Select(MapToResponse).ToList()
            };

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve billing reasons");
            throw;
        }
    }

    private static BillingReasonAllResponse MapToResponse(BillingReasons reason) =>
        new()
        {
            Id = reason.Id,
            Code = reason.Code,
            Description = reason.Description,
            Type = reason.Type,
            BillingConcepts = reason.BillingConcepts?.Select(c => new BillingConceptOnAllResponse
            {
                Id = c.Id,
                Concept = c.Concept,
                Amount = c.Amount,
                Percentage = c.Percentage,
                Min = c.Min,
                Max = c.Max,
                Detail = c.Detail
            }).ToList() ?? new List<BillingConceptOnAllResponse>()
        };

    public record GetAllBillingReasonsAllResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public List<BillingReasonAllResponse> BillingReasons { get; set; } = new();
    }

    public record BillingReasonAllResponse
    {
        public int Id { get; set; }
        public int Code { get; set; }
        public string Description { get; set; }
        public BillingType Type { get; set; }
        public List<BillingConceptOnAllResponse> BillingConcepts { get; set; } = new();
    }

    public record BillingConceptOnAllResponse
    {
        public int Id { get; set; }
        public string Concept { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public decimal Min { get; set; }
        public decimal Max { get; set; }
        public decimal Detail { get; set; }
    }
}