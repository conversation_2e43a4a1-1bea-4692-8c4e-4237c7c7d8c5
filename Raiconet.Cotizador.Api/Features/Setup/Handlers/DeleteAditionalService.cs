
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;


public static class DeleteAdditionalService
{
    public static async Task<Results<Ok, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var service = await dbContext.AdditionalServices
                .FirstOrDefaultAsync(x => x.Id == id && x.IsActive);

            if (service == null)
            {
                return TypedResults.NotFound();
            }

            service.IsActive = false;
            await dbContext.SaveChangesAsync();

            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to delete additional service");
            throw;
        }
    }
}