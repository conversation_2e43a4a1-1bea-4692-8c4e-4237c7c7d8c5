
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class DeleteBillingConcept
{
    public static async Task<Results<Ok, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var concept = await dbContext.BillingConcepts
                .FirstOrDefaultAsync(x => x.Id == id);

            if (concept == null)
            {
                return TypedResults.NotFound();
            }

            dbContext.BillingConcepts.Remove(concept);
            await dbContext.SaveChangesAsync();

            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to delete billing concept");
            throw;
        }
    }
}