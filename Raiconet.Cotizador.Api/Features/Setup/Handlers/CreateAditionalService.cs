using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class CreateAdditionalService
{
    public static async Task<Results<Ok<AdditionalServiceResponse>, BadRequest<string>, IResult>> <PERSON><PERSON>(
        [FromBody] CreateAdditionalServiceRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var exists = await dbContext.AdditionalServices
                .AnyAsync(x => x.Code == request.Code && x.IsActive);

            if (exists)
            {
                return TypedResults.BadRequest("An additional service with this code already exists");
            }

            var service = new AdditionalService
            {
                Code = request.Code,
                Description = request.Description,
                Type = request.Type,
                MinimumValue = request.MinimumValue,
                UpsValue = request.UpsValue,
                DhlValue = request.DhlValue,
                FedexValue = request.FedexValue,
                IsActive = true
            };

            dbContext.AdditionalServices.Add(service);
            await dbContext.SaveChangesAsync();

            return TypedResults.Ok(new AdditionalServiceResponse
            {
                Id = service.Id,
                Code = service.Code,
                Description = service.Description,
                Type = service.Type,
                MinimumValue = service.MinimumValue,
                UpsValue = service.UpsValue,
                DhlValue = service.DhlValue,
                FedexValue = service.FedexValue,
                IsActive = service.IsActive
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create additional service");
            throw;
        }
    }

    public record CreateAdditionalServiceRequest
    {
        public required string Code { get; set; }
        public required string Description { get; set; }
        public required string Type { get; set; }
        public decimal? MinimumValue { get; set; }
        public decimal UpsValue { get; set; }
        public decimal DhlValue { get; set; }
        public decimal FedexValue { get; set; }
    }

    public record AdditionalServiceResponse
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public decimal? MinimumValue { get; set; }
        public decimal UpsValue { get; set; }
        public decimal DhlValue { get; set; }
        public decimal FedexValue { get; set; }
        public bool IsActive { get; set; }
    }
}