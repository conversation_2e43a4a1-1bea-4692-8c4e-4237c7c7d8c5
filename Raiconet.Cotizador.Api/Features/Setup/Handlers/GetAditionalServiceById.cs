

using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetAditionalServiceById
{
    public static async Task<Results<Ok<CreateAdditionalService.AdditionalServiceResponse>, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var service = await dbContext.AdditionalServices
                .FirstOrDefaultAsync(x => x.Id == id && x.IsActive);

            if (service == null)
            {
                return TypedResults.NotFound();
            }

            return TypedResults.Ok(new CreateAdditionalService.AdditionalServiceResponse
            {
                Id = service.Id,
                Code = service.Code,
                Description = service.Description,
                Type = service.Type,
                MinimumValue = service.MinimumValue,
                UpsValue = service.UpsValue,
                DhlValue = service.DhlValue,
                FedexValue = service.FedexValue,
                IsActive = service.IsActive
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve additional service");
            throw;
        }
    }

    // public record AdditionalServiceResponse
    // {
    //     public int Id { get; set; }
    //     public string Code { get; set; }
    //     public string Description { get; set; }
    //     public string Type { get; set; }
    //     public decimal? MinimumValue { get; set; }
    //     public decimal UpsValue { get; set; }
    //     public decimal DhlValue { get; set; }
    //     public decimal FedexValue { get; set; }
    //     public bool IsActive { get; set; }
    // }
}