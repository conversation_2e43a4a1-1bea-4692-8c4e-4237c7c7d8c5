using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetImportZonesPickupByName
{
    public static async Task<Results<Ok<List<ImportZonePickUpResponse>>, IResult>> Handler(
        [FromQuery] string? searchTerm,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.ImportZonesPickUps.AsQueryable();
            
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(x => 
                    x.Country.ToLower().Contains(searchTermLower) || 
                    x.Zone.ToLower().Contains(searchTermLower)
                );
            }

            var zones = await query
                .OrderBy(x => x.Country)
                .Select(x => new ImportZonePickUpResponse
                {
                    Id = x.Id,
                    Country = x.Country,
                    Zone = x.Zone
                })
                .ToListAsync();

            return TypedResults.Ok(zones);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve import zones pickup for search");
            throw;
        }
    }

    public class ImportZonePickUpResponse
    {
        public int Id { get; set; }
        public string Country { get; set; }
        public string Zone { get; set; }
    }
}