using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class CreateRateTime
{
    public static async Task<Results<Ok<RateTimeResponse>, BadRequest<string>, IResult>> <PERSON><PERSON>(
        [FromBody] CreateRateTimeRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var exists = await dbContext.RateTimes
                .AnyAsync(rt => rt.Zone == request.Zone && rt.DeletedAt == null);

            if (exists)
            {
                return TypedResults.BadRequest("A rate time for this zone already exists");
            }

            var rateTime = new RateTime
            {
                Zone = request.Zone,
                PriorityService = request.PriorityService,
                ExpressService = request.ExpressService,
                CreatedAt = DateTime.UtcNow
            };

            dbContext.RateTimes.Add(rateTime);
            await dbContext.SaveChangesAsync();

            return TypedResults.Ok(new RateTimeResponse
            {
                Id = rateTime.Id,
                Zone = rateTime.Zone,
                PriorityService = rateTime.PriorityService,
                ExpressService = rateTime.ExpressService,
                CreatedAt = rateTime.CreatedAt
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create rate time");
            throw;
        }
    }

    public record CreateRateTimeRequest
    {
        public int Zone { get; set; }
        public required string PriorityService { get; set; }
        public required string ExpressService { get; set; }
    }

    public record RateTimeResponse
    {
        public int Id { get; set; }
        public int Zone { get; set; }
        public string PriorityService { get; set; }
        public string ExpressService { get; set; }
        public DateTime? CreatedAt { get; set; }
    }
}