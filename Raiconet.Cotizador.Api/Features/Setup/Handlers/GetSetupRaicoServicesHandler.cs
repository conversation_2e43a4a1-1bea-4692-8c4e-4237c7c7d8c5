using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.SetupRaicoServices.Handlers;

public static class GetSetupRaicoServicesHandler
{
    public static async Task<Results<Ok<List<SetupRaicoService>>, IResult>> Handler(
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var services = await dbContext.SetupRaicoServices
                .OrderBy(x => x.Description)
                .ToListAsync();

            return TypedResults.Ok(services);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve SetupRaicoServices");
            throw;
        }
    }
}