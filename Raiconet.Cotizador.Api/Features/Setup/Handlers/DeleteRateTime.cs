using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class DeleteRateTime
{
    public static async Task<Results<Ok, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var rateTime = await dbContext.RateTimes
                .FirstOrDefaultAsync(rt => rt.Id == id && rt.DeletedAt == null);

            if (rateTime == null)
            {
                return TypedResults.NotFound();
            }

            rateTime.DeletedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            return TypedResults.Ok();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to delete rate time");
            throw;
        }
    }
}