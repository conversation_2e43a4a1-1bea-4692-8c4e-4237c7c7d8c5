using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetAllAdditionalServices
{
    public static async Task<Results<Ok<GetAllAdditionalServicesResponse>, IResult>> <PERSON><PERSON>(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.AdditionalServices
                .Where(x => x.IsActive);

            query = sortBy?.ToLower() switch
            {
                "code" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Code)
                    : query.OrderBy(x => x.Code),
                "description" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Description)
                    : query.OrderBy(x => x.Description),
                "type" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Type)
                    : query.OrderBy(x => x.Type),
                _ => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Id)
                    : query.OrderBy(x => x.Id)
            };

            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            var totalCount = await query.CountAsync();
            var services = await query
                .Skip((actualPage - 1) * actualPageSize)
                .Take(actualPageSize)
                .ToListAsync();

            var response = new GetAllAdditionalServicesResponse
            {
                TotalCount = totalCount,
                PageSize = actualPageSize,
                CurrentPage = actualPage,
                TotalPages = (int)Math.Ceiling(totalCount / (double)actualPageSize),
                AdditionalServices = services.Select(MapToResponse).ToList()
            };

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve additional services");
            throw;
        }
    }

    private static AdditionalServiceOnAllResponse MapToResponse(AdditionalService service) =>
        new()
        {
            Id = service.Id,
            Code = service.Code,
            Description = service.Description,
            Type = service.Type,
            MinimumValue = service.MinimumValue,
            UpsValue = service.UpsValue,
            DhlValue = service.DhlValue,
            FedexValue = service.FedexValue,
            IsActive = service.IsActive
        };

    public record GetAllAdditionalServicesResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public List<AdditionalServiceOnAllResponse> AdditionalServices { get; set; } = new();
    }

    public record AdditionalServiceOnAllResponse
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public decimal? MinimumValue { get; set; }
        public decimal UpsValue { get; set; }
        public decimal DhlValue { get; set; }
        public decimal FedexValue { get; set; }
        public bool IsActive { get; set; }
    }
}