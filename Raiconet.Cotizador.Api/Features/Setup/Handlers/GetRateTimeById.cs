using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetRateTimeById
{
    public static async Task<Results<Ok<RateTimeOnByIdResponse>, NotFound, IResult>> Handler(
        [FromRoute] int id,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var rateTime = await dbContext.RateTimes
                .FirstOrDefaultAsync(rt => rt.Id == id && rt.DeletedAt == null);

            if (rateTime == null)
            {
                return TypedResults.NotFound();
            }

            return TypedResults.Ok(new RateTimeOnByIdResponse
            {
                Id = rateTime.Id,
                Zone = rateTime.Zone,
                PriorityService = rateTime.PriorityService,
                ExpressService = rateTime.ExpressService,
                CreatedAt = rateTime.CreatedAt
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve rate time");
            throw;
        }
    }

    public record RateTimeOnByIdResponse
    {
        public int Id { get; set; }
        public int Zone { get; set; }
        public string PriorityService { get; set; }
        public string ExpressService { get; set; }
        public DateTime? CreatedAt { get; set; }
    }
}