using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.SetupPackageTypes.Handlers;

public static class GetSetupPackageTypesHandler
{
    public static async Task<Results<Ok<List<SetupPackageType>>, IResult>> <PERSON><PERSON>(
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var packageTypes = await dbContext.SetupPackageTypes
                .OrderBy(x => x.Description)
                .ToListAsync();

            return TypedResults.Ok(packageTypes);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve SetupPackageTypes");
            throw;
        }
    }
}