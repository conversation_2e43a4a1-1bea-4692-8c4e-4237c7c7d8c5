
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetSetupZones
{
    public static async Task<Results<Ok<GetAllSetupZonesResponse>, IResult>> Handler(
        [FromQuery] int? page,
        [FromQuery] int? pageSize,
        [FromQuery] string? sortBy,
        [FromQuery] string? sortOrder,
        [FromQuery] string? searchTerm,
        [FromQuery] int? zone,
        [FromQuery] string? warehouse,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var query = dbContext.SetupZones.AsQueryable();
            
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var searchTermLower = searchTerm.ToLower();
                query = query.Where(x => 
                    x.Country.ToLower().Contains(searchTermLower) || 
                    (x.Warehouse != null && x.Warehouse.ToLower().Contains(searchTermLower))
                );
            }

            if (zone.HasValue)
            {
                query = query.Where(x => x.Zone == zone.Value);
            }

            if (!string.IsNullOrWhiteSpace(warehouse))
            {
                var warehouseLower = warehouse.ToLower();
                query = query.Where(x => x.Warehouse != null && 
                    x.Warehouse.ToLower().Contains(warehouseLower));
            }
            
            query = sortBy?.ToLower() switch
            {
                "country" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Country)
                    : query.OrderBy(x => x.Country),
                "zone" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Zone)
                    : query.OrderBy(x => x.Zone),
                "warehouse" => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Warehouse)
                    : query.OrderBy(x => x.Warehouse),
                _ => sortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(x => x.Id)
                    : query.OrderBy(x => x.Id)
            };
            
            var actualPageSize = pageSize ?? 10;
            var actualPage = page ?? 1;

            var totalCount = await query.CountAsync();
            var zones = await query
                .Skip((actualPage - 1) * actualPageSize)
                .Take(actualPageSize)
                .ToListAsync();

            var response = new GetAllSetupZonesResponse
            {
                TotalCount = totalCount,
                PageSize = actualPageSize,
                CurrentPage = actualPage,
                TotalPages = (int)Math.Ceiling(totalCount / (double)actualPageSize),
                SetupZones = zones.Select(MapToResponse).ToList()
            };

            return TypedResults.Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve setup zones");
            throw;
        }
    }

    private static SetupZoneResponse MapToResponse(SetupZones zone) =>
        new()
        {
            Id = zone.Id,
            Country = zone.Country,
            Zone = zone.Zone,
            Warehouse = zone.Warehouse
        };

    public record GetAllSetupZonesResponse
    {
        public int TotalCount { get; set; }
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public List<SetupZoneResponse> SetupZones { get; set; } = new();
    }

    public record SetupZoneResponse
    {
        public int Id { get; set; }
        public string Country { get; set; }
        public int? Zone { get; set; }
        public string? Warehouse { get; set; }
    }
}