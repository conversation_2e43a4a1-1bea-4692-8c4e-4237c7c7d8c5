using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class UpdateAdditionalService
{
    public static async Task<Results<Ok<CreateAdditionalService.AdditionalServiceResponse>, NotFound, BadRequest<string>, IResult>> Handler(
        [FromRoute] int id,
        [FromBody] UpdateAdditionalServiceRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var service = await dbContext.AdditionalServices
                .FirstOrDefaultAsync(x => x.Id == id && x.IsActive);

            if (service == null)
            {
                return TypedResults.NotFound();
            }

            if (service.Code != request.Code)
            {
                var exists = await dbContext.AdditionalServices
                    .AnyAsync(x => x.Code == request.Code && x.Id != id && x.IsActive);

                if (exists)
                {
                    return TypedResults.BadRequest("An additional service with this code already exists");
                }
            }

            service.Code = request.Code;
            service.Description = request.Description;
            service.Type = request.Type;
            service.MinimumValue = request.MinimumValue;
            service.UpsValue = request.UpsValue;
            service.DhlValue = request.DhlValue;
            service.FedexValue = request.FedexValue;

            await dbContext.SaveChangesAsync();

            return TypedResults.Ok(new CreateAdditionalService.AdditionalServiceResponse
            {
                Id = service.Id,
                Code = service.Code,
                Description = service.Description,
                Type = service.Type,
                MinimumValue = service.MinimumValue,
                UpsValue = service.UpsValue,
                DhlValue = service.DhlValue,
                FedexValue = service.FedexValue,
                IsActive = service.IsActive
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to update additional service");
            throw;
        }
    }

    public record UpdateAdditionalServiceRequest
    {
        public required string Code { get; set; }
        public required string Description { get; set; }
        public required string Type { get; set; }
        public decimal? MinimumValue { get; set; }
        public decimal UpsValue { get; set; }
        public decimal DhlValue { get; set; }
        public decimal FedexValue { get; set; }
    }

    // public record AdditionalServiceResponse
    // {
    //     public int Id { get; set; }
    //     public string Code { get; set; }
    //     public string Description { get; set; }
    //     public string Type { get; set; }
    //     public decimal? MinimumValue { get; set; }
    //     public decimal UpsValue { get; set; }
    //     public decimal DhlValue { get; set; }
    //     public decimal FedexValue { get; set; }
    //     public bool IsActive { get; set; }
    // }
}