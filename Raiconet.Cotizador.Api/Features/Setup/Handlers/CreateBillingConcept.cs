using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class CreateBillingConcept
{
    public static async Task<Results<Ok<BillingConceptResponse>, BadRequest<string>, IResult>> <PERSON><PERSON>(
        [FromBody] CreateBillingConceptRequest request,
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var reasonExists = await dbContext.BillingReasons
                .AnyAsync(x => x.Id == request.BillingReasonId);

            if (!reasonExists)
            {
                return TypedResults.BadRequest("The specified billing reason does not exist");
            }

            var concept = new BillingConcepts
            {
                Concept = request.Concept,
                BillingReasonId = request.BillingReasonId,
                Amount = request.Amount,
                Percentage = request.Percentage,
                Min = request.Min,
                Max = request.Max,
                Detail = request.Detail
            };

            dbContext.BillingConcepts.Add(concept);
            await dbContext.SaveChangesAsync();

            // Reload to get the Reason
            concept = await dbContext.BillingConcepts
                .Include(x => x.Reason)
                .FirstAsync(x => x.Id == concept.Id);

            return TypedResults.Ok(new BillingConceptResponse
            {
                Id = concept.Id,
                Concept = concept.Concept,
                BillingReasonId = concept.BillingReasonId,
                Amount = concept.Amount,
                Percentage = concept.Percentage,
                Min = concept.Min,
                Max = concept.Max,
                Detail = concept.Detail,
                ReasonDescription = concept.Reason?.Description
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create billing concept");
            throw;
        }
    }

    public record CreateBillingConceptRequest
    {
        public required string Concept { get; set; }
        public int BillingReasonId { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public decimal Min { get; set; }
        public decimal Max { get; set; }
        public decimal Detail { get; set; }
    }

    public record BillingConceptResponse
    {
        public int Id { get; set; }
        public string Concept { get; set; }
        public int BillingReasonId { get; set; }
        public string? ReasonDescription { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public decimal Min { get; set; }
        public decimal Max { get; set; }
        public decimal Detail { get; set; }
    }
}