using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Features.Setup.Handlers;

public static class GetCustomerTypes
{
    public static async Task<Results<Ok<List<CustomerTypeResponse>>, IResult>> Handler(
        [FromServices] AppDbContext dbContext,
        [FromServices] ILogger logger
    )
    {
        try
        {
            var customerTypes = await dbContext.CustomerType
                .OrderBy(x => x.Name)
                .Select(x => new CustomerTypeResponse
                {
                    Id = x.Id,
                    Name = x.Name
                })
                .ToListAsync();

            return TypedResults.Ok(customerTypes);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve customer types");
            throw;
        }
    }

    public record CustomerTypeResponse
    {
        public int Id { get; init; }
        public string Name { get; init; }
    }
}