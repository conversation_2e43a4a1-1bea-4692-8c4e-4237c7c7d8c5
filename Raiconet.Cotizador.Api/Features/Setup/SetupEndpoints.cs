using Raiconet.Cotizador.Api.Features.Setup.Handlers;
using Raiconet.Cotizador.Api.Features.SetupPackageTypes.Handlers;
using Raiconet.Cotizador.Api.Features.SetupRaicoServices.Handlers;

namespace Raiconet.Cotizador.Api.Features.Setup;

public static class SetupEndpoints
{
    public static void RaicoSetupEndpoints(this WebApplication app)
    {
        var setupGroup = app.MapGroup("setup").WithTags("Setup"); ;
            //.RequireAuthorization();

        // Rate Times endpoints
        var rateTimesGroup = setupGroup.MapGroup("rate-times");
        
        rateTimesGroup.MapGet("all", GetRateTime.Handler)
            .WithName("GetAllRateTimes")
            .WithDescription("Get all rate times with pagination");

        rateTimesGroup.MapGet("{id}", GetRateTimeById.Handler)
            .WithName("GetRateTime")
            .WithDescription("Get a specific rate time by ID");

        rateTimesGroup.MapPost("", CreateRateTime.Handler)
            .WithName("CreateRateTime")
            .WithDescription("Create a new rate time");

        rateTimesGroup.MapPut("{id}", UpdateRateTime.Handler)
            .WithName("UpdateRateTime")
            .WithDescription("Update an existing rate time");

        rateTimesGroup.MapDelete("{id}", DeleteRateTime.Handler)
            .WithName("DeleteRateTime")
            .WithDescription("Delete a rate time");

        // Additional Services endpoints
        var additionalServicesGroup = setupGroup.MapGroup("additional-services");
        
        additionalServicesGroup.MapGet("all", GetAllAdditionalServices.Handler)
            .WithName("GetAllAdditionalServices")
            .WithDescription("Get all additional services with pagination");

        additionalServicesGroup.MapGet("{id}", GetAditionalServiceById.Handler)
            .WithName("GetAdditionalService")
            .WithDescription("Get a specific additional service by ID");

        additionalServicesGroup.MapPost("", CreateAdditionalService.Handler)
            .WithName("CreateAdditionalService")
            .WithDescription("Create a new additional service");

        additionalServicesGroup.MapPut("{id}", UpdateAdditionalService.Handler)
            .WithName("UpdateAdditionalService")
            .WithDescription("Update an existing additional service");

        additionalServicesGroup.MapDelete("{id}", DeleteAdditionalService.Handler)
            .WithName("DeleteAdditionalService")
            .WithDescription("Delete an additional service");
        
        // Billing  endpoints
        var billingReasonsGroup = setupGroup.MapGroup("billing-reasons");
        
        billingReasonsGroup.MapGet("all", GetBillingReasons.Handler)
            .WithName("GetAllBillingReasons")
            .WithDescription("Get all billing reasons with pagination");
        
        billingReasonsGroup.MapGet("search", GetAllBillingReasons.Handler)
            .WithName("GetAllBillingConcepts")
            .WithDescription("Get all billing reasons without pagination");

        billingReasonsGroup.MapGet("{id}", GetBillingReason.Handler)
            .WithName("GetBillingReason")
            .WithDescription("Get a specific billing reason by ID");

        billingReasonsGroup.MapPost("", CreateBillingReason.Handler)
            .WithName("CreateBillingReason")
            .WithDescription("Create a new billing reason");
        
        // Billing Concepts endpoints
        var billingConceptsGroup = setupGroup.MapGroup("billing-concepts");
        billingConceptsGroup.MapGet("all", GetBillingConcepts.Handler)
            .WithName("GetBillingConcepts")
            .WithDescription("Get all billing concepts with pagination");
        billingConceptsGroup.MapGet("{id}", GetBillingConceptById.Handler)
            .WithName("GetBillingConcept")
            .WithDescription("Get a specific billing concept by ID");
        billingConceptsGroup.MapPost("", CreateBillingConcept.Handler)
            .WithName("CreateBillingConcept")
            .WithDescription("Create a new billing concept");
        billingConceptsGroup.MapPut("{id}", UpdateBillingConcept.Handler)
            .WithName("UpdateBillingConcept")
            .WithDescription("Update an existing billing concept");
        billingConceptsGroup.MapDelete("{id}", DeleteBillingConcept.Handler)
            .WithName("DeleteBillingConcept")
            .WithDescription("Delete a billing concept");
        
        // Setup Zones endpoints
        var setupZonesGroup = setupGroup.MapGroup("zones");
        setupZonesGroup.MapGet("all", GetSetupZones.Handler)
            .WithName("GetAllSetupZones")
            .WithDescription("Get all setup zones with pagination and filters");
        setupGroup.MapGet("zones/search", GetSetupZonesByName.Handler)
            .WithName("GetSetupZonesForSearch")
            .WithDescription("Get setup zones for searchbar/dropdown. Optional filters: searchTerm (text search), serviceType ('TVH' for countries with TVH, 'Warehouse' for countries with W)");
        var importGroup = app.MapGroup("import").WithTags("Setup");
        importGroup.MapGet("zones-pickup/search", GetImportZonesPickupByName.Handler)
            .WithName("GetImportZonesPickUpForSearch")
            .WithDescription("Get import zones pickup for searchbar/dropdown");
        
        // Payment terms
        
        var setupPaymentTerms = setupGroup.MapGroup("payment-terms");
        setupPaymentTerms.MapGet("all", GetPaymentTerms.Handler)
            .WithName("GetPaymentTerms")
            .WithDescription("Get all PaymentTerms");
        
        var setupCustomerType = setupGroup.MapGroup("customer-type");
        setupCustomerType.MapGet("all", GetCustomerTypes.Handler)
            .WithName("GetCustomerTypes")
            .WithDescription("Get all CustomerType");
        
        var setupInvoiceTypes = setupGroup.MapGroup("invoice-types");
        setupInvoiceTypes.MapGet("all", GetInvoicesTypes.Handler)
            .WithName("GetInvoicesTypes")
            .WithDescription("Get all InvoicesTypes");
        
        //Pacakges and services
        var setupRaicoServicesGroup = setupGroup.MapGroup("raico-services");
        setupRaicoServicesGroup.MapGet("all", GetSetupRaicoServicesHandler.Handler)
            .WithName("GetSetupRaicoServices")
            .WithDescription("Get all Raico Services");

        var setupPackageTypesGroup = setupGroup.MapGroup("package-types");
        setupPackageTypesGroup.MapGet("all", GetSetupPackageTypesHandler.Handler)
            .WithName("GetSetupPackageTypes")
            .WithDescription("Get all Package Types");
    }
}