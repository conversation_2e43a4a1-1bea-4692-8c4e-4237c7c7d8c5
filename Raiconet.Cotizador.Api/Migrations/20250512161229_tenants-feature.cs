using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class tenantsfeature : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "notes",
                table: "quotation_result",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "customer_service_request",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    customer_id = table.Column<int>(type: "integer", nullable: false),
                    setup_raico_service_id = table.Column<int>(type: "integer", nullable: false),
                    status = table.Column<byte>(type: "smallint", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    created_by_id = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_service_request", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_service_request_asp_net_users_created_by_id",
                        column: x => x.created_by_id,
                        principalTable: "asp_net_user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_customer_service_request_customers_customer_id",
                        column: x => x.customer_id,
                        principalTable: "customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_customer_service_request_setup_raico_services_setup_raico_s",
                        column: x => x.setup_raico_service_id,
                        principalTable: "setup_raico_service",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "rejection_reason",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    quotation_result_id = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_rejection_reason", x => x.id);
                    table.ForeignKey(
                        name: "fk_rejection_reason_quotation_result_quotation_result_id",
                        column: x => x.quotation_result_id,
                        principalTable: "quotation_result",
                        principalColumn: "id");
                });

            migrationBuilder.CreateIndex(
                name: "ix_customer_service_request_created_by_id",
                table: "customer_service_request",
                column: "created_by_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_service_request_customer_id",
                table: "customer_service_request",
                column: "customer_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_service_request_setup_raico_service_id",
                table: "customer_service_request",
                column: "setup_raico_service_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_service_request_status",
                table: "customer_service_request",
                column: "status");

            migrationBuilder.CreateIndex(
                name: "ix_rejection_reason_quotation_result_id",
                table: "rejection_reason",
                column: "quotation_result_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "customer_service_request");

            migrationBuilder.DropTable(
                name: "rejection_reason");

            migrationBuilder.DropColumn(
                name: "notes",
                table: "quotation_result");
        }
    }
}
