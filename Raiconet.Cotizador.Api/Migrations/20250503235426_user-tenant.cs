using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class usertenant : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "tenant_id",
                table: "customers",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "tenant_id",
                table: "asp_net_user",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "tenant",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_tenant", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_customers_tenant_id",
                table: "customers",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_asp_net_user_tenant_id",
                table: "asp_net_user",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "IX_Tenant_Name",
                table: "tenant",
                column: "name");

            migrationBuilder.AddForeignKey(
                name: "fk_asp_net_user_tenant_tenant_id",
                table: "asp_net_user",
                column: "tenant_id",
                principalTable: "tenant",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_customers_tenants_tenant_id",
                table: "customers",
                column: "tenant_id",
                principalTable: "tenant",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_asp_net_user_tenant_tenant_id",
                table: "asp_net_user");

            migrationBuilder.DropForeignKey(
                name: "fk_customers_tenants_tenant_id",
                table: "customers");

            migrationBuilder.DropTable(
                name: "tenant");

            migrationBuilder.DropIndex(
                name: "ix_customers_tenant_id",
                table: "customers");

            migrationBuilder.DropIndex(
                name: "ix_asp_net_user_tenant_id",
                table: "asp_net_user");

            migrationBuilder.DropColumn(
                name: "tenant_id",
                table: "customers");

            migrationBuilder.DropColumn(
                name: "tenant_id",
                table: "asp_net_user");
        }
    }
}
