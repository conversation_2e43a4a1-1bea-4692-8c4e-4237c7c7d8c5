using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class fixservicesraico : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_billing_reasons_setup_raico_service_id",
                table: "billing_reasons");

            migrationBuilder.CreateIndex(
                name: "ix_billing_reasons_setup_raico_service_id",
                table: "billing_reasons",
                column: "setup_raico_service_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_billing_reasons_setup_raico_service_id",
                table: "billing_reasons");

            migrationBuilder.CreateIndex(
                name: "ix_billing_reasons_setup_raico_service_id",
                table: "billing_reasons",
                column: "setup_raico_service_id",
                unique: true);
        }
    }
}
