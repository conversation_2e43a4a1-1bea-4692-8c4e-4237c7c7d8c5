using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class servicesraico : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "billing_reason_id",
                table: "setup_raico_service",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "setup_raico_service_id",
                table: "billing_reasons",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_billing_reasons_setup_raico_service_id",
                table: "billing_reasons",
                column: "setup_raico_service_id",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "fk_billing_reasons_setup_raico_services_setup_raico_service_id",
                table: "billing_reasons",
                column: "setup_raico_service_id",
                principalTable: "setup_raico_service",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_billing_reasons_setup_raico_services_setup_raico_service_id",
                table: "billing_reasons");

            migrationBuilder.DropIndex(
                name: "ix_billing_reasons_setup_raico_service_id",
                table: "billing_reasons");

            migrationBuilder.DropColumn(
                name: "billing_reason_id",
                table: "setup_raico_service");

            migrationBuilder.DropColumn(
                name: "setup_raico_service_id",
                table: "billing_reasons");
        }
    }
}
