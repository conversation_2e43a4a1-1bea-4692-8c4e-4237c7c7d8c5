using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class fixaddress : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_customers_users_user_id",
                table: "customers");

            migrationBuilder.DropColumn(
                name: "billing_contact1",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "billing_contact2",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "commercial_contact",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "commercial_contact2",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "delivery_contact2",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "legal_contact1",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "legal_contact2",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "redispatch_contact1",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "redispatch_contact2",
                table: "addresses");

            migrationBuilder.AlterColumn<string>(
                name: "user_id",
                table: "customers",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<int>(
                name: "customer_code",
                table: "customers",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "contact",
                table: "addresses",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "user_id",
                table: "account_executives",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_account_executives_user_id",
                table: "account_executives",
                column: "user_id");

            migrationBuilder.AddForeignKey(
                name: "fk_account_executives_users_user_id",
                table: "account_executives",
                column: "user_id",
                principalTable: "asp_net_user",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_customers_users_user_id",
                table: "customers",
                column: "user_id",
                principalTable: "asp_net_user",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_account_executives_users_user_id",
                table: "account_executives");

            migrationBuilder.DropForeignKey(
                name: "fk_customers_users_user_id",
                table: "customers");

            migrationBuilder.DropIndex(
                name: "ix_account_executives_user_id",
                table: "account_executives");

            migrationBuilder.DropColumn(
                name: "customer_code",
                table: "customers");

            migrationBuilder.DropColumn(
                name: "contact",
                table: "addresses");

            migrationBuilder.DropColumn(
                name: "user_id",
                table: "account_executives");

            migrationBuilder.AlterColumn<string>(
                name: "user_id",
                table: "customers",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "billing_contact1",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "billing_contact2",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "commercial_contact",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "commercial_contact2",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "delivery_contact2",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "legal_contact1",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "legal_contact2",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "redispatch_contact1",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "redispatch_contact2",
                table: "addresses",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "fk_customers_users_user_id",
                table: "customers",
                column: "user_id",
                principalTable: "asp_net_user",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
