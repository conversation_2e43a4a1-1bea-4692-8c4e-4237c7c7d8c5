using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class quotationstenantupdatedby : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_quotation_result_asp_net_users_created_by_id",
                table: "quotation_result");

            migrationBuilder.CreateIndex(
                name: "ix_quotation_result_updated_by",
                table: "quotation_result",
                column: "updated_by");

            migrationBuilder.AddForeignKey(
                name: "fk_quotation_result_asp_net_users_created_by_id",
                table: "quotation_result",
                column: "created_by_id",
                principalTable: "asp_net_user",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "fk_quotation_result_asp_net_users_updated_by",
                table: "quotation_result",
                column: "updated_by",
                principalTable: "asp_net_user",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_quotation_result_asp_net_users_created_by_id",
                table: "quotation_result");

            migrationBuilder.DropForeignKey(
                name: "fk_quotation_result_asp_net_users_updated_by",
                table: "quotation_result");

            migrationBuilder.DropIndex(
                name: "ix_quotation_result_updated_by",
                table: "quotation_result");

            migrationBuilder.AddForeignKey(
                name: "fk_quotation_result_asp_net_users_created_by_id",
                table: "quotation_result",
                column: "created_by_id",
                principalTable: "asp_net_user",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
