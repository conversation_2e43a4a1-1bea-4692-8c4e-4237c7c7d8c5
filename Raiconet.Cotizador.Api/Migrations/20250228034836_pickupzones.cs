using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class pickupzones : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "import_pickup_rates",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    zone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    weight = table.Column<int>(type: "integer", nullable: false),
                    price = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_import_pickup_rates", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "import_zones_pickup",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    zone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_import_zones_pickup", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_import_pickup_rates_zone_weight",
                table: "import_pickup_rates",
                columns: new[] { "zone", "weight" });

            migrationBuilder.CreateIndex(
                name: "ix_import_zones_pickup_country",
                table: "import_zones_pickup",
                column: "country");

            migrationBuilder.CreateIndex(
                name: "ix_import_zones_pickup_country_zone",
                table: "import_zones_pickup",
                columns: new[] { "country", "zone" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "import_pickup_rates");

            migrationBuilder.DropTable(
                name: "import_zones_pickup");
        }
    }
}
