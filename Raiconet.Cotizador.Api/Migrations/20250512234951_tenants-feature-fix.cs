using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class tenantsfeaturefix : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_rejection_reason_quotation_result_quotation_result_id",
                table: "rejection_reason");

            migrationBuilder.DropIndex(
                name: "ix_rejection_reason_quotation_result_id",
                table: "rejection_reason");

            migrationBuilder.DropColumn(
                name: "quotation_result_id",
                table: "rejection_reason");

            migrationBuilder.AlterColumn<string>(
                name: "notes",
                table: "quotation_result",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<int>(
                name: "rejection_reason_id",
                table: "quotation_result",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_quotation_result_rejection_reason_id",
                table: "quotation_result",
                column: "rejection_reason_id");

            migrationBuilder.AddForeignKey(
                name: "fk_quotation_result_rejection_reasons_rejection_reason_id",
                table: "quotation_result",
                column: "rejection_reason_id",
                principalTable: "rejection_reason",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_quotation_result_rejection_reasons_rejection_reason_id",
                table: "quotation_result");

            migrationBuilder.DropIndex(
                name: "ix_quotation_result_rejection_reason_id",
                table: "quotation_result");

            migrationBuilder.DropColumn(
                name: "rejection_reason_id",
                table: "quotation_result");

            migrationBuilder.AddColumn<int>(
                name: "quotation_result_id",
                table: "rejection_reason",
                type: "integer",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "notes",
                table: "quotation_result",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_rejection_reason_quotation_result_id",
                table: "rejection_reason",
                column: "quotation_result_id");

            migrationBuilder.AddForeignKey(
                name: "fk_rejection_reason_quotation_result_quotation_result_id",
                table: "rejection_reason",
                column: "quotation_result_id",
                principalTable: "quotation_result",
                principalColumn: "id");
        }
    }
}
