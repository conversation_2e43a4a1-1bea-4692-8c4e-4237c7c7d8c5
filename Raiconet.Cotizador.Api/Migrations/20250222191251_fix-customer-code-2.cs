using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class fixcustomercode2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_customers_account_executives_account_executive_id",
                table: "customers");

            migrationBuilder.DropIndex(
                name: "ix_customers_account_executive_id",
                table: "customers");

            migrationBuilder.DropColumn(
                name: "account_executive_id",
                table: "customers");

            migrationBuilder.AlterColumn<bool>(
                name: "two_factor_enabled",
                table: "customers",
                type: "boolean",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "boolean");

            migrationBuilder.AddColumn<string>(
                name: "account_executive_user_id",
                table: "customers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "account_external_executive_id",
                table: "customers",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_customers_account_executive_user_id",
                table: "customers",
                column: "account_executive_user_id");

            migrationBuilder.CreateIndex(
                name: "ix_customers_account_external_executive_id",
                table: "customers",
                column: "account_external_executive_id");

            migrationBuilder.AddForeignKey(
                name: "fk_customers_account_executives_account_external_executive_id",
                table: "customers",
                column: "account_external_executive_id",
                principalTable: "account_executives",
                principalColumn: "id");

            migrationBuilder.AddForeignKey(
                name: "fk_customers_users_account_executive_user_id",
                table: "customers",
                column: "account_executive_user_id",
                principalTable: "asp_net_user",
                principalColumn: "id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_customers_account_executives_account_external_executive_id",
                table: "customers");

            migrationBuilder.DropForeignKey(
                name: "fk_customers_users_account_executive_user_id",
                table: "customers");

            migrationBuilder.DropIndex(
                name: "ix_customers_account_executive_user_id",
                table: "customers");

            migrationBuilder.DropIndex(
                name: "ix_customers_account_external_executive_id",
                table: "customers");

            migrationBuilder.DropColumn(
                name: "account_executive_user_id",
                table: "customers");

            migrationBuilder.DropColumn(
                name: "account_external_executive_id",
                table: "customers");

            migrationBuilder.AlterColumn<bool>(
                name: "two_factor_enabled",
                table: "customers",
                type: "boolean",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "boolean",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "account_executive_id",
                table: "customers",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "ix_customers_account_executive_id",
                table: "customers",
                column: "account_executive_id");

            migrationBuilder.AddForeignKey(
                name: "fk_customers_account_executives_account_executive_id",
                table: "customers",
                column: "account_executive_id",
                principalTable: "account_executives",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
