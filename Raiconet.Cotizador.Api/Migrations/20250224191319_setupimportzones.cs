using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class setupimportzones : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "setup_import_zones",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    name_alt = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    quotations_cd = table.Column<int>(type: "integer", nullable: true),
                    quotations_ecommerce = table.Column<int>(type: "integer", nullable: true),
                    quotations_warehouse = table.Column<int>(type: "integer", nullable: true),
                    quotations_cc = table.Column<int>(type: "integer", nullable: true),
                    quotations_tvh = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_setup_import_zones", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_setup_import_zones_code",
                table: "setup_import_zones",
                column: "code",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "setup_import_zones");
        }
    }
}
