using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class userquoatation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "created_by",
                table: "quotation_result",
                newName: "created_by_id");

            migrationBuilder.CreateIndex(
                name: "ix_quotation_result_created_by_id",
                table: "quotation_result",
                column: "created_by_id");

            migrationBuilder.AddForeignKey(
                name: "fk_quotation_result_asp_net_users_created_by_id",
                table: "quotation_result",
                column: "created_by_id",
                principalTable: "asp_net_user",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_quotation_result_asp_net_users_created_by_id",
                table: "quotation_result");

            migrationBuilder.DropIndex(
                name: "ix_quotation_result_created_by_id",
                table: "quotation_result");

            migrationBuilder.RenameColumn(
                name: "created_by_id",
                table: "quotation_result",
                newName: "created_by");
        }
    }
}
