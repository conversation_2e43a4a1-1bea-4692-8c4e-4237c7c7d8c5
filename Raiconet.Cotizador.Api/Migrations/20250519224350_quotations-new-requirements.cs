using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class quotationsnewrequirements : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "approved_by_client",
                table: "quotation_result",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "code",
                table: "quotation_result",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "description",
                table: "quotation_result",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "duty_percentage",
                table: "quotation_result",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "duty_percentage_suggested",
                table: "quotation_result",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "fob_percentage",
                table: "quotation_result",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_quotation_result_code",
                table: "quotation_result",
                column: "code");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_quotation_result_code",
                table: "quotation_result");

            migrationBuilder.DropColumn(
                name: "approved_by_client",
                table: "quotation_result");

            migrationBuilder.DropColumn(
                name: "code",
                table: "quotation_result");

            migrationBuilder.DropColumn(
                name: "description",
                table: "quotation_result");

            migrationBuilder.DropColumn(
                name: "duty_percentage",
                table: "quotation_result");

            migrationBuilder.DropColumn(
                name: "duty_percentage_suggested",
                table: "quotation_result");

            migrationBuilder.DropColumn(
                name: "fob_percentage",
                table: "quotation_result");
        }
    }
}
