using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class UpdateModelStates : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_addresses_customer_customer_id",
                table: "addresses");

            migrationBuilder.DropForeignKey(
                name: "fk_customer_billing_concepts_customer_customer_id",
                table: "customer_billing_concepts");

            migrationBuilder.DropForeignKey(
                name: "fk_customers_asp_net_users_user_id",
                table: "customers");

            migrationBuilder.DropForeignKey(
                name: "fk_customers_payment_term_payment_term_id",
                table: "customers");

            migrationBuilder.AddForeignKey(
                name: "fk_addresses_customers_customer_id",
                table: "addresses",
                column: "customer_id",
                principalTable: "customers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_customer_billing_concepts_customers_customer_id",
                table: "customer_billing_concepts",
                column: "customer_id",
                principalTable: "customers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_customers_payment_terms_payment_term_id",
                table: "customers",
                column: "payment_term_id",
                principalTable: "payment_terms",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_customers_users_user_id",
                table: "customers",
                column: "user_id",
                principalTable: "asp_net_user",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_addresses_customers_customer_id",
                table: "addresses");

            migrationBuilder.DropForeignKey(
                name: "fk_customer_billing_concepts_customers_customer_id",
                table: "customer_billing_concepts");

            migrationBuilder.DropForeignKey(
                name: "fk_customers_payment_terms_payment_term_id",
                table: "customers");

            migrationBuilder.DropForeignKey(
                name: "fk_customers_users_user_id",
                table: "customers");

            migrationBuilder.AddForeignKey(
                name: "fk_addresses_customer_customer_id",
                table: "addresses",
                column: "customer_id",
                principalTable: "customers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_customer_billing_concepts_customer_customer_id",
                table: "customer_billing_concepts",
                column: "customer_id",
                principalTable: "customers",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_customers_asp_net_users_user_id",
                table: "customers",
                column: "user_id",
                principalTable: "asp_net_user",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_customers_payment_term_payment_term_id",
                table: "customers",
                column: "payment_term_id",
                principalTable: "payment_terms",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
