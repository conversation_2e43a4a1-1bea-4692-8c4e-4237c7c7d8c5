// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Raiconet.Cotizador.Api.Database;

#nullable disable

namespace Raiconet.Cotizador.Api.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250309132226_exprotxones")]
    partial class exprotxones
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text")
                        .HasColumnName("concurrency_stamp");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("name");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_name");

                    b.HasKey("Id")
                        .HasName("pk_asp_net_role");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("asp_net_role", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text")
                        .HasColumnName("claim_value");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("role_id");

                    b.HasKey("Id")
                        .HasName("pk_asp_net_role_claim");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_asp_net_role_claim_role_id");

                    b.ToTable("asp_net_role_claim", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text")
                        .HasColumnName("claim_value");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_asp_net_user_claim");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_asp_net_user_claim_user_id");

                    b.ToTable("asp_net_user_claim", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text")
                        .HasColumnName("login_provider");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text")
                        .HasColumnName("provider_key");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text")
                        .HasColumnName("provider_display_name");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.HasKey("LoginProvider", "ProviderKey")
                        .HasName("pk_asp_net_user_login");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_asp_net_user_login_user_id");

                    b.ToTable("asp_net_user_login", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.Property<string>("RoleId")
                        .HasColumnType("text")
                        .HasColumnName("role_id");

                    b.HasKey("UserId", "RoleId")
                        .HasName("pk_asp_net_user_role");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_asp_net_user_role_role_id");

                    b.ToTable("asp_net_user_role", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text")
                        .HasColumnName("login_provider");

                    b.Property<string>("Name")
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Value")
                        .HasColumnType("text")
                        .HasColumnName("value");

                    b.HasKey("UserId", "LoginProvider", "Name")
                        .HasName("pk_asp_net_user_token");

                    b.ToTable("asp_net_user_token", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.AccountExecutive", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AccountExecutiveTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("account_executive_type_id");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_account_executives");

                    b.HasIndex("AccountExecutiveTypeId")
                        .HasDatabaseName("ix_account_executives_account_executive_type_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_account_executives_user_id");

                    b.ToTable("account_executives", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.AccountExecutiveType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_account_executive_type");

                    b.ToTable("account_executive_type", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.AdditionalService", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("description");

                    b.Property<decimal>("DhlValue")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("dhl_value");

                    b.Property<decimal>("FedexValue")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("fedex_value");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<decimal?>("MinimumValue")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("minimum_value");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("type");

                    b.Property<decimal>("UpsValue")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("ups_value");

                    b.HasKey("Id")
                        .HasName("pk_additional_services");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_additional_services_code");

                    b.ToTable("additional_services", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.Address", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AddressComments")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("address_comments");

                    b.Property<string>("BillingAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("billing_address");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("city");

                    b.Property<string>("CommercialAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("commercial_address");

                    b.Property<string>("CommercialContact1")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("commercial_contact1");

                    b.Property<string>("Contact")
                        .HasColumnType("text")
                        .HasColumnName("contact");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)")
                        .HasColumnName("country_code");

                    b.Property<int>("CustomerId")
                        .HasColumnType("integer")
                        .HasColumnName("customer_id");

                    b.Property<string>("DeliveryAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("delivery_address");

                    b.Property<string>("DeliveryContact1")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("delivery_contact1");

                    b.Property<string>("LegalAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("legal_address");

                    b.Property<string>("Mobile")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("mobile");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("phone");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("postal_code");

                    b.Property<string>("Province")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("province");

                    b.Property<string>("RedispatchAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("redispatch_address");

                    b.Property<string>("StreetAddress")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("street_address");

                    b.HasKey("Id")
                        .HasName("pk_addresses");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("ix_addresses_customer_id");

                    b.ToTable("addresses", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.BillingConcepts", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("amount");

                    b.Property<int>("BillingReasonId")
                        .HasColumnType("integer")
                        .HasColumnName("billing_reason_id");

                    b.Property<string>("Concept")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("concept");

                    b.Property<decimal>("Detail")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("detail");

                    b.Property<decimal>("Max")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("max");

                    b.Property<decimal>("Min")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("min");

                    b.Property<decimal>("Percentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)")
                        .HasColumnName("percentage");

                    b.HasKey("Id")
                        .HasName("pk_billing_concepts");

                    b.HasIndex("BillingReasonId")
                        .HasDatabaseName("ix_billing_concepts_billing_reason_id");

                    b.ToTable("billing_concepts", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.BillingReasons", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("CifFreightRate")
                        .HasColumnType("numeric")
                        .HasColumnName("cif_freight_rate");

                    b.Property<int>("Code")
                        .HasColumnType("integer")
                        .HasColumnName("code");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("description");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_billing_reasons");

                    b.ToTable("billing_reasons", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountExecutiveUserId")
                        .HasColumnType("text")
                        .HasColumnName("account_executive_user_id");

                    b.Property<int?>("AccountExternalExecutiveId")
                        .HasColumnType("integer")
                        .HasColumnName("account_external_executive_id");

                    b.Property<string>("BusinessName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("business_name");

                    b.Property<int?>("CustomerCode")
                        .HasColumnType("integer")
                        .HasColumnName("customer_code");

                    b.Property<int>("CustomerTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("customer_type_id");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("email");

                    b.Property<int>("PaymentTermId")
                        .HasColumnType("integer")
                        .HasColumnName("payment_term_id");

                    b.Property<string>("TaxId")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("tax_id");

                    b.Property<bool?>("TwoFactorEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("two_factor_enabled");

                    b.Property<string>("UserId")
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_customers");

                    b.HasIndex("AccountExecutiveUserId")
                        .HasDatabaseName("ix_customers_account_executive_user_id");

                    b.HasIndex("AccountExternalExecutiveId")
                        .HasDatabaseName("ix_customers_account_external_executive_id");

                    b.HasIndex("CustomerTypeId")
                        .HasDatabaseName("ix_customers_customer_type_id");

                    b.HasIndex("PaymentTermId")
                        .HasDatabaseName("ix_customers_payment_term_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_customers_user_id");

                    b.ToTable("customers", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.CustomerBillingReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BillingReasonId")
                        .HasColumnType("integer")
                        .HasColumnName("billing_reason_id");

                    b.Property<int>("CustomerId")
                        .HasColumnType("integer")
                        .HasColumnName("customer_id");

                    b.Property<decimal>("Percentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)")
                        .HasColumnName("percentage");

                    b.HasKey("Id")
                        .HasName("pk_customer_billing_concepts");

                    b.HasIndex("BillingReasonId")
                        .HasDatabaseName("ix_customer_billing_concepts_billing_reason_id");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("ix_customer_billing_concepts_customer_id");

                    b.ToTable("customer_billing_concepts", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.CustomerFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CustomerId")
                        .HasColumnType("integer")
                        .HasColumnName("customer_id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("description");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("url");

                    b.HasKey("Id")
                        .HasName("pk_customer_files");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("ix_customer_files_customer_id");

                    b.ToTable("customer_files", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.CustomerType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_customer_type");

                    b.ToTable("customer_type", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServiceIef", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_export_ief");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_export_ief_package_type_id");

                    b.ToTable("fedex_export_ief", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServiceIp", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_export_ip");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_export_ip_package_type_id");

                    b.ToTable("fedex_export_ip", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServiceIpf", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_export_ipf");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_export_ipf_package_type_id");

                    b.ToTable("fedex_export_ipf", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServicesIe", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_export_ie");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_export_ie_package_type_id");

                    b.ToTable("fedex_export_ie", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServicesIpe", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_export_ipe");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_export_ipe_package_type_id");

                    b.ToTable("fedex_export_ipe", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexImportServiceIe", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_import_ie");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_import_ie_package_type_id");

                    b.ToTable("fedex_import_ie", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexImportServiceIef", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_import_ief");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_import_ief_package_type_id");

                    b.ToTable("fedex_import_ief", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexImportServiceIp", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_import_ip");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_import_ip_package_type_id");

                    b.ToTable("fedex_import_ip", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexImportServiceIpf", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<string>("WeightRange")
                        .HasColumnType("text")
                        .HasColumnName("weight_range");

                    b.Property<decimal?>("ZoneA")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_a");

                    b.Property<decimal?>("ZoneB")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_b");

                    b.Property<decimal?>("ZoneC")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_c");

                    b.Property<decimal?>("ZoneD")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_d");

                    b.Property<decimal?>("ZoneE")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_e");

                    b.Property<decimal?>("ZoneF")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_f");

                    b.Property<decimal?>("ZoneG")
                        .HasColumnType("numeric")
                        .HasColumnName("zone_g");

                    b.HasKey("Id")
                        .HasName("pk_fedex_import_ipf");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_fedex_import_ipf_package_type_id");

                    b.ToTable("fedex_import_ipf", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexPackageType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("description");

                    b.HasKey("Id")
                        .HasName("pk_fedex_package_types");

                    b.ToTable("fedex_package_types", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "ENVELOPE",
                            Description = "Sobre"
                        },
                        new
                        {
                            Id = 2,
                            Code = "PAK",
                            Description = "Pak"
                        },
                        new
                        {
                            Id = 3,
                            Code = "PACKAGE",
                            Description = "Paquete"
                        },
                        new
                        {
                            Id = 4,
                            Code = "KG_RANGE",
                            Description = "Por kilogramo (Rango)"
                        });
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexZonesExport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("country");

                    b.Property<string>("Ie")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ie");

                    b.Property<string>("Ief")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ief");

                    b.Property<string>("Ip")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ip");

                    b.Property<string>("Ipe")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ipe");

                    b.Property<string>("Ipf")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ipf");

                    b.HasKey("Id")
                        .HasName("pk_fedex_zones_export");

                    b.HasIndex("Country")
                        .IsUnique()
                        .HasDatabaseName("ix_fedex_zones_export_country");

                    b.ToTable("fedex_zones_export", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexZonesImport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("country");

                    b.Property<string>("Ie")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ie");

                    b.Property<string>("Ief")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ief");

                    b.Property<string>("Ip")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ip");

                    b.Property<string>("Ipf")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ipf");

                    b.HasKey("Id")
                        .HasName("pk_fedex_zones_import");

                    b.HasIndex("Country")
                        .IsUnique()
                        .HasDatabaseName("ix_fedex_zones_import_country");

                    b.ToTable("fedex_zones_import", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.ImportPickupRate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Price")
                        .HasColumnType("integer")
                        .HasColumnName("price");

                    b.Property<int>("Weight")
                        .HasColumnType("integer")
                        .HasColumnName("weight");

                    b.Property<string>("Zone")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("zone");

                    b.HasKey("Id")
                        .HasName("pk_import_pickup_rates");

                    b.HasIndex("Zone", "Weight")
                        .HasDatabaseName("ix_import_pickup_rates_zone_weight");

                    b.ToTable("import_pickup_rates", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.ImportWarehouseChina", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("cost");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("price");

                    b.Property<decimal>("Weight")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("weight");

                    b.HasKey("Id")
                        .HasName("pk_import_warehouse_china");

                    b.HasIndex("Weight")
                        .HasDatabaseName("ix_import_warehouse_china_weight");

                    b.ToTable("import_warehouse_china", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.ImportWarehouseMadrid", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("cost");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("price");

                    b.Property<decimal>("Weight")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("weight");

                    b.HasKey("Id")
                        .HasName("pk_import_warehouse_madrid");

                    b.HasIndex("Weight")
                        .HasDatabaseName("ix_import_warehouse_madrid_weight");

                    b.ToTable("import_warehouse_madrid", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.ImportWarehouseMiami", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("cost");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("price");

                    b.Property<decimal>("Weight")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("weight");

                    b.HasKey("Id")
                        .HasName("pk_import_warehouse_miami");

                    b.HasIndex("Weight")
                        .HasDatabaseName("ix_import_warehouse_miami_weight");

                    b.ToTable("import_warehouse_miami", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.ImportZonesPickUp", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("country");

                    b.Property<string>("Zone")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("zone");

                    b.HasKey("Id")
                        .HasName("pk_import_zones_pickup");

                    b.HasIndex("Country")
                        .HasDatabaseName("ix_import_zones_pickup_country");

                    b.HasIndex("Country", "Zone")
                        .IsUnique()
                        .HasDatabaseName("ix_import_zones_pickup_country_zone");

                    b.ToTable("import_zones_pickup", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.InvoiceType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_invoice_types");

                    b.ToTable("invoice_types", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.Otp", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("character varying(6)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("email");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("expires_at");

                    b.Property<bool>("IsVerified")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_verified");

                    b.Property<int>("VerificationAttempts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("verification_attempts");

                    b.HasKey("Id")
                        .HasName("pk_otp");

                    b.HasIndex("Email")
                        .HasDatabaseName("ix_otp_email");

                    b.HasIndex("Email", "ExpiresAt")
                        .HasDatabaseName("ix_otp_email_expires_at");

                    b.ToTable("otp", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.PaymentTerm", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Days")
                        .HasColumnType("integer")
                        .HasColumnName("days");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_payment_terms");

                    b.ToTable("payment_terms", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.QuotationResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CountryOfOrigin")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("country_of_origin");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("created_by");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("customer_name");

                    b.Property<string>("Packages")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("packages");

                    b.Property<int>("QuotationNumber")
                        .HasColumnType("integer")
                        .HasColumnName("quotation_number");

                    b.Property<string>("QuotationPayload")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("quotation_payload");

                    b.Property<int>("ServiceTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("service_type_id");

                    b.Property<byte>("Status")
                        .HasColumnType("smallint")
                        .HasColumnName("status");

                    b.Property<decimal>("TotalPackageValue")
                        .HasColumnType("numeric")
                        .HasColumnName("total_package_value");

                    b.HasKey("Id")
                        .HasName("pk_quotation_result");

                    b.HasIndex("QuotationNumber")
                        .HasDatabaseName("ix_quotation_result_quotation_number");

                    b.ToTable("quotation_result", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.RateTime", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("ExpressService")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("express_service");

                    b.Property<string>("PriorityService")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("priority_service");

                    b.Property<int>("Zone")
                        .HasColumnType("integer")
                        .HasColumnName("zone");

                    b.HasKey("Id")
                        .HasName("pk_rate_time");

                    b.HasIndex("Zone")
                        .IsUnique()
                        .HasDatabaseName("ix_rate_time_zone");

                    b.ToTable("rate_time", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.SetupExportZones", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<string>("NameAlt")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name_alt");

                    b.Property<int?>("QuotationsDocEconomy")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_doc_economy");

                    b.Property<int?>("QuotationsDocPriority")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_doc_priority");

                    b.Property<int?>("QuotationsPackEconomy")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_pack_economy");

                    b.Property<int?>("QuotationsPackPriority")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_pack_priority");

                    b.HasKey("Id")
                        .HasName("pk_setup_export_zones");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_setup_export_zones_code");

                    b.ToTable("setup_export_zones", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.SetupImportZones", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("code");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.Property<string>("NameAlt")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name_alt");

                    b.Property<int?>("QuotationsCc")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_cc");

                    b.Property<int?>("QuotationsCd")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_cd");

                    b.Property<int?>("QuotationsEcommerce")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_ecommerce");

                    b.Property<int?>("QuotationsTvh")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_tvh");

                    b.Property<int?>("QuotationsWarehouse")
                        .HasColumnType("integer")
                        .HasColumnName("quotations_warehouse");

                    b.HasKey("Id")
                        .HasName("pk_setup_import_zones");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("ix_setup_import_zones_code");

                    b.ToTable("setup_import_zones", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.SetupPackageType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("description");

                    b.Property<bool>("Status")
                        .HasColumnType("boolean")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("pk_setup_package_types");

                    b.ToTable("setup_package_types", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.SetupRaicoService", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("description");

                    b.Property<bool>("Status")
                        .HasColumnType("boolean")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("pk_setup_raico_service");

                    b.ToTable("setup_raico_service", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.SetupZones", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("country");

                    b.Property<string>("Warehouse")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("warehouse");

                    b.Property<int?>("Zone")
                        .HasColumnType("integer")
                        .HasColumnName("zone");

                    b.HasKey("Id")
                        .HasName("pk_setup_zones");

                    b.HasIndex("Country")
                        .HasDatabaseName("IX_SetupZones_Country");

                    b.HasIndex("Zone")
                        .HasDatabaseName("IX_SetupZones_Zone");

                    b.ToTable("setup_zones", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.TvhImportCollect", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("cost");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("price");

                    b.Property<decimal>("Weight")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("weight");

                    b.HasKey("Id")
                        .HasName("pk_tvh_import_collect");

                    b.HasIndex("Weight")
                        .HasDatabaseName("ix_tvh_import_collect_weight");

                    b.ToTable("tvh_import_collect", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.TvhImportPrepaid", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("cost");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("price");

                    b.Property<decimal>("Weight")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("weight");

                    b.HasKey("Id")
                        .HasName("pk_tvh_import_prepaid");

                    b.HasIndex("Weight")
                        .HasDatabaseName("ix_tvh_import_prepaid_weight");

                    b.ToTable("tvh_import_prepaid", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsExportServicesExpedited", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<decimal?>("Zone1")
                        .HasColumnType("numeric")
                        .HasColumnName("zone1");

                    b.Property<decimal?>("Zone2")
                        .HasColumnType("numeric")
                        .HasColumnName("zone2");

                    b.Property<decimal?>("Zone3")
                        .HasColumnType("numeric")
                        .HasColumnName("zone3");

                    b.Property<decimal?>("Zone4")
                        .HasColumnType("numeric")
                        .HasColumnName("zone4");

                    b.Property<decimal?>("Zone5")
                        .HasColumnType("numeric")
                        .HasColumnName("zone5");

                    b.Property<decimal?>("Zone6")
                        .HasColumnType("numeric")
                        .HasColumnName("zone6");

                    b.HasKey("Id")
                        .HasName("pk_ups_export_expedited");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_ups_export_expedited_package_type_id");

                    b.ToTable("ups_export_expedited", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsExportServicesExpress", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<decimal?>("Zone1")
                        .HasColumnType("numeric")
                        .HasColumnName("zone1");

                    b.Property<decimal?>("Zone2")
                        .HasColumnType("numeric")
                        .HasColumnName("zone2");

                    b.Property<decimal?>("Zone3")
                        .HasColumnType("numeric")
                        .HasColumnName("zone3");

                    b.Property<decimal?>("Zone4")
                        .HasColumnType("numeric")
                        .HasColumnName("zone4");

                    b.Property<decimal?>("Zone5")
                        .HasColumnType("numeric")
                        .HasColumnName("zone5");

                    b.Property<decimal?>("Zone6")
                        .HasColumnType("numeric")
                        .HasColumnName("zone6");

                    b.HasKey("Id")
                        .HasName("pk_ups_export_express");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_ups_export_express_package_type_id");

                    b.ToTable("ups_export_express", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsExportServicesSaver", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<decimal?>("Zone1")
                        .HasColumnType("numeric")
                        .HasColumnName("zone1");

                    b.Property<decimal?>("Zone2")
                        .HasColumnType("numeric")
                        .HasColumnName("zone2");

                    b.Property<decimal?>("Zone3")
                        .HasColumnType("numeric")
                        .HasColumnName("zone3");

                    b.Property<decimal?>("Zone4")
                        .HasColumnType("numeric")
                        .HasColumnName("zone4");

                    b.Property<decimal?>("Zone5")
                        .HasColumnType("numeric")
                        .HasColumnName("zone5");

                    b.Property<decimal?>("Zone6")
                        .HasColumnType("numeric")
                        .HasColumnName("zone6");

                    b.HasKey("Id")
                        .HasName("pk_ups_export_saver");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_ups_export_saver_package_type_id");

                    b.ToTable("ups_export_saver", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsImportServicesExpedited", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<decimal?>("Zone1")
                        .HasColumnType("numeric")
                        .HasColumnName("zone1");

                    b.Property<decimal?>("Zone2")
                        .HasColumnType("numeric")
                        .HasColumnName("zone2");

                    b.Property<decimal?>("Zone3")
                        .HasColumnType("numeric")
                        .HasColumnName("zone3");

                    b.Property<decimal?>("Zone4")
                        .HasColumnType("numeric")
                        .HasColumnName("zone4");

                    b.Property<decimal?>("Zone5")
                        .HasColumnType("numeric")
                        .HasColumnName("zone5");

                    b.Property<decimal?>("Zone6")
                        .HasColumnType("numeric")
                        .HasColumnName("zone6");

                    b.HasKey("Id")
                        .HasName("pk_ups_import_expedited");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_ups_import_expedited_package_type_id");

                    b.ToTable("ups_import_expedited", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsImportServicesExpressFreight", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<decimal?>("Zone1")
                        .HasColumnType("numeric")
                        .HasColumnName("zone1");

                    b.Property<decimal?>("Zone2")
                        .HasColumnType("numeric")
                        .HasColumnName("zone2");

                    b.Property<decimal?>("Zone3")
                        .HasColumnType("numeric")
                        .HasColumnName("zone3");

                    b.Property<decimal?>("Zone4")
                        .HasColumnType("numeric")
                        .HasColumnName("zone4");

                    b.Property<decimal?>("Zone5")
                        .HasColumnType("numeric")
                        .HasColumnName("zone5");

                    b.Property<decimal?>("Zone6")
                        .HasColumnType("numeric")
                        .HasColumnName("zone6");

                    b.HasKey("Id")
                        .HasName("pk_ups_import_express_freight");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_ups_import_express_freight_package_type_id");

                    b.ToTable("ups_import_express_freight", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsImportServicesSaver", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("PackageTypeId")
                        .HasColumnType("integer")
                        .HasColumnName("package_type_id");

                    b.Property<decimal?>("Weight")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)")
                        .HasColumnName("weight");

                    b.Property<decimal?>("Zone1")
                        .HasColumnType("numeric")
                        .HasColumnName("zone1");

                    b.Property<decimal?>("Zone2")
                        .HasColumnType("numeric")
                        .HasColumnName("zone2");

                    b.Property<decimal?>("Zone3")
                        .HasColumnType("numeric")
                        .HasColumnName("zone3");

                    b.Property<decimal?>("Zone4")
                        .HasColumnType("numeric")
                        .HasColumnName("zone4");

                    b.Property<decimal?>("Zone5")
                        .HasColumnType("numeric")
                        .HasColumnName("zone5");

                    b.Property<decimal?>("Zone6")
                        .HasColumnType("numeric")
                        .HasColumnName("zone6");

                    b.HasKey("Id")
                        .HasName("pk_ups_import_saver");

                    b.HasIndex("PackageTypeId")
                        .HasDatabaseName("ix_ups_import_saver_package_type_id");

                    b.ToTable("ups_import_saver", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsPackageType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("code");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("description");

                    b.HasKey("Id")
                        .HasName("pk_ups_package_types");

                    b.ToTable("ups_package_types", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Code = "DOC_SOBRE",
                            Description = "DOCUMENTO (OTRO SOBRE)"
                        },
                        new
                        {
                            Id = 2,
                            Code = "PAQUETE",
                            Description = "PAQUETE (NO DOCUMENTO)"
                        },
                        new
                        {
                            Id = 3,
                            Code = "POR_KILO",
                            Description = "PRECIO POR KILO"
                        },
                        new
                        {
                            Id = 4,
                            Code = "SOBRE_CARTON",
                            Description = "SOBRE (CARTÓN)"
                        },
                        new
                        {
                            Id = 5,
                            Code = "TARIFA_MIN",
                            Description = "TARIFA MINIMA"
                        });
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsZonesExport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("country");

                    b.Property<int?>("Expedited")
                        .HasColumnType("integer")
                        .HasColumnName("expedited");

                    b.Property<int?>("Express")
                        .HasColumnType("integer")
                        .HasColumnName("express");

                    b.Property<int?>("ExpressFreight")
                        .HasColumnType("integer")
                        .HasColumnName("express_freight");

                    b.Property<int?>("ExpressPlus")
                        .HasColumnType("integer")
                        .HasColumnName("express_plus");

                    b.Property<int?>("ExpressSaver")
                        .HasColumnType("integer")
                        .HasColumnName("express_saver");

                    b.Property<int?>("FreightMidday")
                        .HasColumnType("integer")
                        .HasColumnName("freight_midday");

                    b.Property<string>("IataCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("iata_code");

                    b.HasKey("Id")
                        .HasName("pk_ups_zones_export");

                    b.HasIndex("Country", "IataCode")
                        .IsUnique()
                        .HasDatabaseName("ix_ups_zones_export_country_iata_code");

                    b.ToTable("ups_zones_export", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsZonesImport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("country");

                    b.Property<int?>("Expedited")
                        .HasColumnType("integer")
                        .HasColumnName("expedited");

                    b.Property<int?>("Express")
                        .HasColumnType("integer")
                        .HasColumnName("express");

                    b.Property<int?>("ExpressFreight")
                        .HasColumnType("integer")
                        .HasColumnName("express_freight");

                    b.Property<int?>("ExpressSaver")
                        .HasColumnType("integer")
                        .HasColumnName("express_saver");

                    b.Property<string>("IataCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("iata_code");

                    b.HasKey("Id")
                        .HasName("pk_ups_zones_import");

                    b.HasIndex("Country", "IataCode")
                        .IsUnique()
                        .HasDatabaseName("ix_ups_zones_import_country_iata_code");

                    b.ToTable("ups_zones_import", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer")
                        .HasColumnName("access_failed_count");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text")
                        .HasColumnName("concurrency_stamp");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("email");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean")
                        .HasColumnName("email_confirmed");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("lockout_enabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("lockout_end");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_email");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("normalized_user_name");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text")
                        .HasColumnName("password_hash");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text")
                        .HasColumnName("phone_number");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean")
                        .HasColumnName("phone_number_confirmed");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text")
                        .HasColumnName("security_stamp");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("two_factor_enabled");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("user_name");

                    b.Property<int>("UserProfileId")
                        .HasColumnType("integer")
                        .HasColumnName("user_profile_id");

                    b.HasKey("Id")
                        .HasName("pk_asp_net_user");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("asp_net_user", (string)null);
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UserProfile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("first_name");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("last_name");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_user_profile");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("ix_user_profile_user_id");

                    b.ToTable("user_profile", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asp_net_role_claim_asp_net_roles_role_id");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asp_net_user_claim_asp_net_users_user_id");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asp_net_user_login_asp_net_user_user_id");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asp_net_user_role_asp_net_role_role_id");

                    b.HasOne("Raiconet.Cotizador.Api.Domain.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asp_net_user_role_asp_net_user_user_id");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_asp_net_user_token_asp_net_user_user_id");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.AccountExecutive", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.AccountExecutiveType", "Type")
                        .WithMany("AccountExecutives")
                        .HasForeignKey("AccountExecutiveTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_account_executives_account_executive_type_account_executive");

                    b.HasOne("Raiconet.Cotizador.Api.Domain.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("fk_account_executives_users_user_id");

                    b.Navigation("Type");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.Address", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.Customer", "Customer")
                        .WithMany("Addresses")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_addresses_customers_customer_id");

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.BillingConcepts", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.BillingReasons", "Reason")
                        .WithMany("BillingConcepts")
                        .HasForeignKey("BillingReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_billing_concepts_billing_reasons_billing_reason_id");

                    b.Navigation("Reason");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.Customer", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.User", "AccountExecutiveUser")
                        .WithMany()
                        .HasForeignKey("AccountExecutiveUserId")
                        .HasConstraintName("fk_customers_users_account_executive_user_id");

                    b.HasOne("Raiconet.Cotizador.Api.Domain.AccountExecutive", "AccountExternalExecutive")
                        .WithMany("Customers")
                        .HasForeignKey("AccountExternalExecutiveId")
                        .HasConstraintName("fk_customers_account_executives_account_external_executive_id");

                    b.HasOne("Raiconet.Cotizador.Api.Domain.CustomerType", "Type")
                        .WithMany("Customers")
                        .HasForeignKey("CustomerTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customers_customer_type_customer_type_id");

                    b.HasOne("Raiconet.Cotizador.Api.Domain.PaymentTerm", "PaymentTerm")
                        .WithMany("Customers")
                        .HasForeignKey("PaymentTermId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customers_payment_terms_payment_term_id");

                    b.HasOne("Raiconet.Cotizador.Api.Domain.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .HasConstraintName("fk_customers_users_user_id");

                    b.Navigation("AccountExecutiveUser");

                    b.Navigation("AccountExternalExecutive");

                    b.Navigation("PaymentTerm");

                    b.Navigation("Type");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.CustomerBillingReason", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.BillingReasons", "BillingReason")
                        .WithMany("CustomerBillingReason")
                        .HasForeignKey("BillingReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_billing_concepts_billing_reasons_billing_reason_id");

                    b.HasOne("Raiconet.Cotizador.Api.Domain.Customer", "Customer")
                        .WithMany("CustomerBillingReason")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_billing_concepts_customers_customer_id");

                    b.Navigation("BillingReason");

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.CustomerFile", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.Customer", "Customer")
                        .WithMany("Files")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_customer_files_customers_customer_id");

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServiceIef", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_export_ief_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServiceIp", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_export_ip_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServiceIpf", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_export_ipf_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServicesIe", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_export_ie_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexExportServicesIpe", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_export_ipe_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexImportServiceIe", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_import_ie_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexImportServiceIef", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_import_ief_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexImportServiceIp", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_import_ip_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.FedexImportServiceIpf", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.FedexPackageType", "FedexPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_fedex_import_ipf_fedex_package_type_package_type_id");

                    b.Navigation("FedexPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsExportServicesExpedited", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.UpsPackageType", "UpsPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ups_export_expedited_ups_package_types_package_type_id");

                    b.Navigation("UpsPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsExportServicesExpress", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.UpsPackageType", "UpsPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ups_export_express_ups_package_types_package_type_id");

                    b.Navigation("UpsPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsExportServicesSaver", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.UpsPackageType", "UpsPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ups_export_saver_ups_package_types_package_type_id");

                    b.Navigation("UpsPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsImportServicesExpedited", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.UpsPackageType", "UpsPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ups_import_expedited_ups_package_types_package_type_id");

                    b.Navigation("UpsPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsImportServicesExpressFreight", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.UpsPackageType", "UpsPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ups_import_express_freight_ups_package_types_package_type_id");

                    b.Navigation("UpsPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UpsImportServicesSaver", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.UpsPackageType", "UpsPackageType")
                        .WithMany()
                        .HasForeignKey("PackageTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_ups_import_saver_ups_package_types_package_type_id");

                    b.Navigation("UpsPackageType");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.UserProfile", b =>
                {
                    b.HasOne("Raiconet.Cotizador.Api.Domain.User", "User")
                        .WithOne("UserProfile")
                        .HasForeignKey("Raiconet.Cotizador.Api.Domain.UserProfile", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_profile_asp_net_user_user_id");

                    b.OwnsOne("Raiconet.Cotizador.Api.Domain.UserAddress", "Address", b1 =>
                        {
                            b1.Property<int>("UserProfileId")
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            b1.Property<string>("AddressLine1")
                                .IsRequired()
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("address_address_line1");

                            b1.Property<string>("AddressLine2")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)")
                                .HasColumnName("address_address_line2");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("address_city");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("address_country");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("address_postal_code");

                            b1.HasKey("UserProfileId");

                            b1.ToTable("user_profile");

                            b1.WithOwner()
                                .HasForeignKey("UserProfileId")
                                .HasConstraintName("fk_user_profile_user_profile_id");
                        });

                    b.Navigation("Address")
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.AccountExecutive", b =>
                {
                    b.Navigation("Customers");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.AccountExecutiveType", b =>
                {
                    b.Navigation("AccountExecutives");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.BillingReasons", b =>
                {
                    b.Navigation("BillingConcepts");

                    b.Navigation("CustomerBillingReason");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.Customer", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("CustomerBillingReason");

                    b.Navigation("Files");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.CustomerType", b =>
                {
                    b.Navigation("Customers");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.PaymentTerm", b =>
                {
                    b.Navigation("Customers");
                });

            modelBuilder.Entity("Raiconet.Cotizador.Api.Domain.User", b =>
                {
                    b.Navigation("UserProfile");
                });
#pragma warning restore 612, 618
        }
    }
}
