using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Raiconet.Cotizador.Api.Migrations
{
    /// <inheritdoc />
    public partial class initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "account_executive_type",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    name = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_account_executive_type", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "additional_services",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    description = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    minimum_value = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    ups_value = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    dhl_value = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    fedex_value = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    is_active = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_additional_services", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_role",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    normalized_name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    concurrency_stamp = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_asp_net_role", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user",
                columns: table => new
                {
                    id = table.Column<string>(type: "text", nullable: false),
                    is_enabled = table.Column<bool>(type: "boolean", nullable: false),
                    user_profile_id = table.Column<int>(type: "integer", nullable: false),
                    user_name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    normalized_user_name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    normalized_email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    email_confirmed = table.Column<bool>(type: "boolean", nullable: false),
                    password_hash = table.Column<string>(type: "text", nullable: true),
                    security_stamp = table.Column<string>(type: "text", nullable: true),
                    concurrency_stamp = table.Column<string>(type: "text", nullable: true),
                    phone_number = table.Column<string>(type: "text", nullable: true),
                    phone_number_confirmed = table.Column<bool>(type: "boolean", nullable: false),
                    two_factor_enabled = table.Column<bool>(type: "boolean", nullable: false),
                    lockout_end = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    lockout_enabled = table.Column<bool>(type: "boolean", nullable: false),
                    access_failed_count = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_asp_net_user", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "billing_reasons",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    code = table.Column<int>(type: "integer", nullable: false),
                    description = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    type = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_billing_reasons", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "customer_type",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    name = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_type", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "fedex_package_types",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    description = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_package_types", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "fedex_zones_export",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    country = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ip = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ie = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ipf = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ief = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ipe = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_zones_export", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "fedex_zones_import",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    country = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ip = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ie = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ipf = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ief = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_zones_import", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "otp",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    code = table.Column<string>(type: "character varying(6)", maxLength: 6, nullable: false),
                    expires_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    verification_attempts = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    is_verified = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_otp", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "payment_terms",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    days = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_payment_terms", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "rate_time",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    zone = table.Column<int>(type: "integer", nullable: false),
                    priority_service = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    express_service = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    deleted_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_rate_time", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "ups_package_types",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    description = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_package_types", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "ups_zones_export",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    country = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    iata_code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    express_plus = table.Column<int>(type: "integer", nullable: true),
                    express = table.Column<int>(type: "integer", nullable: true),
                    freight_midday = table.Column<int>(type: "integer", nullable: true),
                    express_freight = table.Column<int>(type: "integer", nullable: true),
                    express_saver = table.Column<int>(type: "integer", nullable: true),
                    expedited = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_zones_export", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "ups_zones_import",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    country = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    iata_code = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    express = table.Column<int>(type: "integer", nullable: true),
                    express_freight = table.Column<int>(type: "integer", nullable: true),
                    express_saver = table.Column<int>(type: "integer", nullable: true),
                    expedited = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_zones_import", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "account_executives",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    account_executive_type_id = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_account_executives", x => x.id);
                    table.ForeignKey(
                        name: "fk_account_executives_account_executive_type_account_executive",
                        column: x => x.account_executive_type_id,
                        principalTable: "account_executive_type",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_role_claim",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    role_id = table.Column<string>(type: "text", nullable: false),
                    claim_type = table.Column<string>(type: "text", nullable: true),
                    claim_value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_asp_net_role_claim", x => x.id);
                    table.ForeignKey(
                        name: "fk_asp_net_role_claim_asp_net_roles_role_id",
                        column: x => x.role_id,
                        principalTable: "asp_net_role",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user_claim",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    user_id = table.Column<string>(type: "text", nullable: false),
                    claim_type = table.Column<string>(type: "text", nullable: true),
                    claim_value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_asp_net_user_claim", x => x.id);
                    table.ForeignKey(
                        name: "fk_asp_net_user_claim_asp_net_users_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user_login",
                columns: table => new
                {
                    login_provider = table.Column<string>(type: "text", nullable: false),
                    provider_key = table.Column<string>(type: "text", nullable: false),
                    provider_display_name = table.Column<string>(type: "text", nullable: true),
                    user_id = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_asp_net_user_login", x => new { x.login_provider, x.provider_key });
                    table.ForeignKey(
                        name: "fk_asp_net_user_login_asp_net_user_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user_role",
                columns: table => new
                {
                    user_id = table.Column<string>(type: "text", nullable: false),
                    role_id = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_asp_net_user_role", x => new { x.user_id, x.role_id });
                    table.ForeignKey(
                        name: "fk_asp_net_user_role_asp_net_role_role_id",
                        column: x => x.role_id,
                        principalTable: "asp_net_role",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_asp_net_user_role_asp_net_user_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "asp_net_user_token",
                columns: table => new
                {
                    user_id = table.Column<string>(type: "text", nullable: false),
                    login_provider = table.Column<string>(type: "text", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_asp_net_user_token", x => new { x.user_id, x.login_provider, x.name });
                    table.ForeignKey(
                        name: "fk_asp_net_user_token_asp_net_user_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "user_profile",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    first_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    last_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    address_country = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    address_address_line1 = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    address_address_line2 = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    address_postal_code = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    address_city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    user_id = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_user_profile", x => x.id);
                    table.ForeignKey(
                        name: "fk_user_profile_asp_net_user_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "billing_concepts",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    concept = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    billing_reason_id = table.Column<int>(type: "integer", nullable: false),
                    amount = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    percentage = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: false),
                    min = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    max = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    detail = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_billing_concepts", x => x.id);
                    table.ForeignKey(
                        name: "fk_billing_concepts_billing_reasons_billing_reason_id",
                        column: x => x.billing_reason_id,
                        principalTable: "billing_reasons",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "fedex_export_ie",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_export_ie", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_export_ie_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "fedex_export_ief",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_export_ief", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_export_ief_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "fedex_export_ip",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_export_ip", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_export_ip_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "fedex_export_ipe",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_export_ipe", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_export_ipe_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "fedex_export_ipf",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_export_ipf", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_export_ipf_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "fedex_import_ie",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_import_ie", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_import_ie_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "fedex_import_ief",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_import_ief", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_import_ief_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "fedex_import_ip",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_import_ip", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_import_ip_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "fedex_import_ipf",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    weight_range = table.Column<string>(type: "text", nullable: true),
                    zone_a = table.Column<int>(type: "integer", nullable: true),
                    zone_b = table.Column<int>(type: "integer", nullable: true),
                    zone_c = table.Column<int>(type: "integer", nullable: true),
                    zone_d = table.Column<int>(type: "integer", nullable: true),
                    zone_e = table.Column<int>(type: "integer", nullable: true),
                    zone_f = table.Column<int>(type: "integer", nullable: true),
                    zone_g = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_fedex_import_ipf", x => x.id);
                    table.ForeignKey(
                        name: "fk_fedex_import_ipf_fedex_package_type_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "fedex_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ups_export_expedited",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    zone1 = table.Column<int>(type: "integer", nullable: true),
                    zone2 = table.Column<int>(type: "integer", nullable: true),
                    zone3 = table.Column<int>(type: "integer", nullable: true),
                    zone4 = table.Column<int>(type: "integer", nullable: true),
                    zone5 = table.Column<int>(type: "integer", nullable: true),
                    zone6 = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_export_expedited", x => x.id);
                    table.ForeignKey(
                        name: "fk_ups_export_expedited_ups_package_types_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "ups_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ups_export_express",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    zone1 = table.Column<int>(type: "integer", nullable: true),
                    zone2 = table.Column<int>(type: "integer", nullable: true),
                    zone3 = table.Column<int>(type: "integer", nullable: true),
                    zone4 = table.Column<int>(type: "integer", nullable: true),
                    zone5 = table.Column<int>(type: "integer", nullable: true),
                    zone6 = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_export_express", x => x.id);
                    table.ForeignKey(
                        name: "fk_ups_export_express_ups_package_types_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "ups_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ups_export_saver",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    zone1 = table.Column<int>(type: "integer", nullable: true),
                    zone2 = table.Column<int>(type: "integer", nullable: true),
                    zone3 = table.Column<int>(type: "integer", nullable: true),
                    zone4 = table.Column<int>(type: "integer", nullable: true),
                    zone5 = table.Column<int>(type: "integer", nullable: true),
                    zone6 = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_export_saver", x => x.id);
                    table.ForeignKey(
                        name: "fk_ups_export_saver_ups_package_types_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "ups_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ups_import_expedited",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    zone1 = table.Column<int>(type: "integer", nullable: true),
                    zone2 = table.Column<int>(type: "integer", nullable: true),
                    zone3 = table.Column<int>(type: "integer", nullable: true),
                    zone4 = table.Column<int>(type: "integer", nullable: true),
                    zone5 = table.Column<int>(type: "integer", nullable: true),
                    zone6 = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_import_expedited", x => x.id);
                    table.ForeignKey(
                        name: "fk_ups_import_expedited_ups_package_types_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "ups_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ups_import_express_freight",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    zone1 = table.Column<int>(type: "integer", nullable: true),
                    zone2 = table.Column<int>(type: "integer", nullable: true),
                    zone3 = table.Column<int>(type: "integer", nullable: true),
                    zone4 = table.Column<int>(type: "integer", nullable: true),
                    zone5 = table.Column<int>(type: "integer", nullable: true),
                    zone6 = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_import_express_freight", x => x.id);
                    table.ForeignKey(
                        name: "fk_ups_import_express_freight_ups_package_types_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "ups_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ups_import_saver",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    package_type_id = table.Column<int>(type: "integer", nullable: false),
                    weight = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    zone1 = table.Column<int>(type: "integer", nullable: true),
                    zone2 = table.Column<int>(type: "integer", nullable: true),
                    zone3 = table.Column<int>(type: "integer", nullable: true),
                    zone4 = table.Column<int>(type: "integer", nullable: true),
                    zone5 = table.Column<int>(type: "integer", nullable: true),
                    zone6 = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ups_import_saver", x => x.id);
                    table.ForeignKey(
                        name: "fk_ups_import_saver_ups_package_types_package_type_id",
                        column: x => x.package_type_id,
                        principalTable: "ups_package_types",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "customers",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    user_id = table.Column<string>(type: "text", nullable: false),
                    tax_id = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    business_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    two_factor_enabled = table.Column<bool>(type: "boolean", nullable: false),
                    account_executive_id = table.Column<int>(type: "integer", nullable: false),
                    customer_type_id = table.Column<int>(type: "integer", nullable: false),
                    payment_term_id = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customers", x => x.id);
                    table.ForeignKey(
                        name: "fk_customers_account_executives_account_executive_id",
                        column: x => x.account_executive_id,
                        principalTable: "account_executives",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_customers_asp_net_users_user_id",
                        column: x => x.user_id,
                        principalTable: "asp_net_user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_customers_customer_type_customer_type_id",
                        column: x => x.customer_type_id,
                        principalTable: "customer_type",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_customers_payment_term_payment_term_id",
                        column: x => x.payment_term_id,
                        principalTable: "payment_terms",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "addresses",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    customer_id = table.Column<int>(type: "integer", nullable: false),
                    country_code = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    province = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    city = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    street_address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    address_comments = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    postal_code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    phone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    mobile = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    commercial_contact = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    legal_address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    legal_contact1 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    legal_contact2 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    commercial_address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    commercial_contact1 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    commercial_contact2 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    billing_contact1 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    billing_contact2 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    redispatch_address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    redispatch_contact1 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    redispatch_contact2 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    delivery_address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    delivery_contact1 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    delivery_contact2 = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_addresses", x => x.id);
                    table.ForeignKey(
                        name: "fk_addresses_customer_customer_id",
                        column: x => x.customer_id,
                        principalTable: "customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_billing_concepts",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    customer_id = table.Column<int>(type: "integer", nullable: false),
                    billing_reason_id = table.Column<int>(type: "integer", nullable: false),
                    percentage = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_billing_concepts", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_billing_concepts_billing_reasons_billing_reason_id",
                        column: x => x.billing_reason_id,
                        principalTable: "billing_reasons",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_customer_billing_concepts_customer_customer_id",
                        column: x => x.customer_id,
                        principalTable: "customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "customer_files",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    url = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    customer_id = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_customer_files", x => x.id);
                    table.ForeignKey(
                        name: "fk_customer_files_customers_customer_id",
                        column: x => x.customer_id,
                        principalTable: "customers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "fedex_package_types",
                columns: new[] { "id", "code", "description" },
                values: new object[,]
                {
                    { 1, "ENVELOPE", "Sobre" },
                    { 2, "PAK", "Pak" },
                    { 3, "PACKAGE", "Paquete" },
                    { 4, "KG_RANGE", "Por kilogramo (Rango)" }
                });

            migrationBuilder.InsertData(
                table: "ups_package_types",
                columns: new[] { "id", "code", "description" },
                values: new object[,]
                {
                    { 1, "DOC_SOBRE", "DOCUMENTO (OTRO SOBRE)" },
                    { 2, "PAQUETE", "PAQUETE (NO DOCUMENTO)" },
                    { 3, "POR_KILO", "PRECIO POR KILO" },
                    { 4, "SOBRE_CARTON", "SOBRE (CARTÓN)" },
                    { 5, "TARIFA_MIN", "TARIFA MINIMA" }
                });

            migrationBuilder.CreateIndex(
                name: "ix_account_executives_account_executive_type_id",
                table: "account_executives",
                column: "account_executive_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_additional_services_code",
                table: "additional_services",
                column: "code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_addresses_customer_id",
                table: "addresses",
                column: "customer_id");

            migrationBuilder.CreateIndex(
                name: "RoleNameIndex",
                table: "asp_net_role",
                column: "normalized_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_asp_net_role_claim_role_id",
                table: "asp_net_role_claim",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                table: "asp_net_user",
                column: "normalized_email");

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                table: "asp_net_user",
                column: "normalized_user_name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_asp_net_user_claim_user_id",
                table: "asp_net_user_claim",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_asp_net_user_login_user_id",
                table: "asp_net_user_login",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_asp_net_user_role_role_id",
                table: "asp_net_user_role",
                column: "role_id");

            migrationBuilder.CreateIndex(
                name: "ix_billing_concepts_billing_reason_id",
                table: "billing_concepts",
                column: "billing_reason_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_billing_concepts_billing_reason_id",
                table: "customer_billing_concepts",
                column: "billing_reason_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_billing_concepts_customer_id",
                table: "customer_billing_concepts",
                column: "customer_id");

            migrationBuilder.CreateIndex(
                name: "ix_customer_files_customer_id",
                table: "customer_files",
                column: "customer_id");

            migrationBuilder.CreateIndex(
                name: "ix_customers_account_executive_id",
                table: "customers",
                column: "account_executive_id");

            migrationBuilder.CreateIndex(
                name: "ix_customers_customer_type_id",
                table: "customers",
                column: "customer_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_customers_payment_term_id",
                table: "customers",
                column: "payment_term_id");

            migrationBuilder.CreateIndex(
                name: "ix_customers_user_id",
                table: "customers",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_export_ie_package_type_id",
                table: "fedex_export_ie",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_export_ief_package_type_id",
                table: "fedex_export_ief",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_export_ip_package_type_id",
                table: "fedex_export_ip",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_export_ipe_package_type_id",
                table: "fedex_export_ipe",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_export_ipf_package_type_id",
                table: "fedex_export_ipf",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_import_ie_package_type_id",
                table: "fedex_import_ie",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_import_ief_package_type_id",
                table: "fedex_import_ief",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_import_ip_package_type_id",
                table: "fedex_import_ip",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_import_ipf_package_type_id",
                table: "fedex_import_ipf",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_fedex_zones_export_country",
                table: "fedex_zones_export",
                column: "country",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_fedex_zones_import_country",
                table: "fedex_zones_import",
                column: "country",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_otp_email",
                table: "otp",
                column: "email");

            migrationBuilder.CreateIndex(
                name: "ix_otp_email_expires_at",
                table: "otp",
                columns: new[] { "email", "expires_at" });

            migrationBuilder.CreateIndex(
                name: "ix_rate_time_zone",
                table: "rate_time",
                column: "zone",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_ups_export_expedited_package_type_id",
                table: "ups_export_expedited",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_ups_export_express_package_type_id",
                table: "ups_export_express",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_ups_export_saver_package_type_id",
                table: "ups_export_saver",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_ups_import_expedited_package_type_id",
                table: "ups_import_expedited",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_ups_import_express_freight_package_type_id",
                table: "ups_import_express_freight",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_ups_import_saver_package_type_id",
                table: "ups_import_saver",
                column: "package_type_id");

            migrationBuilder.CreateIndex(
                name: "ix_ups_zones_export_country_iata_code",
                table: "ups_zones_export",
                columns: new[] { "country", "iata_code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_ups_zones_import_country_iata_code",
                table: "ups_zones_import",
                columns: new[] { "country", "iata_code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_user_profile_user_id",
                table: "user_profile",
                column: "user_id",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "additional_services");

            migrationBuilder.DropTable(
                name: "addresses");

            migrationBuilder.DropTable(
                name: "asp_net_role_claim");

            migrationBuilder.DropTable(
                name: "asp_net_user_claim");

            migrationBuilder.DropTable(
                name: "asp_net_user_login");

            migrationBuilder.DropTable(
                name: "asp_net_user_role");

            migrationBuilder.DropTable(
                name: "asp_net_user_token");

            migrationBuilder.DropTable(
                name: "billing_concepts");

            migrationBuilder.DropTable(
                name: "customer_billing_concepts");

            migrationBuilder.DropTable(
                name: "customer_files");

            migrationBuilder.DropTable(
                name: "fedex_export_ie");

            migrationBuilder.DropTable(
                name: "fedex_export_ief");

            migrationBuilder.DropTable(
                name: "fedex_export_ip");

            migrationBuilder.DropTable(
                name: "fedex_export_ipe");

            migrationBuilder.DropTable(
                name: "fedex_export_ipf");

            migrationBuilder.DropTable(
                name: "fedex_import_ie");

            migrationBuilder.DropTable(
                name: "fedex_import_ief");

            migrationBuilder.DropTable(
                name: "fedex_import_ip");

            migrationBuilder.DropTable(
                name: "fedex_import_ipf");

            migrationBuilder.DropTable(
                name: "fedex_zones_export");

            migrationBuilder.DropTable(
                name: "fedex_zones_import");

            migrationBuilder.DropTable(
                name: "otp");

            migrationBuilder.DropTable(
                name: "rate_time");

            migrationBuilder.DropTable(
                name: "ups_export_expedited");

            migrationBuilder.DropTable(
                name: "ups_export_express");

            migrationBuilder.DropTable(
                name: "ups_export_saver");

            migrationBuilder.DropTable(
                name: "ups_import_expedited");

            migrationBuilder.DropTable(
                name: "ups_import_express_freight");

            migrationBuilder.DropTable(
                name: "ups_import_saver");

            migrationBuilder.DropTable(
                name: "ups_zones_export");

            migrationBuilder.DropTable(
                name: "ups_zones_import");

            migrationBuilder.DropTable(
                name: "user_profile");

            migrationBuilder.DropTable(
                name: "asp_net_role");

            migrationBuilder.DropTable(
                name: "billing_reasons");

            migrationBuilder.DropTable(
                name: "customers");

            migrationBuilder.DropTable(
                name: "fedex_package_types");

            migrationBuilder.DropTable(
                name: "ups_package_types");

            migrationBuilder.DropTable(
                name: "account_executives");

            migrationBuilder.DropTable(
                name: "asp_net_user");

            migrationBuilder.DropTable(
                name: "customer_type");

            migrationBuilder.DropTable(
                name: "payment_terms");

            migrationBuilder.DropTable(
                name: "account_executive_type");
        }
    }
}
