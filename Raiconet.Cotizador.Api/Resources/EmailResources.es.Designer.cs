//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Raiconet.Cotizador.Api.Resources {
    using System;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class EmailResources_es {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EmailResources_es() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("Raiconet.Cotizador.Api.Resources.EmailResources_es", typeof(EmailResources_es).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        internal static string ConfirmEmail_Subject {
            get {
                return ResourceManager.GetString("ConfirmEmail_Subject", resourceCulture);
            }
        }
        
        internal static string ResetPassword_Subject {
            get {
                return ResourceManager.GetString("ResetPassword_Subject", resourceCulture);
            }
        }
        
        internal static string ResetCode_Subject {
            get {
                return ResourceManager.GetString("ResetCode_Subject", resourceCulture);
            }
        }
        
        internal static string Welcome_Subject {
            get {
                return ResourceManager.GetString("Welcome_Subject", resourceCulture);
            }
        }
    }
}
