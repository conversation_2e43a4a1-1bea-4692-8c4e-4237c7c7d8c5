<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="InvalidCredentials" xml:space="preserve">
        <value>Invalid credentials</value>
    </data>
    <data name="InvalidCredentialsDesc" xml:space="preserve">
        <value>Password and/or email are incorrect</value>
    </data>
    <data name="ServerError" xml:space="preserve">
        <value>An error occured while processing your request</value>
    </data>
    <data name="ServerErrorDesc" xml:space="preserve">
        <value>Try again later, if the error persist contact support.</value>
    </data>
    <data name="InvalidOtp" xml:space="preserve">
        <value>Invalid code</value>
    </data>
    <data name="InvalidOtpDesc" xml:space="preserve">
        <value>The code is expired or wrong</value>
    </data>
    <data name="EmailInvalid" xml:space="preserve">
        <value>Invalid email</value>
    </data>
    <data name="NotEmpty" xml:space="preserve">
        <value>Cannot be empty</value>
    </data>
    <data name="DefaultError" xml:space="preserve">
    <value>An error has occurred.</value>
</data>
    <data name="ConcurrencyFailure" xml:space="preserve">
    <value>Optimistic concurrency failure, object has been modified.</value>
</data>
    <data name="PasswordMismatch" xml:space="preserve">
    <value>Incorrect password.</value>
</data>
    <data name="InvalidToken" xml:space="preserve">
    <value>Invalid token.</value>
</data>
    <data name="RecoveryCodeRedemptionFailed" xml:space="preserve">
    <value>Recovery code redemption failed.</value>
</data>
    <data name="LoginAlreadyAssociated" xml:space="preserve">
    <value>A user with this login already exists.</value>
</data>
    <data name="InvalidUserName" xml:space="preserve">
    <value>Username '{0}' is invalid, can only contain letters or digits.</value>
</data>
    <data name="InvalidEmail" xml:space="preserve">
    <value>Email '{0}' is invalid.</value>
</data>
    <data name="DuplicateUserName" xml:space="preserve">
    <value>Username '{0}' is already taken.</value>
</data>
    <data name="DuplicateEmail" xml:space="preserve">
    <value>Email '{0}' is already taken.</value>
</data>
    <data name="InvalidRoleName" xml:space="preserve">
    <value>Role name '{0}' is invalid.</value>
</data>
    <data name="DuplicateRoleName" xml:space="preserve">
    <value>Role name '{0}' is already taken.</value>
</data>
    <data name="UserAlreadyHasPassword" xml:space="preserve">
    <value>User already has a password set.</value>
</data>
    <data name="UserLockoutNotEnabled" xml:space="preserve">
    <value>Lockout is not enabled for this user.</value>
</data>
    <data name="UserAlreadyInRole" xml:space="preserve">
    <value>User already in role '{0}'.</value>
</data>
    <data name="UserNotInRole" xml:space="preserve">
    <value>User is not in role '{0}'.</value>
</data>
    <data name="PasswordTooShort" xml:space="preserve">
    <value>Password must be at least {0} characters long.</value>
</data>
    <data name="PasswordRequiresUniqueChars" xml:space="preserve">
    <value>Password must use at least {0} different characters.</value>
</data>
    <data name="PasswordRequiresNonAlphanumeric" xml:space="preserve">
    <value>Password must contain at least one special character.</value>
</data>
    <data name="PasswordRequiresDigit" xml:space="preserve">
    <value>Password must contain at least one number.</value>
</data>
    <data name="PasswordRequiresLower" xml:space="preserve">
    <value>Password must contain at least one lowercase letter.</value>
</data>
    <data name="PasswordRequiresUpper" xml:space="preserve">
    <value>Password must contain at least one uppercase letter.</value>
</data>
    <data name="EmailAlreadyInUse" xml:space="preserve">
    <value>Email address is already in use</value>
</data>
    <data name="EmailAlreadyInUseDetail" xml:space="preserve">
    <value>Email address '{0}' is already in use</value>
</data>
    <data name="EmailInvalidFormat" xml:space="preserve">
    <value>Invalid email format</value>
</data>
    <data name="GreaterThanZero" xml:space="preserve">
        <value>The value must be greater than 0</value>
    </data>
    <data name="InvalidLookupId" xml:space="preserve">
        <value>The entity '{0}' with id '{1}' does not exist.</value>
    </data>
    <data name="ClientTypeNotFound" xml:space="preserve">
        <value>The client type with ID '{0}' does not exist.</value>
    </data>
    <data name="ClientTypeNotFoundDesc" xml:space="preserve">
        <value>Refresh the page and try again.</value>
    </data>
    <data name="MaxLength" xml:space="preserve">
    <value>The length must be less than {0}</value>
  </data>
</root>