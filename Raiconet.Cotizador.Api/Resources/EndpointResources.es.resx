<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="InvalidCredentials" xml:space="preserve">
        <value>Credenciales Inválidas</value>
    </data>
    <data name="InvalidCredentialsDesc" xml:space="preserve">
        <value>Contraseña y/o correo son inválidos</value>
    </data>
    <data name="ServerError" xml:space="preserve">
        <value>Ocurrió un error al momento de procesar la solicitud</value>
    </data>
    <data name="ServerErrorDesc" xml:space="preserve">
        <value>Intenta de nuevo, si el error persiste contacta a soporte.</value>
    </data>
    <data name="InvalidOtp" xml:space="preserve">
        <value>Código de seguridad inválido</value>
    </data>
    <data name="InvalidOtpDesc" xml:space="preserve">
        <value>El código ha expirado o es incorrecto</value>
    </data>
    <data name="EmailInvalid" xml:space="preserve">
        <value>Correo inválido</value>
    </data>
    <data name="NotEmpty" xml:space="preserve">
        <value>El campo no puede estar vacío</value>
    </data>
    <!-- Add these to your Spanish resource file -->
    <data name="DefaultError" xml:space="preserve">
    <value>Ha ocurrido un error.</value>
</data>
    <data name="ConcurrencyFailure" xml:space="preserve">
    <value>Error de concurrencia, el objeto ha sido modificado.</value>
</data>
    <data name="PasswordMismatch" xml:space="preserve">
    <value>Contraseña incorrecta.</value>
</data>
    <data name="InvalidToken" xml:space="preserve">
    <value>Token inválido.</value>
</data>
    <data name="RecoveryCodeRedemptionFailed" xml:space="preserve">
    <value>Error al canjear el código de recuperación.</value>
</data>
    <data name="LoginAlreadyAssociated" xml:space="preserve">
    <value>Ya existe un usuario con este inicio de sesión.</value>
</data>
    <data name="InvalidUserName" xml:space="preserve">
    <value>El nombre de usuario '{0}' es inválido, solo puede contener letras o dígitos.</value>
</data>
    <data name="InvalidEmail" xml:space="preserve">
    <value>El correo '{0}' es inválido.</value>
</data>
    <data name="DuplicateUserName" xml:space="preserve">
    <value>El nombre de usuario '{0}' ya está en uso.</value>
</data>
    <data name="DuplicateEmail" xml:space="preserve">
    <value>El correo '{0}' ya está en uso.</value>
</data>
    <data name="InvalidRoleName" xml:space="preserve">
    <value>El nombre del rol '{0}' es inválido.</value>
</data>
    <data name="DuplicateRoleName" xml:space="preserve">
    <value>El nombre del rol '{0}' ya está en uso.</value>
</data>
    <data name="UserAlreadyHasPassword" xml:space="preserve">
    <value>El usuario ya tiene una contraseña establecida.</value>
</data>
    <data name="UserLockoutNotEnabled" xml:space="preserve">
    <value>El bloqueo no está habilitado para este usuario.</value>
</data>
    <data name="UserAlreadyInRole" xml:space="preserve">
    <value>El usuario ya está en el rol '{0}'.</value>
</data>
    <data name="UserNotInRole" xml:space="preserve">
    <value>El usuario no está en el rol '{0}'.</value>
</data>
    <data name="PasswordTooShort" xml:space="preserve">
    <value>La contraseña debe tener al menos {0} caracteres.</value>
</data>
    <data name="PasswordRequiresUniqueChars" xml:space="preserve">
    <value>La contraseña debe usar al menos {0} caracteres diferentes.</value>
</data>
    <data name="PasswordRequiresNonAlphanumeric" xml:space="preserve">
    <value>La contraseña debe contener al menos un carácter especial.</value>
</data>
    <data name="PasswordRequiresDigit" xml:space="preserve">
    <value>La contraseña debe contener al menos un número.</value>
</data>
    <data name="PasswordRequiresLower" xml:space="preserve">
    <value>La contraseña debe contener al menos una letra minúscula.</value>
</data>
    <data name="PasswordRequiresUpper" xml:space="preserve">
    <value>La contraseña debe contener al menos una letra mayúscula.</value>
</data>
    <data name="EmailAlreadyInUse" xml:space="preserve">
    <value>El correo electrónico ya está en uso</value>
</data>
    <data name="EmailAlreadyInUseDetail" xml:space="preserve">
    <value>El correo electrónico '{0}' ya está en uso</value>
</data>
    <data name="EmailInvalidFormat" xml:space="preserve">
    <value>Formato de correo electrónico inválido</value>
</data>
    <data name="GreaterThanZero" xml:space="preserve">
        <value>El valor tiene que ser mayor a 0</value>
    </data>
    <data name="InvalidLookupId" xml:space="preserve">
        <value>La entidad '{0}' con el identificador '{1}' no existe.</value>
    </data>
    <data name="ClientTypeNotFound" xml:space="preserve">
        <value>El tipo de cliente con el ID '{0}' no existe.</value>
    </data>
    <data name="ClientTypeNotFoundDesc" xml:space="preserve">
        <value>Refresca la página e intenta de nuevo.</value>
    </data>
    <data name="MaxLength" xml:space="preserve">
        <value>La longitud del campo debe ser menor a {0}</value>
    </data>
</root>