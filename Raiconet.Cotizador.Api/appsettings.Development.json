//{
//  "ConnectionStrings": {
//    "DefaultConnection": "Server=localhost;Port=5433;Userid=********;Password=********;Pooling=false;MinPoolSize=1;MaxPoolSize=20;Timeout=15;SslMode=Disable;Database=raiconet"
//  },
//  "AppSettings": {
//    "AppUrl": "http://localhost:3000/"
//  },
//  "Resend": {
//    "ApiKey": "re_UzD6kA39_E9mxczaLBHGnWacd1P6HteMH"
//  },
//  "JWT": {
//    "ValidAudience": "http://localhost:3000",
//    "ValidIssuer": "http://localhost:5000",
//    "Secret": "JWTAuthenticationHIGHsecuredPasswordVVVp1OH7Xzyr"
//  }
//}
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=*************;Port=5454;Userid=********;Password=****************************************************************;Pooling=false;MinPoolSize=1;MaxPoolSize=20;Timeout=15;SslMode=Disable;Database=********"
  },
  "AppSettings": {
    "AppUrl": "http://localhost:3000/"
  },
  "Resend": {
    "ApiKey": "re_UzD6kA39_E9mxczaLBHGnWacd1P6HteMH"
  },
  "JWT": {
    "ValidAudience": "http://localhost:3000",
    "ValidIssuer": "http://localhost:5000",
    "Secret": "JWTAuthenticationHIGHsecuredPasswordVVVp1OH7Xzyr"
  }
}