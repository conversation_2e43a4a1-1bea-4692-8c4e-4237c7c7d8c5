<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>Raiconet.Cotizador.Api</RootNamespace>
		<UserSecretsId>a7f0276d-94b2-4645-bd92-6d9a8a2015eb</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Ardalis.GuardClauses" Version="5.0.0" />
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0"/>
        <PackageReference Include="FluentValidation" Version="11.11.0" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
        <PackageReference Include="Handlebars.Net" Version="2.1.6"/>
        <PackageReference Include="Infisical.IConfigurationProvider" Version="0.0.2" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.2" />
        <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.1"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.1"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.1">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.3"/>
        <PackageReference Include="Resend" Version="0.0.12"/>
        <PackageReference Include="Serilog" Version="4.2.0"/>
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0"/>
        <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.0"/>
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0"/>
        <PackageReference Include="SharpGrip.FluentValidation.AutoValidation.Endpoints" Version="1.5.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0"/>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="Resources\EmailResources.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>EmailResources.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resources\EmailResources.es.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>EmailResources.es.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resources\EndpointResources.resx">
          <Generator>ResXFileCodeGenerator</Generator>
          <LastGenOutput>EndpointResources.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resources\EndpointResources.es.resx">
          <Generator>ResXFileCodeGenerator</Generator>
          <LastGenOutput>EndpointResources.es.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Resources\EmailResources.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>EmailResources.resx</DependentUpon>
        </Compile>
        <Compile Update="Resources\EmailResources.es.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>EmailResources.es.resx</DependentUpon>
        </Compile>
        <Compile Update="Resources\EndpointResources.Designer.cs">
          <DesignTime>True</DesignTime>
          <AutoGen>True</AutoGen>
          <DependentUpon>EndpointResources.resx</DependentUpon>
        </Compile>
        <Compile Update="Resources\EndpointResources.es.Designer.cs">
          <DesignTime>True</DesignTime>
          <AutoGen>True</AutoGen>
          <DependentUpon>EndpointResources.es.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <None Include="Templates\**\*.hbs">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Features\Tenants\Handlers\" />
      <Folder Include="Migrations\" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>
</Project>
