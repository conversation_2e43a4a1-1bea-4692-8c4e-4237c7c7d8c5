using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class BillingReasonsConfiguration : IEntityTypeConfiguration<BillingReasons>
{
    public void Configure(EntityTypeBuilder<BillingReasons> builder)
    {
        builder.ToTable("billing_reasons");
       
        builder.HasKey(x => x.Id);
       
        builder.Property(x => x.Code)
            .IsRequired();
        
        builder.Property(x => x.CifFreightRate);
           
        builder.Property(x => x.Description)
            .HasMaxLength(100)
            .IsRequired();
        
           
        builder.Property(x => x.Type)
            .HasConversion<int>()
            .IsRequired();

        builder.HasMany(x => x.BillingConcepts)
            .WithOne(x => x.Reason)
            .HasForeignKey(x => x.BillingReasonId);
        
        builder.HasOne(x => x.SetupRaicoService)
            .WithMany(x => x.BillingReasons);
    }
}