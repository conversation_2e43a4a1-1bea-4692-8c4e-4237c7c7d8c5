using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class CustomerFileConfiguration : IEntityTypeConfiguration<CustomerFile>
{
    public void Configure(EntityTypeBuilder<CustomerFile> builder)
    {
        builder.ToTable("customer_files");
       
        builder.HasKey(x => x.Id);
       
        builder.Property(x => x.Description)
            .HasMaxLength(200);
           
        builder.Property(x => x.Url)
            .HasMaxLength(500)
            .IsRequired();

        builder.HasOne(x => x.Customer)
            .WithMany(x => x.Files)
            .HasForeignKey(x => x.CustomerId);
    }
}