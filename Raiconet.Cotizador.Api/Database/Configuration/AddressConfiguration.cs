using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class AddressConfiguration : IEntityTypeConfiguration<Address>
{
    public void Configure(EntityTypeBuilder<Address> builder)
    {
        builder.ToTable("addresses");
       
        builder.HasKey(x => x.Id);
       
        builder.Property(x => x.CountryCode).HasMaxLength(3);
        builder.Property(x => x.Province).HasMaxLength(100).IsRequired(false);
        builder.Property(x => x.City).HasMaxLength(100).IsRequired(false);
        builder.Property(x => x.StreetAddress).HasMaxLength(200).IsRequired(false);
        builder.Property(x => x.AddressComments).HasMaxLength(500).IsRequired(false);
        builder.Property(x => x.PostalCode).HasMaxLength(10).IsRequired(false);
        builder.Property(x => x.Phone).HasMaxLength(20).IsRequired(false);
        builder.Property(x => x.Mobile).HasMaxLength(20).IsRequired(false);
        builder.Property(x => x.LegalAddress).HasMaxLength(200).IsRequired(false);
        builder.Property(x => x.CommercialAddress).HasMaxLength(200).IsRequired(false);
        builder.Property(x => x.CommercialContact1).HasMaxLength(100).IsRequired(false);
        builder.Property(x => x.BillingAddress).HasMaxLength(200).IsRequired(false);
        builder.Property(x => x.RedispatchAddress).HasMaxLength(200).IsRequired(false);
        builder.Property(x => x.DeliveryAddress).HasMaxLength(200).IsRequired(false);
        builder.Property(x => x.DeliveryContact1).HasMaxLength(100).IsRequired(false);
    }
}