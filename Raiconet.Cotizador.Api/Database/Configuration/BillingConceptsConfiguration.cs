using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class BillingConceptsConfiguration : IEntityTypeConfiguration<BillingConcepts> 
{
    public void Configure(EntityTypeBuilder<BillingConcepts> builder)
    {
        builder.ToTable("billing_concepts");
       
        builder.HasKey(x => x.Id);
       
        builder.Property(x => x.Concept)
            .HasMaxLength(100)
            .IsRequired();
           
        builder.Property(x => x.Amount)
            .HasPrecision(10, 2);
           
        builder.Property(x => x.Percentage)
            .HasPrecision(5, 2);
           
        builder.Property(x => x.Min)
            .HasPrecision(10, 2);
           
        builder.Property(x => x.Max)
            .HasPrecision(10, 2);
           
        builder.Property(x => x.Detail)
            .HasPrecision(10, 2);
    }
}