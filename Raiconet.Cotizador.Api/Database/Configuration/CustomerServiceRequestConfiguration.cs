using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class CustomerServiceRequestEntityConfiguration : IEntityTypeConfiguration<CustomerServiceRequest>
{
    public void Configure(EntityTypeBuilder<CustomerServiceRequest> builder)
    {
        builder.ToTable("customer_service_request");
        builder.HasKey(c => c.Id);

        builder.Property(c => c.Id)
            .ValueGeneratedOnAdd()
            .IsRequired();

        builder.Property(c => c.CustomerId)
            .IsRequired();

        builder.Property(c => c.SetupRaicoServiceId)
            .IsRequired();

        builder.Property(c => c.Status)
            .IsRequired();

        builder.Property(c => c.CreatedAt)
            .IsRequired();

        builder.Property(c => c.CreatedById)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.UpdatedAt)
            .IsRequired();

        builder.Property(c => c.UpdatedBy)
            .IsRequired()
            .HasMaxLength(100);

        // Relationships
        builder.HasOne(c => c.Customer)
            .WithMany()
            .HasForeignKey(c => c.CustomerId);

        builder.HasOne(c => c.SetupRaicoService)
            .WithMany()
            .HasForeignKey(c => c.SetupRaicoServiceId);

        builder.HasOne(c => c.Creator)
            .WithMany()
            .HasForeignKey(c => c.CreatedById);

        // Create index for potential frequent queries
        builder.HasIndex(c => c.CustomerId);
        builder.HasIndex(c => c.Status);
    }
}