using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class CustomerBillingConceptConfiguration : IEntityTypeConfiguration<CustomerBillingReason>
{
    public void Configure(EntityTypeBuilder<CustomerBillingReason> builder)
    {
        builder.ToTable("customer_billing_concepts");
       
        builder.HasKey(x => x.Id);
       
        builder.Property(x => x.Percentage)
            .HasPrecision(5, 2)
            .IsRequired();

        builder.HasOne(x => x.Customer)
            .WithMany(x => x.CustomerBillingReason)
            .HasForeignKey(x => x.CustomerId);

        builder.HasOne(x => x.BillingReason)
            .WithMany(x => x.CustomerBillingReason)
            .HasForeignKey(x => x.BillingReasonId);
    }
}