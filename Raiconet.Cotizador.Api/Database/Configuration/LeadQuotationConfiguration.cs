using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class LeadQuotationConfiguration : IEntityTypeConfiguration<LeadQuotation>
{
    public void Configure(EntityTypeBuilder<LeadQuotation> builder)
    {
        builder.ToTable("leads_quotation");
        
        builder.HasKey(l => l.Id);
        
        builder.Property(l => l.Id)
            .ValueGeneratedOnAdd();
            
        builder.Property(l => l.FirstName)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(l => l.LastName)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(l => l.PhoneNumber)
            .IsRequired()
            .HasMaxLength(20);
            
        builder.Property(l => l.Email)
            .IsRequired()
            .HasMaxLength(255);
            
        builder.Property(l => l.QuotationRequestData)
            .IsRequired()
            .HasColumnType("jsonb"); // PostgreSQL JSONB type for better performance
            
        builder.Property(l => l.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP");
            
        // Indexes for better query performance
        builder.HasIndex(l => l.Email)
            .HasDatabaseName("IX_LeadsQuotation_Email");
            
        builder.HasIndex(l => l.CreatedAt)
            .HasDatabaseName("IX_LeadsQuotation_CreatedAt");
            
        builder.HasIndex(l => new { l.FirstName, l.LastName })
            .HasDatabaseName("IX_LeadsQuotation_FullName");
    }
}
