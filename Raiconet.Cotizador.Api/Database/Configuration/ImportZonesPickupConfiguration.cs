using Raiconet.Cotizador.Api.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class ImportZonesPickUpConfiguration : IEntityTypeConfiguration<ImportZonesPickUp>
{
    public void Configure(EntityTypeBuilder<ImportZonesPickUp> builder)
    {
        builder.ToTable("import_zones_pickup");
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
        
        builder.HasKey(z => z.Id);
        
        builder.Property(z => z.Country)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(z => z.Zone)
            .HasMaxLength(50)
            .IsRequired();
        
        // Índice para búsquedas por país
        builder.HasIndex(z => z.Country);
        
        // Índice compuesto para búsquedas de zona por país
        builder.HasIndex(z => new { z.Country, z.Zone }).IsUnique();
        
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}