using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class SetupServiceTypeConfiguration : IEntityTypeConfiguration<SetupRaicoService>
{
    public void Configure(EntityTypeBuilder<SetupRaicoService> builder)
    {
        builder.ToTable("setup_raico_service"); // Optional: Specify table name

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Description)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(e => e.Code)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(e => e.Status)
            .IsRequired();
    }
}