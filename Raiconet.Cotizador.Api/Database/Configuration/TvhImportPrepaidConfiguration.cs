using Raiconet.Cotizador.Api.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class TvhImportPrepaidConfiguration : IEntityTypeConfiguration<TvhImportPrepaid>
{
    public void Configure(EntityTypeBuilder<TvhImportPrepaid> builder)
    {
        builder.ToTable("tvh_import_prepaid");
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
        
        builder.HasKey(t => t.Id);
        
        builder.Property(t => t.Weight)
            .HasColumnType("decimal(18,2)")
            .IsRequired();
            
        builder.Property(t => t.Price)
            .HasColumnType("decimal(18,2)")
            .IsRequired();
            
        builder.Property(t => t.Cost)
            .HasColumnType("decimal(18,2)")
            .IsRequired();
        
        // Opcional: Agregar un índice en Weight si se busca frecuentemente por este campo
        builder.HasIndex(t => t.Weight);
        
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}