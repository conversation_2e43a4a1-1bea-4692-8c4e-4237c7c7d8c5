using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class FedexExportServiceIpeConfiguration : IEntityTypeConfiguration<FedexExportServicesIpe>
{
    public void Configure(EntityTypeBuilder<FedexExportServicesIpe> builder)
    {
        builder.ToTable("fedex_export_ipe");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}

public class FedexExportServiceIpConfiguration : IEntityTypeConfiguration<FedexExportServiceIp>
{
    public void Configure(EntityTypeBuilder<FedexExportServiceIp> builder)
    {
        builder.ToTable("fedex_export_ip");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}

public class FedexExportServiceIeConfiguration : IEntityTypeConfiguration<FedexExportServicesIe>
{
    public void Configure(EntityTypeBuilder<FedexExportServicesIe> builder)
    {
        builder.ToTable("fedex_export_ie");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}

public class FedexExportServiceIpfConfiguration : IEntityTypeConfiguration<FedexExportServiceIpf>
{
    public void Configure(EntityTypeBuilder<FedexExportServiceIpf> builder)
    {
        builder.ToTable("fedex_export_ipf");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}

public class FedexExportServiceIefConfiguration : IEntityTypeConfiguration<FedexExportServiceIef>
{
    public void Configure(EntityTypeBuilder<FedexExportServiceIef> builder)
    {
        builder.ToTable("fedex_export_ief");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}

public class FedexImportServiceIpConfiguration : IEntityTypeConfiguration<FedexImportServiceIp>
{
    public void Configure(EntityTypeBuilder<FedexImportServiceIp> builder)
    {
        builder.ToTable("fedex_import_ip");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}

public class FedexImportServiceIeConfiguration : IEntityTypeConfiguration<FedexImportServiceIe>
{
    public void Configure(EntityTypeBuilder<FedexImportServiceIe> builder)
    {
        builder.ToTable("fedex_import_ie");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}

public class FedexImportServiceIefConfiguration : IEntityTypeConfiguration<FedexImportServiceIef>
{
    public void Configure(EntityTypeBuilder<FedexImportServiceIef> builder)
    {
        builder.ToTable("fedex_import_ief");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}

public class FedexImportServiceIpfConfiguration : IEntityTypeConfiguration<FedexImportServiceIpf>
{
    public void Configure(EntityTypeBuilder<FedexImportServiceIpf> builder)
    {
        builder.ToTable("fedex_import_ipf");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.FedexPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
        builder.Property(u => u.WeightRange);          
        // Configuración de zonas opcionales
        builder.Property(u => u.ZoneA);
        builder.Property(u => u.ZoneB);
        builder.Property(u => u.ZoneC);
        builder.Property(u => u.ZoneD);
        builder.Property(u => u.ZoneE);
        builder.Property(u => u.ZoneF);
        builder.Property(u => u.ZoneG);
    }
}