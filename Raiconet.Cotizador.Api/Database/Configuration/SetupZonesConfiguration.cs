using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class SetupZonesConfiguration : IEntityTypeConfiguration<SetupZones>
{
    public void Configure(EntityTypeBuilder<SetupZones> builder)
    {
        // Tabla
        builder.ToTable("setup_zones");
        builder.HasKey(x => x.Id);
        
        builder.Property(x => x.Id)
            .ValueGeneratedOnAdd();
        builder.Property(x => x.Country)  
            .IsRequired()
            .HasMaxLength(100); 
        builder.Property(x => x.IataCode)  
            .IsRequired()
            .HasMaxLength(100); 
        builder.Property(x => x.Zone)
            .IsRequired(false);

        builder.Property(x => x.Warehouse)
            .IsRequired(false)
            .HasMaxLength(100); 
        
        builder.HasIndex(x => x.Country)
            .HasDatabaseName("IX_SetupZones_Country");

        builder.HasIndex(x => x.Zone)
            .HasDatabaseName("IX_SetupZones_Zone");
    }
}