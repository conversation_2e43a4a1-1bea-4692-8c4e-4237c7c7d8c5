using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class UpsImportServicesSaverConfiguration : IEntityTypeConfiguration<UpsImportServicesSaver>
{
    public void Configure(EntityTypeBuilder<UpsImportServicesSaver> builder)
    {
        builder.ToTable("ups_import_saver");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.UpsPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
            
        // Configuración de zonas opcionales
        builder.Property(u => u.Zone1);
        builder.Property(u => u.Zone2);
        builder.Property(u => u.Zone3);
        builder.Property(u => u.Zone4);
        builder.Property(u => u.Zone5);
        builder.Property(u => u.Zone6);
    }
}

public class UpsImportServicesExpeditedConfiguration : IEntityTypeConfiguration<UpsImportServicesExpedited>
{
    public void Configure(EntityTypeBuilder<UpsImportServicesExpedited> builder)
    {
        builder.ToTable("ups_import_expedited");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.UpsPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
            
        // Configuración de zonas opcionales
        builder.Property(u => u.Zone1);
        builder.Property(u => u.Zone2);
        builder.Property(u => u.Zone3);
        builder.Property(u => u.Zone4);
        builder.Property(u => u.Zone5);
        builder.Property(u => u.Zone6);
    }
}

public class UpsImportServicesExpressFreightConfiguration : IEntityTypeConfiguration<UpsImportServicesExpressFreight>
{
    public void Configure(EntityTypeBuilder<UpsImportServicesExpressFreight> builder)
    {
        builder.ToTable("ups_import_express_freight");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.UpsPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
            
        // Configuración de zonas opcionales
        builder.Property(u => u.Zone1);
        builder.Property(u => u.Zone2);
        builder.Property(u => u.Zone3);
        builder.Property(u => u.Zone4);
        builder.Property(u => u.Zone5);
        builder.Property(u => u.Zone6);
    }
}

public class UpsExportServicesSaverConfiguration : IEntityTypeConfiguration<UpsExportServicesSaver>
{
    public void Configure(EntityTypeBuilder<UpsExportServicesSaver> builder)
    {
        builder.ToTable("ups_export_saver");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.UpsPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
            
        // Configuración de zonas opcionales
        builder.Property(u => u.Zone1);
        builder.Property(u => u.Zone2);
        builder.Property(u => u.Zone3);
        builder.Property(u => u.Zone4);
        builder.Property(u => u.Zone5);
        builder.Property(u => u.Zone6);
    }
}

public class UpsExportServicesExpressConfiguration : IEntityTypeConfiguration<UpsExportServicesExpress>
{
    public void Configure(EntityTypeBuilder<UpsExportServicesExpress> builder)
    {
        builder.ToTable("ups_export_express");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.UpsPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
            
        // Configuración de zonas opcionales
        builder.Property(u => u.Zone1);
        builder.Property(u => u.Zone2);
        builder.Property(u => u.Zone3);
        builder.Property(u => u.Zone4);
        builder.Property(u => u.Zone5);
        builder.Property(u => u.Zone6);
    }
}

public class UpsExportServicesExpeditedConfiguration : IEntityTypeConfiguration<UpsExportServicesExpedited>
{
    public void Configure(EntityTypeBuilder<UpsExportServicesExpedited> builder)
    {
        builder.ToTable("ups_export_expedited");
        builder.HasKey(u => u.Id);
        
        // Relación con PackageType
        builder.HasOne(u => u.UpsPackageType)
            .WithMany()
            .HasForeignKey("PackageTypeId")
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.Property(u => u.Weight)
            .HasPrecision(10, 2);
            
        // Configuración de zonas opcionales
        builder.Property(u => u.Zone1);
        builder.Property(u => u.Zone2);
        builder.Property(u => u.Zone3);
        builder.Property(u => u.Zone4);
        builder.Property(u => u.Zone5);
        builder.Property(u => u.Zone6);
    }
}