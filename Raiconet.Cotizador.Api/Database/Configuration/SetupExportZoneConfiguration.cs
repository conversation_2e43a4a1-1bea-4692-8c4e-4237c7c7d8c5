using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class SetupExportZonesConfiguration:IEntityTypeConfiguration<SetupExportZones>
{
    public void Configure(EntityTypeBuilder<SetupExportZones> builder)
    {
        builder.ToTable("setup_export_zones");
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
            
        builder.HasKey(l => l.Id);
            
        builder.Property(l => l.Code)
            .HasMaxLength(10) // Ajusta el tamaño según sea necesario
            .IsRequired();
        builder.Property(l => l.Name)
            .HasMaxLength(255)
            .IsRequired();
        builder.Property(l => l.NameAlt)
            .HasMaxLength(255);
        builder.Property(l => l.QuotationsDocEconomy);
        builder.Property(l => l.QuotationsPackEconomy);
        builder.Property(l => l.QuotationsDocPriority);
        builder.Property(l => l.QuotationsDocPriority);

        // Si necesitas un índice único para Code
        builder.HasIndex(l => l.Code).IsUnique();
            
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}