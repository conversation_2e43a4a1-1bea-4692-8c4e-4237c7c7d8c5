using Raiconet.Cotizador.Api.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class RateTimeConfiguration : IEntityTypeConfiguration<RateTime>
{
    public void Configure(EntityTypeBuilder<RateTime> builder)
    {
        builder.ToTable("rate_time");
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
        
        builder.HasKey(r => r.Id);
        
        builder.Property(r => r.Zone).IsRequired();
        builder.Property(r => r.PriorityService)
            .HasMaxLength(255)
            .IsRequired();
        builder.Property(r => r.ExpressService)
            .HasMaxLength(255)
            .IsRequired();
        builder.Property(r => r.CreatedAt);
        builder.Property(r => r.DeletedAt);

        // Si necesitas un índice único para Zone
        builder.HasIndex(r => r.Zone).IsUnique();
        
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}