using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class UpsZonesExportConfiguration : IEntityTypeConfiguration<UpsZonesExport>
{
    public void Configure(EntityTypeBuilder<UpsZonesExport> builder)
    {
        builder.ToTable("ups_zones_export");
        builder.HasKey(u => u.Id);
        
        builder.HasIndex(u => new { u.Country, u.IataCode }).IsUnique();
        
        builder.Property(u => u.Country).HasMaxLength(50).IsRequired();
        builder.Property(u => u.IataCode).HasMaxLength(50).IsRequired();
        builder.Property(u => u.ExpressPlus);
        builder.Property(u => u.Express);
        builder.Property(u => u.FreightMidday);
        builder.Property(u => u.ExpressFreight);
        builder.Property(u => u.ExpressSaver);
        builder.Property(u => u.Expedited);
        

    }
}