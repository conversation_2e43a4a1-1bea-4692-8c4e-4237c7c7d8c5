using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class OtpConfiguration: IEntityTypeConfiguration<Otp>
{
    public void Configure(EntityTypeBuilder<Otp> builder)
    { 
        builder.ToTable("otp");
        builder.<PERSON><PERSON><PERSON>(o => o.Id);
        builder.Property(o => o.Id)
            .ValueGeneratedOnAdd();

        builder.Property(o => o.Code)
            .IsRequired()
            .HasMaxLength(6);

        builder.Property(o => o.Email)
            .IsRequired()
            .HasMaxLength(256);

        builder.Property(o => o.ExpiresAt)
            .IsRequired();

        builder.Property(o => o.CreatedAt)
            .IsRequired();

        builder.Property(o => o.VerificationAttempts)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(o => o.IsVerified)
            .IsRequired()
            .HasDefaultValue(false);

        builder.HasIndex(o => o.Email);

        builder.HasIndex(o => new { o.Email, o.ExpiresAt });
    }
}