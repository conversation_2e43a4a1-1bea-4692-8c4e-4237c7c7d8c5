using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class FedexPackageTypeConfiguration : IEntityTypeConfiguration<FedexPackageType>
{
    public void Configure(EntityTypeBuilder<FedexPackageType> builder)
    {
        builder.ToTable("fedex_package_types");
        builder.<PERSON><PERSON>ey(p => p.Id);
        
        builder.Property(p => p.Description)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(p => p.Code)
            .HasMaxLength(50);

        // Seed Data
        builder.HasData(
            new UpsPackageType { Id = 1, Description = "Sobre", Code = "ENVELOPE" },
            new UpsPackageType { Id = 2, Description = "Pak", Code = "PAK" },
            new UpsPackageType { Id = 3, Description = "Paquete", Code = "PACKAGE" },
            new UpsPackageType { Id = 4, Description = "Por kilogramo (Rango)", Code = "KG_RANGE" }
        );
    }
}