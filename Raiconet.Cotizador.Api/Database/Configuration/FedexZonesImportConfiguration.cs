using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class FedexZonesImportConfiguration : IEntityTypeConfiguration<FedexZonesImport>
{
    public void Configure(EntityTypeBuilder<FedexZonesImport> builder)
    {
        builder.ToTable("fedex_zones_import");
        builder.HasKey(f => f.Id);

        // Índice compuesto único para Country e Ipe
        builder.HasIndex(f => new { f.Country }).IsUnique();
        
        // Configuración de propiedades
        builder.Property(f => f.Country).HasMaxLength(255).IsRequired();
        builder.Property(x => x.IataCode)  
            .IsRequired()
            .HasMaxLength(100);
        builder.Property(f => f.Ip).HasMaxLength(10);
        builder.Property(f => f.Ie).HasMaxLength(10);
        builder.Property(f => f.Ipf).HasMaxLength(10);
        builder.Property(f => f.Ief).HasMaxLength(10);
        
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}