using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.Quotations;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class QuotationResultEntityConfiguration : IEntityTypeConfiguration<QuotationResult>
{
    public void Configure(EntityTypeBuilder<QuotationResult> builder)
    {
        builder.ToTable("quotation_result");
        builder.HasKey(q => q.Id);

        builder.Property(q => q.Id)
            .ValueGeneratedOnAdd()
            .IsRequired();

        builder.Property(q => q.QuotationNumber)
            .IsRequired();

        builder.Property(q => q.ServiceTypeId)
            .IsRequired();

        builder.Property(q => q.CreatedById)
            .IsRequired()
            .HasMaxLength(100);
        
        builder.Property(q => q.UpdatedBy)
            .IsRequired(false)
            .HasMaxLength(100);
        
        builder.Property(q => q.Notes)
            .IsRequired(false)
            .HasMaxLength(100);

        builder.Property(q => q.CountryOfOrigin)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(q => q.CustomerName)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(q => q.CreatedAt)
            .IsRequired();
        
        builder.Property(q => q.UpdatedAt)
            .IsRequired(false);

        // Configuración para los nuevos campos
        builder.Property(q => q.Description)
            .IsRequired(false)
            .HasMaxLength(500);

        builder.Property(q => q.Code)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(q => q.DutyPercentage)
            .IsRequired(false)
            .HasPrecision(18, 2);

        builder.Property(q => q.DutyPercentageSuggested)
            .IsRequired(false)
            .HasPrecision(18, 2);

        builder.Property(q => q.FobPercentage)
            .IsRequired(false)
            .HasPrecision(18, 2);

        // Configuración para el nuevo campo ApprovedByClient
        builder.Property(q => q.ApprovedByClient)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(q => q.QuotationPayload)
            .IsRequired(false)
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, new JsonSerializerOptions { WriteIndented = false }),
                v => JsonSerializer.Deserialize<TotalsByService>(v, new JsonSerializerOptions { WriteIndented = false })
                    !);
        
        builder.Property(q => q.CustomQuotationPayload)
            .IsRequired(false)
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, new JsonSerializerOptions { WriteIndented = false }),
                v => JsonSerializer.Deserialize<Dictionary<string,string>>(v, new JsonSerializerOptions { WriteIndented = false })
                    !);

        builder.Property(q => q.Packages)
            .IsRequired()
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonSerializer.Serialize(v, new JsonSerializerOptions { WriteIndented = false }),
                v => JsonSerializer.Deserialize<List<Package>>(v, new JsonSerializerOptions { WriteIndented = false }));

        builder.Property(q => q.Status)
            .IsRequired();
        
        // Relación con el usuario creador
        builder.HasOne(q => q.Creator)
            .WithMany(u => u.CreatedQuotations)
            .HasForeignKey(q => q.CreatedById)
            .OnDelete(DeleteBehavior.Restrict); // Opcional: especificar comportamiento al eliminar

        // Nueva relación con el usuario que actualizó
        builder.HasOne(q => q.UpdatedByUser)
            .WithMany() // Sin especificar colección en User, ya que puede no ser necesaria
            .HasForeignKey(q => q.UpdatedBy)
            .OnDelete(DeleteBehavior.SetNull); // Si se elimina el usuario, establecer null

        builder.HasOne(q => q.RejectionReason);

        builder.HasIndex(q => q.QuotationNumber);
        builder.HasIndex(q => q.Code);
        
        // Opcional: Añadir índice para UpdatedBy para mejorar rendimiento en las consultas
        builder.HasIndex(q => q.UpdatedBy);
    }
}