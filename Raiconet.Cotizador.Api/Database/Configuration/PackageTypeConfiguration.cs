using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

public class PackageTypeConfiguration : IEntityTypeConfiguration<UpsPackageType>
{
    public void Configure(EntityTypeBuilder<UpsPackageType> builder)
    {
        builder.ToTable("ups_package_types");
        builder.HasKey(p => p.Id);
        
        builder.Property(p => p.Description)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(p => p.Code)
            .HasMaxLength(50);

        // Seed Data
        builder.HasData(
            new UpsPackageType { Id = 1, Description = "DOCUMENTO (OTRO SOBRE)", Code = "DOC_SOBRE" },
            new UpsPackageType { Id = 2, Description = "PAQUETE (NO DOCUMENTO)", Code = "PAQUETE" },
            new UpsPackageType { Id = 3, Description = "PRECIO POR KILO", Code = "POR_KILO" },
            new UpsPackageType { Id = 4, Description = "SOBRE (CARTÓN)", Code = "SOBRE_CARTON" },
            new UpsPackageType { Id = 5, Description = "TARIFA MINIMA", Code = "TARIFA_MIN" }
        );
    }
}