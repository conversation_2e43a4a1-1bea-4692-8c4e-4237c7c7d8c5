using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class UserProfileEntityTypeConfiguration : IEntityTypeConfiguration<UserProfile>
{
    public void Configure(EntityTypeBuilder<UserProfile> builder)
    {
        builder.ToTable("user_profile");

        builder.HasKey(up => up.Id);
        builder.Property(o => o.Id).ValueGeneratedOnAdd();

        builder.Property(up => up.FirstName).IsRequired().HasMaxLength(100);
        builder.Property(up => up.LastName).IsRequired().HasMaxLength(100);

        builder.OwnsOne(up => up.Address, address =>
        {
            address.Property(p => p.City).IsRequired().HasMaxLength(100);
            address.Property(p => p.Country).IsRequired().HasMaxLength(100);
            address.Property(p => p.AddressLine1).IsRequired().HasMaxLength(500);
            address.Property(p => p.AddressLine2).HasMaxLength(500);
            address.Property(p => p.PostalCode).IsRequired().HasMaxLength(100);
        });
        
        builder.HasOne(up => up.User)
            .WithOne(u => u.UserProfile)
            .HasForeignKey<UserProfile>(up => up.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}