using Raiconet.Cotizador.Api.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class UpsZonesImportConfiguration : IEntityTypeConfiguration<UpsZonesImport>
{
    public void Configure(EntityTypeBuilder<UpsZonesImport> builder)
    {
        builder.ToTable("ups_zones_import");
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
        
        
        builder.HasKey(u => u.Id);

        builder.HasIndex(u => new { u.Country, u.IataCode }).IsUnique();
        
        builder.Property(u => u.Country).HasMaxLength(255).IsRequired();
        builder.Property(u => u.IataCode).HasMaxLength(255).IsRequired();
        builder.Property(u => u.Express);
        builder.Property(u => u.ExpressFreight);
        builder.Property(u => u.ExpressSaver);
        builder.Property(u => u.Expedited);
        
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}