using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class SetupImportZonesConfiguration:IEntityTypeConfiguration<SetupImportZones>
{
    public void Configure(EntityTypeBuilder<SetupImportZones> builder)
    {
        builder.ToTable("setup_import_zones");
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
            
        builder.HasKey(l => l.Id);
            
        builder.Property(l => l.Code)
            .HasMaxLength(10) // Ajusta el tamaño según sea necesario
            .IsRequired();
        builder.Property(l => l.Name)
            .HasMaxLength(255)
            .IsRequired();
        builder.Property(l => l.NameAlt)
            .HasMaxLength(255);
        builder.Property(l => l.QuotationsCd);
        builder.Property(l => l.QuotationsWarehouse);
        builder.Property(l => l.QuotationsCc);
        builder.Property(l => l.QuotationsEcommerce);

        // Si necesitas un índice único para Code
        builder.HasIndex(l => l.Code).IsUnique();
            
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}