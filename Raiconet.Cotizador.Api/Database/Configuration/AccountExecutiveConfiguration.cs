using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class AccountExecutiveConfiguration : IEntityTypeConfiguration<AccountExecutive>
{
    public void Configure(EntityTypeBuilder<AccountExecutive> builder)
    {
        builder.ToTable("account_executives");
       
        builder.<PERSON><PERSON><PERSON>(x => x.Id);
       
        builder.Property(x => x.Name)
            .HasMaxLength(100)
            .IsRequired();
           
        builder.Property(x => x.Email)
            .HasMaxLength(100)
            .IsRequired();

        builder.HasOne(x => x.Type)
            .WithMany(x => x.AccountExecutives)
            .HasForeignKey(x => x.AccountExecutiveTypeId);
    }
}