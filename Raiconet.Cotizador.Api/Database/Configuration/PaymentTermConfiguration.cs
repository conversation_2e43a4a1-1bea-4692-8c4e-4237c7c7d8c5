using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class PaymentTermConfiguration : IEntityTypeConfiguration<PaymentTerm>
{
    public void Configure(EntityTypeBuilder<PaymentTerm> builder)
    {
        builder.ToTable("payment_terms");
       
        builder.HasKey(x => x.Id);
       
        builder.Property(x => x.Name)
            .HasMaxLength(50)
            .IsRequired();
           
        builder.Property(x => x.Days)
            .IsRequired();
    }
}