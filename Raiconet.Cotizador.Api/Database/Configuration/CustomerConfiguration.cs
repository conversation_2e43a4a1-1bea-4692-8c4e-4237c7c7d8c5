using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class CustomerConfiguration : IEntityTypeConfiguration<Customer>
{
    public void Configure(EntityTypeBuilder<Customer> builder)
    {
        builder.ToTable("customers");
       
        builder.HasKey(x => x.Id);

        // Propiedades simples
        builder.Property(x => x.TaxId)
            .HasMaxLength(20)
            .IsRequired();
           
        builder.Property(x => x.BusinessName)
            .HasMaxLength(100)
            .IsRequired();
        
        builder.Property(x => x.CustomerCode);
           
        builder.Property(x => x.Email)
            .HasMaxLength(100)
            .IsRequired();
           
        builder.Property(x => x.TwoFactorEnabled)
            .IsRequired(false);

        builder.Property(x => x.UserId)
            .IsRequired(false);

        // Relaciones uno a muchos
        builder.HasOne(x => x.User)
            .WithMany()
            .HasForeignKey(x => x.UserId)
            .IsRequired(false);

        builder.HasOne(x => x.AccountExecutiveUser)
            .WithMany()
            .HasForeignKey(x => x.AccountExecutiveUserId)
            .IsRequired(false);

        builder.HasOne(x => x.AccountExternalExecutive)
            .WithMany(x => x.Customers)
            .HasForeignKey(x => x.AccountExternalExecutiveId)
            .IsRequired(false);
           
        builder.HasOne(x => x.Type)
            .WithMany(x => x.Customers)
            .HasForeignKey(x => x.CustomerTypeId)
            .IsRequired();

        builder.Property(x => x.PaymentTerm);

        // Colecciones
        builder.HasMany(x => x.CustomerBillingReason)
            .WithOne(x => x.Customer)
            .HasForeignKey(x => x.CustomerId);
           
        builder.HasMany(x => x.Addresses)
            .WithOne(x => x.Customer)
            .HasForeignKey(x => x.CustomerId);

        builder.HasMany(x => x.Files)
            .WithOne(x => x.Customer)
            .HasForeignKey(x => x.CustomerId);
    }
}