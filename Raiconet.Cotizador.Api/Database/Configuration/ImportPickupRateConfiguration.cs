using Raiconet.Cotizador.Api.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class ImportPickupRateConfiguration : IEntityTypeConfiguration<ImportPickupRate>
{
    public void Configure(EntityTypeBuilder<ImportPickupRate> builder)
    {
        builder.ToTable("import_pickup_rates");
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
        
        builder.HasKey(r => r.Id);
        
        builder.Property(r => r.Zone)
            .HasMaxLength(50)
            .IsRequired();
            
        builder.Property(r => r.Weight)
            .IsRequired();
            
        builder.Property(r => r.Price)
            .IsRequired();
        
        // Opcional: Agregar un índice compuesto por Zone y Weight para búsquedas eficientes
        builder.HasIndex(r => new { r.Zone, r.Weight });
        
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}