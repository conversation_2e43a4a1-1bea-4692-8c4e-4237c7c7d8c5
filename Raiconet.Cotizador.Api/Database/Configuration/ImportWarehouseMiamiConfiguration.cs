using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class ImportWarehouseMiamiConfiguration : IEntityTypeConfiguration<ImportWarehouseMiami>
{
    public void Configure(EntityTypeBuilder<ImportWarehouseMiami> builder)
    {
        builder.ToTable("import_warehouse_miami");
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
        
        builder.HasKey(t => t.Id);
        
        builder.Property(t => t.Weight)
            .HasColumnType("decimal(18,2)")
            .IsRequired();
            
        builder.Property(t => t.Price)
            .HasColumnType("decimal(18,2)")
            .IsRequired();
            
        builder.Property(t => t.Cost)
            .HasColumnType("decimal(18,2)")
            .IsRequired();
        
        // Opcional: Agregar un índice en Weight si se busca frecuentemente por este campo
        builder.HasIndex(t => t.Weight);
        
        builder.UsePropertyAccessMode(PropertyAccessMode.Field);
    }
}