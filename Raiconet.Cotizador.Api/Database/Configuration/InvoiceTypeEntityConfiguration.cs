using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class InvoiceTypeEntityConfiguration: IEntityTypeConfiguration<InvoiceType>
{
    public void Configure(EntityTypeBuilder<InvoiceType> builder)
    {
        builder.HasKey(x => x.Id);
       
        builder.Property(x => x.Name)
            .HasMaxLength(50)
            .IsRequired();
    }
}