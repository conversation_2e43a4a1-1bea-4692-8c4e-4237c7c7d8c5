using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database.Configuration;

public class AdditionalServiceConfiguration : IEntityTypeConfiguration<AdditionalService>
{
    public void Configure(EntityTypeBuilder<AdditionalService> builder)
    {
        builder.ToTable("additional_services");
        
        builder.HasKey(x => x.Id);
        
        builder.Property(x => x.Code)
            .HasMaxLength(10)
            .IsRequired();
            
        builder.Property(x => x.Description)
            .HasMaxLength(100)
            .IsRequired();
            
        builder.Property(x => x.Type)
            .HasMaxLength(50)
            .IsRequired();
            
        builder.Property(x => x.MinimumValue)
            .HasPrecision(10, 2);
            
        builder.Property(x => x.UpsValue)
            .HasPrecision(10, 2)
            .IsRequired();
            
        builder.Property(x => x.DhlValue)
            .HasPrecision(10, 2)
            .IsRequired();
            
        builder.Property(x => x.FedexValue)
            .HasPrecision(10, 2)
            .IsRequired();
            
        // Índice único para el código
        builder.HasIndex(x => x.Code).IsUnique();
    }
}