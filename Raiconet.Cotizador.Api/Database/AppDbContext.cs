using System.Reflection;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Raiconet.Cotizador.Api.Domain;

namespace Raiconet.Cotizador.Api.Database;

public class AppDbContext :  IdentityDbContext<User, IdentityRole, string>
{
    // Store the options builder action to be used in OnConfiguring
    private readonly Action<DbContextOptionsBuilder>? _optionsBuilderAction;
    
    public DbSet<Otp> Otps { get; init; }
    public DbSet<UserProfile> UserProfiles { get; init; }
    
    public DbSet<UpsZonesImport> UpsZonesImports { get; init; }
    public DbSet<UpsZonesExport> UpsZonesExports { get; init; }
    
    public DbSet<FedexZonesImport> FedexZonesImports { get; init; }
    public DbSet<FedexZonesExport> FedexZonesExports { get; init; }
    
    
    public DbSet<UpsImportServicesSaver> UpsImportServicesSavers { get; init; }
    public DbSet<UpsImportServicesExpedited> UpsImportServicesExpediteds { get; init; }
    public DbSet<UpsImportServicesExpressFreight> UpsImportServicesExpressFreights { get; init; }
    public DbSet<UpsExportServicesSaver>  UpsExportServicesSavers { get; init; }
    public DbSet<UpsExportServicesExpress> UpsExportServicesExpresses  { get; init; }
    public DbSet<UpsExportServicesExpedited> UpsExportServicesExpediteds { get; init; }
    
    public DbSet<FedexImportServiceIe> FedexImportServiceIes{ get; init; }
    public DbSet<FedexImportServiceIef>  FedexImportServiceIefs { get; init; }
    public DbSet<FedexImportServiceIp>  FedexImportServiceIps{ get; init; }
    public DbSet<FedexImportServiceIpf>  FedexImportServiceIpfs{ get; init; }
    public DbSet<FedexExportServiceIef> FedexExportServiceIefs { get; init; }
    public DbSet<FedexExportServiceIp> FedexExportServiceIps { get; init; }
    public DbSet<FedexExportServicesIe> FedexExportServicesIes { get; init; }
    public DbSet<FedexExportServiceIpf> FedexExportServiceIpfs { get; init; }
    public DbSet<FedexExportServicesIpe> FedexExportServicesIpes { get; init; }
    
    //setup
    public DbSet<RateTime> RateTimes { get; init; }
    public DbSet<AdditionalService> AdditionalServices { get; init; }
    public DbSet<BillingReasons> BillingReasons { get; init; }
    public DbSet<BillingConcepts> BillingConcepts { get; init; }
    public DbSet<SetupZones> SetupZones { get; init; }
    public DbSet<SetupImportZones> SetupImportZones { get; init; }
    public DbSet<SetupExportZones> SetupExportZones { get; init; }
    public DbSet<SetupRaicoService> SetupRaicoServices { get; init; }
    public DbSet<SetupPackageType> SetupPackageTypes { get; init; }
    public DbSet<TvhImportCollect> TvhImportCollects { get; init; }
    public DbSet<TvhImportPrepaid> TvhImportPrepaids { get; init; }
    public DbSet<ImportWarehouseChina> ImportWarehouseChinas { get; init; }
    public DbSet<ImportWarehouseMadrid> ImportWarehouseMadrids { get; init; }
    public DbSet<ImportWarehouseMiami> ImportWarehouseMiamis { get; init; }
    public DbSet<ImportZonesPickUp> ImportZonesPickUps { get; init; }
    public DbSet<ImportPickupRate> ImportPickupRates { get; init; }
    public DbSet<RejectionReason> RejectionReasons { get; init; }
    
    //Customer
    
    public DbSet<Customer> Customers { get; init; }
    public DbSet<AccountExecutive> AccountExecutives { get; init; }
    public DbSet<CustomerType> CustomerType { get; init; }
    public DbSet<PaymentTerm> PaymentTerms { get; init; }
    public DbSet<Address> Addresses { get; init; }
    public DbSet<CustomerBillingReason> CustomerBillingReason { get; init; }
    public DbSet<CustomerFile> CustomerFiles { get; init; }
    public DbSet<InvoiceType> InvoiceTypes { get; init; }
    public DbSet<QuotationResult> QuotationResults { get; init; }
    public DbSet<Tenant> Tenants { get; init; }
    public DbSet<CustomerServiceRequest> CustomerServiceRequests { get; init; }
    public DbSet<LeadQuotation> LeadQuotations { get; init; }

    // Constructor used by dependency injection
    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
    {
    }

    // Constructor that takes a builder action
    public AppDbContext(Action<DbContextOptionsBuilder> optionsBuilderAction)
    {
        _optionsBuilderAction = optionsBuilderAction;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        _optionsBuilderAction?.Invoke(optionsBuilder);
        base.OnConfiguring(optionsBuilder);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }
}

public class AppDbContextFactory : IDesignTimeDbContextFactory<AppDbContext>
{
    public AppDbContext CreateDbContext(string[] args)
    {
        string environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") 
                                 ?? "Development";
        
        IConfigurationRoot configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile($"appsettings.{environmentName}.json", optional: true)
            .AddEnvironmentVariables()
            .AddUserSecrets(Assembly.GetAssembly(typeof(Program))!)
            .Build();

        return new AppDbContext(builder =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            builder.UseNpgsql(connectionString);
            builder.UseSnakeCaseNamingConvention();
        });
    }
}