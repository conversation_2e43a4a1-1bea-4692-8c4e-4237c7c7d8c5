using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Raiconet.Cotizador.Api.Database;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Features.Account;
using Raiconet.Cotizador.Api.Features.Courriers;
using Raiconet.Cotizador.Api.Features.Customer;
using Raiconet.Cotizador.Api.Features.CustomerServiceRequests;
using Raiconet.Cotizador.Api.Features.CustomerServices;
using Raiconet.Cotizador.Api.Features.Quotations;
using Raiconet.Cotizador.Api.Features.Setup;
using Raiconet.Cotizador.Api.Middlewares;
using Raiconet.Cotizador.Api.Features.RejectionReasons;
using SharpGrip.FluentValidation.AutoValidation.Endpoints.Extensions;

namespace Raiconet.Cotizador.Api.Configuration;

public static class ApiEndpointExtensions
{
    private const string CorsPolicy = "RaiconetCorsPolicy";

    public static IServiceCollection AddRaiconetEndpoints(
        this IServiceCollection services,
        IConfiguration configuration,
        IWebHostEnvironment environment
    )
    {
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "Raiconet Cotizador API",
                Version = "v1",
                Description = "API for Raiconet Cotizador"
            });

            options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Name = "Authorization",
                Type = SecuritySchemeType.Http,
                Scheme = "Bearer",
                BearerFormat = "JWT",
                In = ParameterLocation.Header,
                Description = "Enter your JWT token."
            });

            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        }
                    },
                    Array.Empty<string>()
                }
            });
        });
        services.AddHttpContextAccessor();
        services.AddFluentValidationAutoValidation();
        services.AddAuthorization();

        var allowedOrigins = configuration.GetSection("Cors:AllowedOrigins")
            .Get<string[]>() ?? Array.Empty<string>();

        services.ConfigureHttpJsonOptions(options =>
        {
            options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            options.SerializerOptions.PropertyNameCaseInsensitive = true;
        });

        services.AddCors(options =>
        {
            options.AddPolicy(name: CorsPolicy,
                policy =>
                {
                    policy.AllowAnyOrigin()
                        .AllowAnyMethod()
                        .AllowAnyHeader();
                });
        });

        services.Configure<RouteOptions>(options => { options.LowercaseUrls = true; });

        services.AddIdentityApiEndpoints<User>(options => { options.SignIn.RequireConfirmedEmail = true; })
            .AddRoles<IdentityRole>()
            .AddEntityFrameworkStores<AppDbContext>()
            .AddErrorDescriber<CustomIdentityErrorDescriber>();

        services.ConfigureApplicationCookie(options => { options.Cookie.Name = "Raiconet.Cotizador.Api"; });

        return services;
    }

    public static void UseRaiconetEndpoints(this WebApplication app)
    {
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseAuthentication();
        app.UseAuthorization();
        app.UseJsonExceptionHandler();
        app.UseCors(CorsPolicy);

        app.UseAccountEndpoints();
        app.GetCourrierEndpoints();
        app.RaicoSetupEndpoints();
        app.GetCustomerEndpoints();
        app.AddQuotationEndpoints();
        app.RaicoServicesEndpoints();
        app.MapCustomerServiceRequestEndpoints();
        app.MapRejectionReasonEndpoints();

    }

    public static IApplicationBuilder UseJsonExceptionHandler(
        this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<JsonExceptionHandlerMiddleware>();
    }
}