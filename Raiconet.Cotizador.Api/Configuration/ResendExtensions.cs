using Resend;

namespace Raiconet.Cotizador.Api.Configuration;

public class ResendConfiguration
{
    public required string ApiKey { get; set; }
}

public static class ResendExtensions
{
    public static IServiceCollection AddResend(this IServiceCollection services, IConfiguration configuration)
    {
        var resendConfiguration = configuration.GetSection("Resend").Get<ResendConfiguration>() ??
                                  throw new InvalidOperationException("Missing configuration section");
        
        services.AddHttpClient<ResendClient>();
        services.Configure<ResendClientOptions>(o => { o.ApiToken = resendConfiguration.ApiKey; });
        services.AddTransient<IResend, ResendClient>();

        return services;
    }
}