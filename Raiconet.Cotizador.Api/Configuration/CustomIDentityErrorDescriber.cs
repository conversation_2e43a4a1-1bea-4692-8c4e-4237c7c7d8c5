using Microsoft.AspNetCore.Identity;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using static Raiconet.Cotizador.Api.Resources.EndpointResources;

namespace Raiconet.Cotizador.Api.Configuration;

public class CustomIdentityErrorDescriber(ICultureProvider cultureProvider) : IdentityErrorDescriber
{
    public override IdentityError DefaultError()
    {
        return new IdentityError
        {
            Code = nameof(DefaultError),
            Description = ResourceManager.GetString("DefaultError",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError ConcurrencyFailure()
    {
        return new IdentityError
        {
            Code = nameof(ConcurrencyFailure),
            Description = ResourceManager.GetString("ConcurrencyFailure",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError PasswordMismatch()
    {
        return new IdentityError
        {
            Code = nameof(PasswordMismatch),
            Description = ResourceManager.GetString("PasswordMismatch",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError InvalidToken()
    {
        return new IdentityError
        {
            Code = nameof(InvalidToken),
            Description = ResourceManager.GetString("InvalidToken",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError RecoveryCodeRedemptionFailed()
    {
        return new IdentityError
        {
            Code = nameof(RecoveryCodeRedemptionFailed),
            Description = ResourceManager.GetString("RecoveryCodeRedemptionFailed",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError LoginAlreadyAssociated()
    {
        return new IdentityError
        {
            Code = nameof(LoginAlreadyAssociated),
            Description = ResourceManager.GetString("LoginAlreadyAssociated",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError InvalidUserName(string? userName)
    {
        return new IdentityError
        {
            Code = nameof(InvalidUserName),
            Description = string.Format(
                ResourceManager.GetString("InvalidUserName",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                userName)
        };
    }

    public override IdentityError InvalidEmail(string? email)
    {
        return new IdentityError
        {
            Code = nameof(InvalidEmail),
            Description = string.Format(
                ResourceManager.GetString("InvalidEmail",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                email)
        };
    }

    public override IdentityError DuplicateUserName(string userName)
    {
        return new IdentityError
        {
            Code = nameof(DuplicateUserName),
            Description = string.Format(
                ResourceManager.GetString("DuplicateUserName",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                userName)
        };
    }

    public override IdentityError DuplicateEmail(string email)
    {
        return new IdentityError
        {
            Code = nameof(DuplicateEmail),
            Description = string.Format(
                ResourceManager.GetString("DuplicateEmail",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                email)
        };
    }

    public override IdentityError InvalidRoleName(string? role)
    {
        return new IdentityError
        {
            Code = nameof(InvalidRoleName),
            Description = string.Format(
                ResourceManager.GetString("InvalidRoleName",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                role)
        };
    }

    public override IdentityError DuplicateRoleName(string role)
    {
        return new IdentityError
        {
            Code = nameof(DuplicateRoleName),
            Description = string.Format(
                ResourceManager.GetString("DuplicateRoleName",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                role)
        };
    }

    public override IdentityError UserAlreadyHasPassword()
    {
        return new IdentityError
        {
            Code = nameof(UserAlreadyHasPassword),
            Description = ResourceManager.GetString("UserAlreadyHasPassword",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError UserLockoutNotEnabled()
    {
        return new IdentityError
        {
            Code = nameof(UserLockoutNotEnabled),
            Description = ResourceManager.GetString("UserLockoutNotEnabled",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError UserAlreadyInRole(string role)
    {
        return new IdentityError
        {
            Code = nameof(UserAlreadyInRole),
            Description = string.Format(
                ResourceManager.GetString("UserAlreadyInRole",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                role)
        };
    }

    public override IdentityError UserNotInRole(string role)
    {
        return new IdentityError
        {
            Code = nameof(UserNotInRole),
            Description = string.Format(
                ResourceManager.GetString("UserNotInRole",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                role)
        };
    }

    public override IdentityError PasswordTooShort(int length)
    {
        return new IdentityError
        {
            Code = nameof(PasswordTooShort),
            Description = string.Format(
                ResourceManager.GetString("PasswordTooShort",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                length)
        };
    }

    public override IdentityError PasswordRequiresUniqueChars(int uniqueChars)
    {
        return new IdentityError
        {
            Code = nameof(PasswordRequiresUniqueChars),
            Description = string.Format(
                ResourceManager.GetString("PasswordRequiresUniqueChars",
                    cultureProvider.GetCurrentCulture()) ?? string.Empty,
                uniqueChars)
        };
    }

    public override IdentityError PasswordRequiresNonAlphanumeric()
    {
        return new IdentityError
        {
            Code = nameof(PasswordRequiresNonAlphanumeric),
            Description = ResourceManager.GetString("PasswordRequiresNonAlphanumeric",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError PasswordRequiresDigit()
    {
        return new IdentityError
        {
            Code = nameof(PasswordRequiresDigit),
            Description = ResourceManager.GetString("PasswordRequiresDigit",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError PasswordRequiresLower()
    {
        return new IdentityError
        {
            Code = nameof(PasswordRequiresLower),
            Description = ResourceManager.GetString("PasswordRequiresLower",
                cultureProvider.GetCurrentCulture())!
        };
    }

    public override IdentityError PasswordRequiresUpper()
    {
        return new IdentityError
        {
            Code = nameof(PasswordRequiresUpper),
            Description = ResourceManager.GetString("PasswordRequiresUpper",
                cultureProvider.GetCurrentCulture())!
        };
    }
}