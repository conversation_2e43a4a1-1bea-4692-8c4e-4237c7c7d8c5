using Microsoft.EntityFrameworkCore;
using Npgsql;
using Raiconet.Cotizador.Api.Database;

namespace Raiconet.Cotizador.Api.Configuration;

public static class DatabaseExtensions
{
    public static IServiceCollection AddRaiconetDatabase(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        var dataSourceBuilder = new NpgsqlDataSourceBuilder(
            configuration.GetConnectionString("DefaultConnection"));
        dataSourceBuilder.EnableDynamicJson();
        var database = dataSourceBuilder.Build();

        services.AddDbContext<AppDbContext>((_, options) =>
        {
            options.UseNpgsql(database);
            options.UseSnakeCaseNamingConvention();
        });

        return services;
    }

    public static async Task ApplyMigrations(this WebApplication app)
    {
        if (!app.Environment.IsDevelopment())
        {
            await using var scope = app.Services.CreateAsyncScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            await context.Database.MigrateAsync();
        }
    }
}