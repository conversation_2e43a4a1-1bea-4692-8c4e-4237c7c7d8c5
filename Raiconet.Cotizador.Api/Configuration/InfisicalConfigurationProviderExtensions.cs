using InfisicalConfiguration;

namespace Raiconet.Cotizador.Api.Configuration;

public class InfisicalConfiguration
{
    public required string ProjectId { get; init; }
    public required string Env { get; init; }
    public required string InfisicalUrl { get; init; }
    public required string ClientId { get; init; }
    public required string ClientSecret { get; init; }
}

public static class InfisicalConfigurationProviderExtensions
{
    public static void AddInfisicalConfigurationProvider(this WebApplicationBuilder builder)
    {
        if (builder.Environment.IsDevelopment()) return;

        var infisical = builder.Configuration.GetSection("Infisical").Get<InfisicalConfiguration>();
        if (infisical == null)
        {
            throw new ApplicationException("Environment is not development, provide infisical configuration");
        }

        builder.Configuration
            .SetBasePath(builder.Environment.ContentRootPath)
            .AddInfisical(
                new InfisicalConfigBuilder()
                    .SetProjectId(infisical.ProjectId)
                    .SetEnvironment(infisical.Env)
                    .SetInfisicalUrl(infisical.InfisicalUrl)
                    .SetAuth(
                        new InfisicalAuthBuilder()
                            .SetUniversalAuth(
                                clientId: infisical.ClientId,
                                clientSecret: infisical.ClientSecret
                            )
                            .Build()
                    )
                    .Build()
            )
            .Build();
    }
}