using HandlebarsDotNet;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Raiconet.Cotizador.Api.Domain;
using Raiconet.Cotizador.Api.Services.CultureProvider;
using Raiconet.Cotizador.Api.Services.CustomerService;
using Raiconet.Cotizador.Api.Services.MailSender;
using Raiconet.Cotizador.Api.Services.Quotation;
using Raiconet.Cotizador.Api.Services.Quotations;
using Raiconet.Cotizador.Api.Services.SessionManager;
using Raiconet.Cotizador.Api.Services.TemplateRender;
using Raiconet.Cotizador.Api.Services.RejectionReasonService;

namespace Raiconet.Cotizador.Api.Configuration;

public static class ServicesExtension
{
    public static IServiceCollection AddRaiconetServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<AppSettings>(configuration.GetSection(AppSettings.SectionName));
        
        services.AddSingleton<ITemplateCache, TemplateCache>();
        services.AddSingleton<IHandlebars>(_ => RaiconetHandlerbars.CreateHandlebars());
        services.AddSingleton(sp => sp.GetRequiredService<IOptions<AppSettings>>().Value);

        services.AddScoped<ISessionManager, SessionManager>();
        
        services.AddTransient<ICultureProvider, UserPreferenceCultureProvider>();
        services.AddTransient<ITemplateRender, HandlebarsTemplateRender>();
        services.AddTransient<IEmailSender, ResendMailSender>();
        services.AddScoped<IQuotationCalculator, QuotationCalculator>();
        services.AddScoped<IQuotationCalculatorExport, QuotationCalculatorExport>();
        services.AddScoped<IRicQuotationCalculator, RicQuotationCalculator>();
        services.AddScoped<ICustomerServiceRequestService, CustomerServiceRequestService>();
        services.AddScoped<IRejectionReasonService, RejectionReasonService>();

        return services;
    }
}

public static class RaiconetHandlerbars
{
    public static IHandlebars CreateHandlebars()
    {
        var handlebars = Handlebars.Create(new HandlebarsConfiguration
        {
            ThrowOnUnresolvedBindingExpression = false,
            NoEscape = false
        });

        return handlebars;
    }
}